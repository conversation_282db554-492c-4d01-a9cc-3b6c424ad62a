#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@File    : setup.py
<AUTHOR> XExtract Team
@Date    : 2023-05-08
@Desc    : Setup script for XExtract
"""

from setuptools import setup, find_packages

setup(
    name="xextract",
    version="0.1.0",
    description="Document Extraction Engine",
    author="XExtract Team",
    author_email="<EMAIL>",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    entry_points={
        "console_scripts": [
            "xextract=main:main",
        ],
    },
    python_requires=">=3.9",
    install_requires=[
        "fastapi>=0.68.0",
        "uvicorn>=0.15.0",
        "httpx>=0.23.0",
        "pydantic>=1.8.2",
        "pyyaml>=6.0",
    ],
    extras_require={
        "dev": [
            "pytest>=6.0.0",
            "pytest-asyncio>=0.15.0",
            "black>=21.5b2",
            "isort>=5.9.1",
            "mypy>=0.812",
        ],
    },
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
)
