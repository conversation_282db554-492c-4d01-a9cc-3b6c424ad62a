#!/usr/bin/env python3
"""
测试优化后的流水线架构
验证 "Read Once, Process in Memory, Write at End" 模式
"""

import asyncio
import json
import logging
import time
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_pipeline_basic():
    """测试基本的流水线功能"""
    try:
        from src.xextract.engine import PipelineExtractorDriver
        
        logger.info("=== 测试流水线基本功能 ===")
        
        # 创建流水线驱动器
        driver = PipelineExtractorDriver(
            parse_workers=1,
            classify_workers=1, 
            extract_workers=1,
            queue_size=10
        )
        
        logger.info("✓ PipelineExtractorDriver 创建成功")
        
        # 使用上下文管理器启动流水线
        with driver:
            logger.info("✓ 流水线启动成功")
            
            # 检查测试文件
            test_file = Path("tests/data/test.pdf")
            if not test_file.exists():
                logger.warning(f"测试文件不存在: {test_file}")
                return False
                
            logger.info(f"开始处理文件: {test_file}")
            
            # 提交文件到流水线
            start_time = time.time()
            result = driver.extract_from_file_sync(str(test_file), timeout=60.0)
            processing_time = time.time() - start_time
            
            logger.info(f"✓ 文件处理完成，耗时: {processing_time:.2f}秒")
            
            # 检查结果
            if result:
                logger.info("✓ 获得提取结果")
                logger.info(f"任务ID: {result.get('task_id', 'N/A')}")
                logger.info(f"文档类型: {result.get('document_type', 'N/A')}")
                logger.info(f"Schema: {result.get('schema_name', 'N/A')}")
                logger.info(f"状态: {result.get('status', 'N/A')}")
                
                # 显示处理时间
                processing_times = result.get('processing_times', {})
                if processing_times:
                    logger.info("各阶段处理时间:")
                    for stage, duration in processing_times.items():
                        logger.info(f"  {stage}: {duration:.2f}秒")
                        
                total_time = result.get('total_processing_time', 0)
                logger.info(f"总处理时间: {total_time:.2f}秒")
                
                # 保存结果到文件 (Write at End)
                output_file = Path("output") / f"pipeline_result_{result.get('task_id', 'unknown')}.json"
                output_file.parent.mkdir(exist_ok=True)
                
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=2)
                    
                logger.info(f"✓ 结果已保存到: {output_file}")
                
                return True
            else:
                logger.error("✗ 未获得提取结果")
                return False
                
    except Exception as e:
        logger.error(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_pipeline_batch():
    """测试批量处理功能"""
    try:
        from src.xextract.engine import PipelineExtractorDriver
        
        logger.info("=== 测试流水线批量处理 ===")
        
        # 创建流水线驱动器 (更多工作者)
        driver = PipelineExtractorDriver(
            parse_workers=2,
            classify_workers=1,
            extract_workers=2,
            queue_size=20
        )
        
        with driver:
            # 检查测试目录
            test_dir = Path("tests/data")
            if not test_dir.exists():
                logger.warning(f"测试目录不存在: {test_dir}")
                return False
                
            logger.info(f"开始批量处理目录: {test_dir}")
            
            # 批量处理
            start_time = time.time()
            results = asyncio.run(driver.extract_from_directory(
                str(test_dir), 
                file_pattern="*.pdf",
                max_concurrent=3,
                timeout_per_file=60.0
            ))
            processing_time = time.time() - start_time
            
            logger.info(f"✓ 批量处理完成，耗时: {processing_time:.2f}秒")
            logger.info(f"处理文件数量: {len(results)}")
            
            # 统计结果
            success_count = sum(1 for r in results if r.get('status') == 'success')
            error_count = len(results) - success_count
            
            logger.info(f"成功: {success_count}, 失败: {error_count}")
            
            # 保存批量结果
            if results:
                output_file = Path("output") / f"pipeline_batch_results_{int(time.time())}.json"
                output_file.parent.mkdir(exist_ok=True)
                
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(results, f, ensure_ascii=False, indent=2)
                    
                logger.info(f"✓ 批量结果已保存到: {output_file}")
                
            return True
            
    except Exception as e:
        logger.error(f"✗ 批量测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_pipeline_stats():
    """测试流水线统计功能"""
    try:
        from src.xextract.engine import PipelineExtractorDriver
        
        logger.info("=== 测试流水线统计功能 ===")
        
        driver = PipelineExtractorDriver()
        
        with driver:
            # 获取初始统计
            stats = driver.get_stats()
            logger.info("初始统计:")
            logger.info(f"  队列大小: {stats['queue_sizes']}")
            logger.info(f"  已处理项目: {stats['items_processed']}")
            logger.info(f"  失败项目: {stats['items_failed']}")
            
            # 处理一个文件
            test_file = Path("tests/data/test.pdf")
            if test_file.exists():
                result = driver.extract_from_file_sync(str(test_file), timeout=30.0)
                
                # 获取处理后统计
                stats = driver.get_stats()
                logger.info("处理后统计:")
                logger.info(f"  队列大小: {stats['queue_sizes']}")
                logger.info(f"  已处理项目: {stats['items_processed']}")
                logger.info(f"  失败项目: {stats['items_failed']}")
                logger.info(f"  平均处理时间: {stats['avg_processing_times']}")
                
            return True
            
    except Exception as e:
        logger.error(f"✗ 统计测试失败: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("开始测试优化后的流水线架构")
    logger.info("模式: Read Once, Process in Memory, Write at End")
    
    tests = [
        ("基本功能测试", test_pipeline_basic),
        ("统计功能测试", test_pipeline_stats),
        ("批量处理测试", test_pipeline_batch),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"运行测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            success = test_func()
            results.append((test_name, success))
            if success:
                logger.info(f"✓ {test_name} 通过")
            else:
                logger.error(f"✗ {test_name} 失败")
        except Exception as e:
            logger.error(f"✗ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    logger.info(f"\n{'='*50}")
    logger.info("测试总结")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✓ 通过" if success else "✗ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！流水线架构工作正常")
        return True
    else:
        logger.warning("⚠️  部分测试失败，需要检查问题")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
