#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@File    : test_openai_llm.py
<AUTHOR> XExtract Team
@Date    : 2023-05-07
@Desc    : Unit tests for OpenAILLM class
"""

import asyncio
import json
import os
import sys
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from openai.types.chat import Chat<PERSON><PERSON>pletion, ChatCompletionMessage

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
from src.xextract.llm.openai_llm import OpenAILLM
from src.xextract.llm.client import LLMClient


class TestOpenAILLM:
    """Test suite for OpenAILLM class."""

    @pytest.fixture
    def api_key(self):
        """Get API key from environment variable or use a dummy key for testing."""
        return os.getenv("XEXTRACT_LLM__API_KEY", "test-api-key")

    @pytest.fixture
    def model(self):
        """Get model from environment variable or use a default model for testing."""
        return os.getenv("XEXTRACT_LLM__MODEL", "gpt-3.5-turbo")

    @pytest.fixture
    def api_base_url(self):
        """Get API base URL from environment variable or use a default URL for testing."""
        return os.getenv("XEXTRACT_LLM__API_BASE_URL", "https://api.openai.com/v1")

    @pytest.fixture
    def provider(self):
        """Get provider from environment variable or use a default provider for testing."""
        return os.getenv("XEXTRACT_LLM__PROVIDER", "openai")

    @pytest.fixture
    def llm(self, api_key, model, api_base_url, provider) -> OpenAILLM:
        """Create an OpenAILLM instance for testing."""
        return OpenAILLM(
            api_key=api_key,
            model=model,
            api_base_url=api_base_url,
            provider=provider,
        )

    def test_llm_init(self, llm, api_key, model, api_base_url, provider):
        """Test initialization of OpenAILLM instance."""
        assert llm.api_key == api_key
        assert llm.model == model
        assert llm.api_base_url == api_base_url
        assert llm.provider == provider
        assert isinstance(llm.client, object)  # Check that client is initialized

    @pytest.mark.asyncio
    async def test_generate(self, llm: OpenAILLM):
        """Test generate method."""
        # Setup mock response
        # mock_client = AsyncMock()
        # mock_openai.return_value = mock_client

        # mock_response = MagicMock()
        # mock_message = MagicMock()
        # mock_message.content = "This is a test response"
        # mock_choice = MagicMock()
        # mock_choice.message = mock_message
        # mock_response.choices = [mock_choice]

        # mock_client.chat.completions.create = AsyncMock(return_value=mock_response)

        # # Replace the client with our mock
        # llm.client = mock_client

        # Call generate method
        extra_body = {"thinking": {"type": "disabled"}}
        result = await llm.generate(
            "计算1+1=?,直接输出结果。",
            temperature=0.0,
            max_tokens=1024,
            extra_body=extra_body,
        )

        # Verify result
        assert result == "2"

    @pytest.mark.asyncio
    @patch("src.xextract.llm.openai_llm.AsyncOpenAI")
    async def test_generate_json(self, mock_openai, llm):
        """Test generate_json method."""
        # Setup mock response
        mock_client = AsyncMock()
        mock_openai.return_value = mock_client

        mock_response = MagicMock()
        mock_message = MagicMock()
        mock_message.content = '{"key": "value", "number": 42}'
        mock_choice = MagicMock()
        mock_choice.message = mock_message
        mock_response.choices = [mock_choice]

        mock_client.chat.completions.create = AsyncMock(return_value=mock_response)

        # Replace the client with our mock
        llm.client = mock_client

        # Call generate_json method
        result = await llm.generate_json("List some data")

        # Verify result
        assert isinstance(result, dict)
        assert result["key"] == "value"
        assert result["number"] == 42


class TestLLMClient:
    """Test suite for LLMClient class."""

    @pytest.fixture
    def api_key(self):
        """Get API key from environment variable or use a dummy key for testing."""
        return "test-api-key"

    @pytest.fixture
    def client(self, api_key):
        """Create an LLMClient instance for testing."""
        # Create a mock LLM
        mock_llm = MagicMock()

        # Patch the create_llm import inside the __init__ method
        with patch("src.xextract.llm.create_llm") as mock_create_llm:
            mock_create_llm.return_value = mock_llm

            # Create client
            client = LLMClient(
                provider="openai",
                api_key=api_key,
                model="gpt-3.5-turbo",
            )

            # Replace the client's LLM with our mock
            client.llm = mock_llm

            return client

    def test_client_init(self, client):
        """Test initialization of LLMClient instance."""
        assert client.provider == "openai"
        assert client.model == "gpt-3.5-turbo"
