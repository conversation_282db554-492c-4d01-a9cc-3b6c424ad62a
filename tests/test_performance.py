#!/usr/bin/env python3
"""
性能测试：比较单进程vs多并发处理多文件的耗时差异
"""

import asyncio
import logging
import time
import json
from pathlib import Path
from typing import List, Dict, Any
import statistics

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_files(test_dir: Path, num_files: int = 5) -> List[Path]:
    """创建测试文件（复制现有的doc.json）"""
    test_dir.mkdir(parents=True, exist_ok=True)

    # 源文件
    source_file = Path("tests/data/doc.json")
    if not source_file.exists():
        raise FileNotFoundError(f"Source file not found: {source_file}")

    test_files = []

    # 读取源文件内容
    with open(source_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # 创建多个测试文件
    for i in range(num_files):
        test_file = test_dir / f"test_doc_{i+1}.json"
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(content)
        test_files.append(test_file)

    logger.info(f"Created {len(test_files)} test files in {test_dir}")
    return test_files

def test_single_process_extraction(test_files: List[Path], schema_name: str) -> Dict[str, Any]:
    """测试单进程顺序处理"""
    from src.xextract.engine import PipelineExtractorDriver

    logger.info("🔄 开始单进程顺序处理测试")

    # 创建单worker的驱动器
    driver = PipelineExtractorDriver(
        parse_workers=1,
        extract_workers=1,
        queue_size=10
    )

    results = []
    processing_times = []

    start_time = time.time()

    try:
        with driver:
            for i, test_file in enumerate(test_files, 1):
                logger.info(f"  处理文件 {i}/{len(test_files)}: {test_file.name}")

                file_start_time = time.time()

                try:
                    result = driver.extract_from_file_sync(
                        str(test_file),
                        schema_name=schema_name,
                        timeout=120
                    )

                    file_processing_time = time.time() - file_start_time
                    processing_times.append(file_processing_time)

                    if result and result.get('status') == 'success':
                        extraction_result = result.get('extraction_result', {})

                        # 输出大模型提取结果
                        print(f"    📄 大模型提取结果:")
                        try:
                            if hasattr(extraction_result, '__dict__'):
                                # ExtractionResult对象
                                result_dict = extraction_result.__dict__
                                fields_count = len(result_dict)
                                print(f"       类型: ExtractionResult对象")
                                for field_name, field_value in result_dict.items():
                                    # 安全地处理字段值
                                    if hasattr(field_value, '__dict__'):
                                        print(f"       {field_name}: <对象: {type(field_value).__name__}>")
                                    elif callable(field_value):
                                        print(f"       {field_name}: <方法: {field_value.__name__}>")
                                    else:
                                        print(f"       {field_name}: {field_value}")
                            elif isinstance(extraction_result, dict):
                                # 字典对象
                                fields_count = len(extraction_result)
                                print(f"       类型: 字典对象")
                                for field_name, field_value in extraction_result.items():
                                    print(f"       {field_name}: {field_value}")
                            else:
                                fields_count = 0
                                print(f"       类型: {type(extraction_result)}")
                                print(f"       内容: {str(extraction_result)[:200]}...")  # 限制输出长度
                        except Exception as e:
                            fields_count = 0
                            print(f"       处理提取结果时出错: {e}")
                            print(f"       原始类型: {type(extraction_result)}")
                            print(f"       原始内容: {str(extraction_result)[:100]}...")

                        results.append({
                            "file": test_file.name,
                            "status": "success",
                            "processing_time": file_processing_time,
                            "fields_extracted": fields_count
                        })
                        logger.info(f"    ✅ 成功 ({file_processing_time:.2f}s, 提取字段: {fields_count})")
                    else:
                        results.append({
                            "file": test_file.name,
                            "status": "error",
                            "processing_time": file_processing_time,
                            "error": result.get('error', 'Unknown error') if result else 'No result'
                        })
                        logger.info(f"    ❌ 失败 ({file_processing_time:.2f}s)")

                except Exception as e:
                    file_processing_time = time.time() - file_start_time
                    processing_times.append(file_processing_time)
                    results.append({
                        "file": test_file.name,
                        "status": "exception",
                        "processing_time": file_processing_time,
                        "error": str(e)
                    })
                    logger.error(f"    💥 异常 ({file_processing_time:.2f}s): {e}")

    except Exception as e:
        logger.error(f"单进程测试失败: {e}")
        return {"error": str(e)}

    total_time = time.time() - start_time

    # 统计结果
    success_count = sum(1 for r in results if r['status'] == 'success')
    error_count = len(results) - success_count

    stats = {
        "mode": "single_process",
        "total_files": len(test_files),
        "success_count": success_count,
        "error_count": error_count,
        "total_time": total_time,
        "avg_time_per_file": statistics.mean(processing_times) if processing_times else 0,
        "min_time": min(processing_times) if processing_times else 0,
        "max_time": max(processing_times) if processing_times else 0,
        "throughput": len(test_files) / total_time if total_time > 0 else 0,
        "results": results
    }

    logger.info(f"单进程测试完成: {success_count}/{len(test_files)} 成功, 总耗时 {total_time:.2f}s")
    return stats

def test_concurrent_extraction(test_files: List[Path], schema_name: str, max_concurrent: int = 3) -> Dict[str, Any]:
    """测试多并发处理"""
    from src.xextract.engine import PipelineExtractorDriver

    logger.info(f"🚀 开始多并发处理测试 (并发数: {max_concurrent})")

    # 创建多worker的驱动器
    driver = PipelineExtractorDriver(
        parse_workers=max_concurrent,
        extract_workers=max_concurrent,
        queue_size=20
    )

    start_time = time.time()

    try:
        with driver:
            # 使用批量处理方法
            results = asyncio.run(driver.extract_from_directory(
                str(test_files[0].parent),
                schema_name=schema_name,
                file_pattern="test_doc_*.json",
                max_concurrent=max_concurrent,
                timeout_per_file=120
            ))

    except Exception as e:
        logger.error(f"多并发测试失败: {e}")
        return {"error": str(e)}

    total_time = time.time() - start_time

    # 处理结果
    processing_times = []
    processed_results = []

    for result in results:
        processing_time = result.get('total_processing_time', 0)
        processing_times.append(processing_time)

        file_name = Path(result.get('source_path', '')).name

        if result.get('status') == 'success':
            extraction_result = result.get('extraction_result', {})

            # 输出大模型提取结果
            print(f"    📄 并发提取结果 ({file_name}):")
            try:
                if hasattr(extraction_result, '__dict__'):
                    # ExtractionResult对象
                    result_dict = extraction_result.__dict__
                    fields_count = len(result_dict)
                    print(f"       类型: ExtractionResult对象")
                    for field_name, field_value in result_dict.items():
                        # 安全地处理字段值
                        if hasattr(field_value, '__dict__'):
                            print(f"       {field_name}: <对象: {type(field_value).__name__}>")
                        elif callable(field_value):
                            print(f"       {field_name}: <方法: {field_value.__name__}>")
                        else:
                            print(f"       {field_name}: {field_value}")
                elif isinstance(extraction_result, dict):
                    # 字典对象
                    fields_count = len(extraction_result)
                    print(f"       类型: 字典对象")
                    for field_name, field_value in extraction_result.items():
                        print(f"       {field_name}: {field_value}")
                else:
                    fields_count = 0
                    print(f"       类型: {type(extraction_result)}")
                    print(f"       内容: {str(extraction_result)[:200]}...")  # 限制输出长度
            except Exception as e:
                fields_count = 0
                print(f"       处理提取结果时出错: {e}")
                print(f"       原始类型: {type(extraction_result)}")
                print(f"       原始内容: {str(extraction_result)[:100]}...")

            processed_results.append({
                "file": file_name,
                "status": "success",
                "processing_time": processing_time,
                "fields_extracted": fields_count
            })
        else:
            processed_results.append({
                "file": file_name,
                "status": "error",
                "processing_time": processing_time,
                "error": result.get('error', 'Unknown error')
            })

    # 统计结果
    success_count = sum(1 for r in results if r.get('status') == 'success')
    error_count = len(results) - success_count

    stats = {
        "mode": "concurrent",
        "max_concurrent": max_concurrent,
        "total_files": len(test_files),
        "success_count": success_count,
        "error_count": error_count,
        "total_time": total_time,
        "avg_time_per_file": statistics.mean(processing_times) if processing_times else 0,
        "min_time": min(processing_times) if processing_times else 0,
        "max_time": max(processing_times) if processing_times else 0,
        "throughput": len(test_files) / total_time if total_time > 0 else 0,
        "results": processed_results
    }

    logger.info(f"多并发测试完成: {success_count}/{len(test_files)} 成功, 总耗时 {total_time:.2f}s")
    return stats

def compare_performance(single_stats: Dict[str, Any], concurrent_stats: Dict[str, Any]) -> Dict[str, Any]:
    """比较性能结果"""

    if "error" in single_stats or "error" in concurrent_stats:
        return {"error": "One or both tests failed"}

    # 计算性能提升
    time_improvement = (single_stats['total_time'] - concurrent_stats['total_time']) / single_stats['total_time'] * 100
    throughput_improvement = (concurrent_stats['throughput'] - single_stats['throughput']) / single_stats['throughput'] * 100

    comparison = {
        "single_process": {
            "total_time": single_stats['total_time'],
            "avg_time_per_file": single_stats['avg_time_per_file'],
            "throughput": single_stats['throughput'],
            "success_rate": single_stats['success_count'] / single_stats['total_files'] * 100
        },
        "concurrent": {
            "total_time": concurrent_stats['total_time'],
            "avg_time_per_file": concurrent_stats['avg_time_per_file'],
            "throughput": concurrent_stats['throughput'],
            "success_rate": concurrent_stats['success_count'] / concurrent_stats['total_files'] * 100,
            "max_concurrent": concurrent_stats['max_concurrent']
        },
        "improvement": {
            "time_saved_seconds": single_stats['total_time'] - concurrent_stats['total_time'],
            "time_improvement_percent": time_improvement,
            "throughput_improvement_percent": throughput_improvement,
            "speedup_factor": single_stats['total_time'] / concurrent_stats['total_time'] if concurrent_stats['total_time'] > 0 else 0
        }
    }

    return comparison

def run_performance_test():
    """运行完整的性能测试"""

    print("🧪 PDF提取性能测试：单进程 vs 多并发")
    print("=" * 60)

    # 测试配置
    test_dir = Path("tests/data/performance_test")
    num_files = 1
    schema_name = "shareholder_meeting_notice"
    max_concurrent = 3

    try:
        # 1. 创建测试文件
        print(f"📁 创建 {num_files} 个测试文件...")
        test_files = create_test_files(test_dir, num_files)

        # 2. 单进程测试
        print(f"\n🔄 单进程顺序处理测试...")
        single_stats = test_single_process_extraction(test_files, schema_name)

        if "error" in single_stats:
            print(f"❌ 单进程测试失败: {single_stats['error']}")
            return

        # 3. 多并发测试
        print(f"\n🚀 多并发处理测试 (并发数: {max_concurrent})...")
        concurrent_stats = test_concurrent_extraction(test_files, schema_name, max_concurrent)

        if "error" in concurrent_stats:
            print(f"❌ 多并发测试失败: {concurrent_stats['error']}")
            return

        # 4. 性能比较
        print(f"\n📊 性能比较分析...")
        comparison = compare_performance(single_stats, concurrent_stats)

        if "error" in comparison:
            print(f"❌ 性能比较失败: {comparison['error']}")
            return

        # 5. 输出结果
        print_performance_results(comparison)

        # 6. 保存详细结果
        results_file = test_dir / f"performance_results_{int(time.time())}.json"
        detailed_results = {
            "test_config": {
                "num_files": num_files,
                "schema_name": schema_name,
                "max_concurrent": max_concurrent,
                "test_time": time.strftime("%Y-%m-%d %H:%M:%S")
            },
            "single_process_stats": single_stats,
            "concurrent_stats": concurrent_stats,
            "comparison": comparison
        }

        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(detailed_results, f, ensure_ascii=False, indent=2)

        print(f"\n💾 详细结果已保存到: {results_file}")

    except Exception as e:
        print(f"💥 性能测试失败: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # 清理测试文件
        if test_dir.exists():
            for file in test_dir.glob("test_doc_*.json"):
                file.unlink()
            print(f"🧹 已清理测试文件")

def print_performance_results(comparison: Dict[str, Any]):
    """打印性能测试结果"""

    single = comparison['single_process']
    concurrent = comparison['concurrent']
    improvement = comparison['improvement']

    print("\n" + "=" * 60)
    print("📈 性能测试结果")
    print("=" * 60)

    print(f"\n🔄 单进程顺序处理:")
    print(f"   总耗时: {single['total_time']:.2f}秒")
    print(f"   平均每文件: {single['avg_time_per_file']:.2f}秒")
    print(f"   吞吐量: {single['throughput']:.2f}文件/秒")
    print(f"   成功率: {single['success_rate']:.1f}%")

    print(f"\n🚀 多并发处理 (并发数: {concurrent['max_concurrent']}):")
    print(f"   总耗时: {concurrent['total_time']:.2f}秒")
    print(f"   平均每文件: {concurrent['avg_time_per_file']:.2f}秒")
    print(f"   吞吐量: {concurrent['throughput']:.2f}文件/秒")
    print(f"   成功率: {concurrent['success_rate']:.1f}%")

    print(f"\n📊 性能提升:")
    print(f"   时间节省: {improvement['time_saved_seconds']:.2f}秒")
    print(f"   时间提升: {improvement['time_improvement_percent']:.1f}%")
    print(f"   吞吐量提升: {improvement['throughput_improvement_percent']:.1f}%")
    print(f"   加速倍数: {improvement['speedup_factor']:.2f}x")

    # 性能评估
    if improvement['speedup_factor'] > 1.5:
        print(f"\n🎉 多并发处理显著提升性能！")
    elif improvement['speedup_factor'] > 1.1:
        print(f"\n✅ 多并发处理有一定性能提升")
    else:
        print(f"\n⚠️  多并发处理性能提升不明显，可能受限于I/O或其他因素")

if __name__ == "__main__":
    run_performance_test()
