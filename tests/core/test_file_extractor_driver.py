"""Tests for the FileExtractorDriver class."""

import os
import json
import pytest
from unittest.mock import MagicMock, patch
from pathlib import Path

from src.xextract.core.engine import FileExtractorDriver
from src.xextract.schema import Schema


@pytest.fixture
def mock_pdf_parser():
    """Create a mock PDF parser."""
    parser = MagicMock()
    parser.parse_pdf.return_value = {"pages": [{"text": "Test document content"}]}
    parser.to_markdown.return_value = "# Test Document\n\nTest document content"
    return parser


@pytest.fixture
def mock_extractor():
    """Create a mock extractor."""
    extractor = MagicMock()
    extractor.extract.return_value = {
        "title": "Test Document",
        "content": "Test document content"
    }
    return extractor


@pytest.fixture
def mock_confidence_calculator():
    """Create a mock confidence calculator."""
    calculator = MagicMock()
    calculator.calculate.return_value = {
        "title": "Test Document",
        "content": "Test document content",
        "metadata": {
            "confidence": {
                "overall": 0.85,
                "fields": {
                    "title": 0.9,
                    "content": 0.8
                }
            }
        }
    }
    return calculator


@pytest.fixture
def mock_schema():
    """Create a mock schema."""
    schema = MagicMock(spec=Schema)
    schema.fields = [
        {"name": "title", "type": "string"},
        {"name": "content", "type": "string"}
    ]
    return schema


@pytest.fixture
def driver(mock_pdf_parser, mock_extractor, mock_confidence_calculator):
    """Create a FileExtractorDriver with mock components."""
    with patch("src.xextract.core.engine.driver.PDFParserFactory") as mock_factory:
        mock_factory.create_parser.return_value = mock_pdf_parser
        
        with patch("src.xextract.core.engine.driver.ExtractorFactory") as mock_extractor_factory:
            mock_extractor_factory.create_extractor.return_value = mock_extractor
            
            with patch("src.xextract.core.engine.driver.ConfidenceCalculator") as mock_calculator_class:
                mock_calculator_class.return_value = mock_confidence_calculator
                
                driver = FileExtractorDriver(
                    pdf_parser=mock_pdf_parser,
                    extractor_type="llm"
                )
                
                # Set components directly
                driver.pdf_parser = mock_pdf_parser
                driver.extractor = mock_extractor
                driver.confidence_calculator = mock_confidence_calculator
                
                yield driver


@pytest.mark.asyncio
async def test_extract_from_file(driver, mock_schema, tmp_path):
    """Test extracting data from a file."""
    # Create a test file
    test_file = tmp_path / "test.pdf"
    test_file.write_bytes(b"Test PDF content")
    
    # Mock _load_schema method
    with patch.object(driver, "_load_schema", return_value=mock_schema):
        # Extract data
        result = await driver.extract_from_file(
            test_file,
            mock_schema,
            output_path=tmp_path / "output.json"
        )
        
        # Check that the PDF parser was called
        driver.pdf_parser.parse_pdf.assert_called_once()
        driver.pdf_parser.to_markdown.assert_called_once()
        
        # Check that the extractor was called
        driver.extractor.extract.assert_called_once()
        
        # Check that the confidence calculator was called
        driver.confidence_calculator.calculate.assert_called_once()
        
        # Check the result
        assert result["title"] == "Test Document"
        assert result["content"] == "Test document content"
        assert result["metadata"]["confidence"]["overall"] == 0.85
        
        # Check that the output file was created
        output_file = tmp_path / "output.json"
        assert output_file.exists()


def test_extract_from_file_sync(driver, mock_schema, tmp_path):
    """Test synchronous extraction from a file."""
    # Create a test file
    test_file = tmp_path / "test.pdf"
    test_file.write_bytes(b"Test PDF content")
    
    # Mock asyncio.run
    with patch("asyncio.run") as mock_run:
        mock_run.return_value = {
            "title": "Test Document",
            "content": "Test document content",
            "metadata": {
                "confidence": {
                    "overall": 0.85,
                    "fields": {
                        "title": 0.9,
                        "content": 0.8
                    }
                }
            }
        }
        
        # Mock _load_schema method
        with patch.object(driver, "_load_schema", return_value=mock_schema):
            # Extract data
            result = driver.extract_from_file_sync(
                test_file,
                mock_schema,
                output_path=tmp_path / "output.json"
            )
            
            # Check that asyncio.run was called
            mock_run.assert_called_once()
            
            # Check the result
            assert result["title"] == "Test Document"
            assert result["content"] == "Test document content"
            assert result["metadata"]["confidence"]["overall"] == 0.85


@pytest.mark.asyncio
async def test_extract_from_directory(driver, mock_schema, tmp_path):
    """Test extracting data from a directory."""
    # Create test files
    test_dir = tmp_path / "test_dir"
    test_dir.mkdir()
    
    for i in range(3):
        test_file = test_dir / f"test{i}.pdf"
        test_file.write_bytes(f"Test PDF content {i}".encode())
    
    # Create output directory
    output_dir = tmp_path / "output_dir"
    output_dir.mkdir()
    
    # Mock _load_schema method
    with patch.object(driver, "_load_schema", return_value=mock_schema):
        # Extract data
        results = await driver.extract_from_directory(
            test_dir,
            mock_schema,
            output_dir=output_dir,
            file_pattern="*.pdf"
        )
        
        # Check the results
        assert len(results) == 3
        
        # Check that output files were created
        assert len(list(output_dir.glob("*.json"))) == 3


def test_load_schema(driver):
    """Test loading schema from different sources."""
    # Mock Schema class
    with patch("src.xextract.core.engine.driver.Schema") as mock_schema_class:
        # Mock Schema.from_json_file
        mock_schema_class.from_json_file.return_value = "json_schema"
        
        # Mock Schema.from_yaml_file
        mock_schema_class.from_yaml_file.return_value = "yaml_schema"
        
        # Mock Schema.get_by_name
        mock_schema_class.get_by_name.return_value = "named_schema"
        
        # Test loading from JSON file
        with patch("os.path.exists", return_value=True):
            schema = driver._load_schema("test.json")
            assert schema == "json_schema"
            mock_schema_class.from_json_file.assert_called_once_with("test.json")
        
        # Reset mocks
        mock_schema_class.reset_mock()
        
        # Test loading from YAML file
        with patch("os.path.exists", return_value=True):
            schema = driver._load_schema("test.yaml")
            assert schema == "yaml_schema"
            mock_schema_class.from_yaml_file.assert_called_once_with("test.yaml")
        
        # Reset mocks
        mock_schema_class.reset_mock()
        
        # Test loading by name
        with patch("os.path.exists", return_value=False):
            schema = driver._load_schema("test_schema")
            assert schema == "named_schema"
            mock_schema_class.get_by_name.assert_called_once_with("test_schema")
