"""Test configuration for XExtract."""
import os
import sys
import pytest
from pathlib import Path

# Add the project root directory to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def pytest_configure(config):
    """Configure pytest."""
    # Register custom markers
    config.addinivalue_line("markers", "unit: unit tests")
    config.addinivalue_line("markers", "integration: integration tests")
    config.addinivalue_line("markers", "slow: slow tests")
    config.addinivalue_line("markers", "api: tests that require API access")


@pytest.fixture(scope="session", autouse=True)
def setup_environment():
    """Set up environment variables for testing."""
    # Load environment variables from .env file if it exists
    env_file = project_root / ".env"
    if env_file.exists():
        with open(env_file, "r") as f:
            for line in f:
                line = line.strip()
                if not line or line.startswith("#"):
                    continue
                try:
                    key, value = line.split("=", 1)
                    os.environ[key] = value
                except ValueError:
                    # Skip lines that don't have a key=value format
                    pass

    # Set test-specific environment variables
    os.environ["XEXTRACT_PDF_PARSER__DEFAULT_PARSER"] = "api"
    os.environ["XEXTRACT_LLM__PROVIDER"] = "gemini"
    os.environ["XEXTRACT_LLM__MODEL"] = "gemini-2.0-flash"

    yield

# Test data paths
TEST_DATA_DIR = Path(__file__).parent / "data"
TEST_DOCUMENT_PATH = TEST_DATA_DIR / "test_document.txt"
TEST_SCHEMA_PATH = Path(project_root) / "models" / "test_schema.json"
TEST_GROUND_TRUTH_PATH = TEST_DATA_DIR / "ground_truth" / "test_document.json"

# Ensure test directories exist
TEST_DATA_DIR.mkdir(exist_ok=True)
(TEST_DATA_DIR / "ground_truth").mkdir(exist_ok=True)
(TEST_DATA_DIR / "input").mkdir(exist_ok=True)
(TEST_DATA_DIR / "output").mkdir(exist_ok=True)


@pytest.fixture
def test_document_path():
    """Path to test document."""
    return TEST_DOCUMENT_PATH


@pytest.fixture
def test_schema_path():
    """Path to test schema."""
    return TEST_SCHEMA_PATH


@pytest.fixture
def test_ground_truth_path():
    """Path to test ground truth."""
    return TEST_GROUND_TRUTH_PATH


@pytest.fixture
def test_document_text():
    """Test document text."""
    if TEST_DOCUMENT_PATH.exists():
        with open(TEST_DOCUMENT_PATH, "r", encoding="utf-8") as f:
            return f.read()
    return ""


@pytest.fixture
def test_schema_data():
    """Test schema data."""
    if TEST_SCHEMA_PATH.exists():
        import json
        with open(TEST_SCHEMA_PATH, "r", encoding="utf-8") as f:
            return json.load(f)
    return {}


@pytest.fixture
def test_ground_truth_data():
    """Test ground truth data."""
    if TEST_GROUND_TRUTH_PATH.exists():
        import json
        with open(TEST_GROUND_TRUTH_PATH, "r", encoding="utf-8") as f:
            return json.load(f)
    return {}
