# Test Data

This directory contains test data files for testing the document parser service.

## Usage

Place your test PDF and DOCX files in this directory, then run the test script:

```bash
# Test PDF to document structure extraction
./tests/run_docparser_test.sh tests/data/sample.pdf pdf2doc

# Test PDF to table extraction
./tests/run_docparser_test.sh tests/data/sample.pdf pdf2table

# Test DOCX to PDF conversion
./tests/run_docparser_test.sh tests/data/sample.docx docx2pdf
```

## Sample Files

You should add the following sample files to this directory:

1. `sample.pdf` - A sample PDF file with text and tables
2. `sample.docx` - A sample DOCX file for testing conversion

You can use any PDF or DOCX file for testing. If you don't have any, you can download sample files from various sources online.
