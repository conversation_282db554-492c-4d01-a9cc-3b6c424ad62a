{"pdf_info": {"title": "", "producer": "Microsoft® Word 2016", "creator": "Microsoft® Word 2016", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subject": "", "keywords": "", "source": null}, "images": {}, "fonts": {"5": {"family": "Calib<PERSON>", "bold": false, "italic": false}, "9": {"family": "宋体", "bold": false, "italic": false}, "16": {"family": "黑体", "bold": false, "italic": false}, "23": {"family": "仿宋", "bold": false, "italic": false}, "25": {"family": "Calib<PERSON>", "bold": true, "italic": false}, "27": {"family": "Wingdings", "bold": false, "italic": false}, "32": {"family": "<PERSON><PERSON>", "bold": false, "italic": false}, "38": {"family": "Cambria", "bold": false, "italic": false}, "49": {"family": "华文中宋", "bold": false, "italic": false}, "unknown": {"family": "unknown", "bold": false, "italic": false}}, "pages": [{"number": 1, "bbox": [0, 0, 595, 842], "crop_bbox": [0, 0, 595, 842], "type": "pdf", "rotate": 0, "rotate2": 0, "warp_angle": 0, "header": null, "footer": null, "background": {"rects": [], "figures": [], "textboxes": []}, "sidebars": {"left": null, "right": null}, "footnotes": [], "subscripts": [], "supscripts": [], "body": {"bbox": [0, 0, 595, 842], "blocks": [{"bbox": [90, 100, 505, 768]}]}}, {"number": 2, "bbox": [0, 0, 595, 842], "crop_bbox": [0, 0, 595, 842], "type": "pdf", "rotate": 0, "rotate2": 0, "warp_angle": 0, "header": null, "footer": null, "background": {"rects": [], "figures": [], "textboxes": []}, "sidebars": {"left": null, "right": null}, "footnotes": [], "subscripts": [], "supscripts": [], "body": {"bbox": [0, 0, 595, 842], "blocks": [{"bbox": [90, 94, 517, 768]}]}}, {"number": 3, "bbox": [0, 0, 595, 842], "crop_bbox": [0, 0, 595, 842], "type": "pdf", "rotate": 0, "rotate2": 0, "warp_angle": 0, "header": null, "footer": null, "background": {"rects": [], "figures": [], "textboxes": []}, "sidebars": {"left": null, "right": null}, "footnotes": [], "subscripts": [], "supscripts": [], "body": {"bbox": [0, 0, 595, 842], "blocks": [{"bbox": [90, 92, 511, 768]}]}}, {"number": 4, "bbox": [0, 0, 595, 842], "crop_bbox": [0, 0, 595, 842], "type": "pdf", "rotate": 0, "rotate2": 0, "warp_angle": 0, "header": null, "footer": null, "background": {"rects": [], "figures": [], "textboxes": []}, "sidebars": {"left": null, "right": null}, "footnotes": [], "subscripts": [], "supscripts": [], "body": {"bbox": [0, 0, 595, 842], "blocks": [{"bbox": [90, 371, 505, 768]}]}}, {"number": 5, "bbox": [0, 0, 595, 842], "crop_bbox": [0, 0, 595, 842], "type": "pdf", "rotate": 0, "rotate2": 0, "warp_angle": 0, "header": null, "footer": null, "background": {"rects": [], "figures": [], "textboxes": []}, "sidebars": {"left": null, "right": null}, "footnotes": [], "subscripts": [], "supscripts": [], "body": {"bbox": [0, 0, 595, 842], "blocks": [{"bbox": [90, 168, 504, 757]}]}}], "layout_mode": "default", "tree": {"root": {"id": null, "type": "title", "parent_path": [], "data": null, "children": [{"id": 1, "type": "title", "parent_path": [], "page_number": 1, "data": {"text": "<封面>", "textlines": [], "target": null, "no": null}, "children": [{"id": 2, "type": "section", "parent_path": [1], "page_number": 1, "data": {"textlines": [{"bbox": [90, 756, 486, 768], "page_number": 1, "block_index": 0, "text": "证券代码：600536证券简称：中国软件公告编号：2024-033", "bold": false, "spans": [{"bbox": [90, 756, 150, 768], "page_number": 1, "block_index": 0, "text": "证券代码：", "bold": false}, {"bbox": [150, 756, 186, 768], "page_number": 1, "block_index": 0, "text": "600536", "bold": false}, {"bbox": [228, 756, 336, 768], "page_number": 1, "block_index": 0, "text": "证券简称：中国软件", "bold": false}, {"bbox": [378, 756, 438, 768], "page_number": 1, "block_index": 0, "text": "公告编号：", "bold": false}, {"bbox": [438, 756, 486, 768], "page_number": 1, "block_index": 0, "text": "2024-033", "bold": false}]}]}, "children": []}, {"id": 3, "type": "section", "parent_path": [1], "page_number": 1, "data": {"textlines": [{"bbox": [162, 714, 433, 732], "page_number": 1, "block_index": 0, "text": "中国软件与技术服务股份有限公司", "bold": true, "spans": [{"bbox": [162, 714, 433, 732], "page_number": 1, "block_index": 0, "text": "中国软件与技术服务股份有限公司", "bold": true}]}, {"bbox": [121, 683, 474, 701], "page_number": 1, "block_index": 0, "text": "关于召开2024年第四次临时股东大会的通知", "bold": true, "spans": [{"bbox": [121, 683, 194, 701], "page_number": 1, "block_index": 0, "text": "关于召开", "bold": true}, {"bbox": [198, 683, 234, 701], "page_number": 1, "block_index": 0, "text": "2024", "bold": true}, {"bbox": [239, 683, 474, 701], "page_number": 1, "block_index": 0, "text": "年第四次临时股东大会的通知", "bold": true}]}]}, "children": []}, {"id": 4, "type": "section", "parent_path": [1], "page_number": 1, "data": {"textlines": [{"bbox": [119, 633, 500, 644], "page_number": 1, "block_index": 0, "text": "本公司董事会及全体董事保证本公告内容不存在任何虚假记载、误导性陈述或者重", "bold": true, "spans": [{"bbox": [119, 633, 500, 644], "page_number": 1, "block_index": 0, "text": "本公司董事会及全体董事保证本公告内容不存在任何虚假记载、误导性陈述或者重", "bold": true}]}, {"bbox": [95, 613, 391, 623], "page_number": 1, "block_index": 0, "text": "大遗漏，并对其内容的真实性、准确性和完整性承担法律责任。", "bold": true, "spans": [{"bbox": [95, 613, 391, 623], "page_number": 1, "block_index": 0, "text": "大遗漏，并对其内容的真实性、准确性和完整性承担法律责任。", "bold": true}]}]}, "children": []}, {"id": 5, "type": "section", "parent_path": [1], "page_number": 1, "data": {"textlines": [{"bbox": [90, 567, 174, 579], "page_number": 1, "block_index": 0, "text": "重要内容提示：", "bold": true, "spans": [{"bbox": [90, 567, 174, 579], "page_number": 1, "block_index": 0, "text": "重要内容提示：", "bold": true}]}]}, "children": []}, {"id": 6, "type": "section", "parent_path": [1], "page_number": 1, "data": {"textlines": [{"bbox": [90, 544, 291, 560], "page_number": 1, "block_index": 0, "text": "●股东大会召开日期：2024年4月1日", "bold": false, "spans": [{"bbox": [90, 548, 99, 560], "page_number": 1, "block_index": 0, "text": "●", "bold": false}, {"bbox": [111, 544, 219, 556], "page_number": 1, "block_index": 0, "text": "股东大会召开日期：", "bold": false}, {"bbox": [219, 544, 243, 556], "page_number": 1, "block_index": 0, "text": "2024", "bold": false}, {"bbox": [243, 544, 291, 556], "page_number": 1, "block_index": 0, "text": "年4月1日", "bold": false}]}]}, "children": []}, {"id": 7, "type": "section", "parent_path": [1], "page_number": 1, "data": {"textlines": [{"bbox": [90, 525, 99, 537], "page_number": 1, "block_index": 0, "text": "●", "bold": false, "spans": [{"bbox": [90, 525, 99, 537], "page_number": 1, "block_index": 0, "text": "●", "bold": false}]}]}, "children": []}, {"id": 8, "type": "section", "parent_path": [1], "page_number": 1, "data": {"textlines": [{"bbox": [111, 520, 505, 532], "page_number": 1, "block_index": 0, "text": "本次股东大会采用的网络投票系统：上海证券交易所股东大会网络投票系统", "bold": false, "spans": [{"bbox": [111, 520, 505, 532], "page_number": 1, "block_index": 0, "text": "本次股东大会采用的网络投票系统：上海证券交易所股东大会网络投票系统", "bold": false}]}]}, "children": []}, {"id": 9, "type": "title", "parent_path": [1], "page_number": 1, "data": {"text": "一、召开会议的基本情况", "textlines": [{"bbox": [114, 474, 261, 486], "page_number": 1, "block_index": 0, "text": "一、召开会议的基本情况", "bold": true, "spans": [{"bbox": [114, 474, 138, 486], "page_number": 1, "block_index": 0, "text": "一、", "bold": true}, {"bbox": [153, 474, 261, 486], "page_number": 1, "block_index": 0, "text": "召开会议的基本情况", "bold": true}]}], "target": null, "no": {"text": "一、", "value": "一", "int_value": 1, "is_roman": false, "bbox": [114, 474, 138, 486], "page_number": 1}}, "children": [{"id": 10, "type": "title", "parent_path": [1, 9], "page_number": 1, "data": {"text": "（一）股东大会类型和届次", "textlines": [{"bbox": [114, 450, 258, 462], "page_number": 1, "block_index": 0, "text": "（一）股东大会类型和届次", "bold": false, "spans": [{"bbox": [114, 450, 258, 462], "page_number": 1, "block_index": 0, "text": "（一）股东大会类型和届次", "bold": false}]}], "target": null, "no": {"text": "（一）", "value": "一", "int_value": 1, "is_roman": false, "bbox": [114, 450, 150, 462], "page_number": 1}}, "children": [{"id": 11, "type": "section", "parent_path": [1, 9, 10], "page_number": 1, "data": {"textlines": [{"bbox": [114, 427, 261, 439], "page_number": 1, "block_index": 0, "text": "2024年第四次临时股东大会", "bold": false, "spans": [{"bbox": [114, 427, 138, 439], "page_number": 1, "block_index": 0, "text": "2024", "bold": false}, {"bbox": [141, 427, 261, 439], "page_number": 1, "block_index": 0, "text": "年第四次临时股东大会", "bold": false}]}]}, "children": []}]}, {"id": 12, "type": "title", "parent_path": [1, 9], "page_number": 1, "data": {"text": "（二）股东大会召集人：董事会", "textlines": [{"bbox": [114, 404, 282, 416], "page_number": 1, "block_index": 0, "text": "（二）股东大会召集人：董事会", "bold": false, "spans": [{"bbox": [114, 404, 282, 416], "page_number": 1, "block_index": 0, "text": "（二）股东大会召集人：董事会", "bold": false}]}], "target": null, "no": {"text": "（二）", "value": "二", "int_value": 2, "is_roman": false, "bbox": [114, 404, 150, 416], "page_number": 1}}, "children": []}, {"id": 13, "type": "title", "parent_path": [1, 9], "page_number": 1, "data": {"text": "（三）投票方式：本次股东大会所采用的表决方式是现场投票和网络投票相结合的方式", "textlines": [{"bbox": [114, 380, 505, 392], "page_number": 1, "block_index": 0, "text": "（三）投票方式：本次股东大会所采用的表决方式是现场投票和网络投票相", "bold": false, "spans": [{"bbox": [114, 380, 505, 392], "page_number": 1, "block_index": 0, "text": "（三）投票方式：本次股东大会所采用的表决方式是现场投票和网络投票相", "bold": false}]}, {"bbox": [90, 357, 150, 369], "page_number": 1, "block_index": 0, "text": "结合的方式", "bold": false, "spans": [{"bbox": [90, 357, 150, 369], "page_number": 1, "block_index": 0, "text": "结合的方式", "bold": false}]}], "target": null, "no": {"text": "（三）", "value": "三", "int_value": 3, "is_roman": false, "bbox": [114, 380, 145, 392], "page_number": 1}}, "children": []}, {"id": 14, "type": "title", "parent_path": [1, 9], "page_number": 1, "data": {"text": "（四）现场会议召开的日期、时间和地点", "textlines": [{"bbox": [114, 334, 330, 346], "page_number": 1, "block_index": 0, "text": "（四）现场会议召开的日期、时间和地点", "bold": false, "spans": [{"bbox": [114, 334, 330, 346], "page_number": 1, "block_index": 0, "text": "（四）现场会议召开的日期、时间和地点", "bold": false}]}], "target": null, "no": {"text": "（四）", "value": "四", "int_value": 4, "is_roman": false, "bbox": [114, 334, 150, 346], "page_number": 1}}, "children": [{"id": 15, "type": "section", "parent_path": [1, 9, 14], "page_number": 1, "data": {"textlines": [{"bbox": [114, 310, 366, 322], "page_number": 1, "block_index": 0, "text": "召开的日期时间：2024年4月1日14点30分", "bold": false, "spans": [{"bbox": [114, 310, 210, 322], "page_number": 1, "block_index": 0, "text": "召开的日期时间：", "bold": false}, {"bbox": [210, 310, 234, 322], "page_number": 1, "block_index": 0, "text": "2024", "bold": false}, {"bbox": [237, 310, 249, 322], "page_number": 1, "block_index": 0, "text": "年", "bold": false}, {"bbox": [252, 310, 258, 322], "page_number": 1, "block_index": 0, "text": "4", "bold": false}, {"bbox": [261, 310, 273, 322], "page_number": 1, "block_index": 0, "text": "月", "bold": false}, {"bbox": [276, 310, 282, 322], "page_number": 1, "block_index": 0, "text": "1", "bold": false}, {"bbox": [285, 310, 297, 322], "page_number": 1, "block_index": 0, "text": "日", "bold": false}, {"bbox": [309, 310, 321, 322], "page_number": 1, "block_index": 0, "text": "14", "bold": false}, {"bbox": [324, 310, 336, 322], "page_number": 1, "block_index": 0, "text": "点", "bold": false}, {"bbox": [339, 310, 351, 322], "page_number": 1, "block_index": 0, "text": "30", "bold": false}, {"bbox": [354, 310, 366, 322], "page_number": 1, "block_index": 0, "text": "分", "bold": false}]}]}, "children": []}, {"id": 16, "type": "section", "parent_path": [1, 9, 14], "page_number": 1, "data": {"textlines": [{"bbox": [114, 287, 480, 299], "page_number": 1, "block_index": 0, "text": "召开地点：北京市海淀区学院南路55号中软大厦C座1层第一会议室", "bold": false, "spans": [{"bbox": [114, 287, 294, 299], "page_number": 1, "block_index": 0, "text": "召开地点：北京市海淀区学院南路", "bold": false}, {"bbox": [297, 287, 309, 299], "page_number": 1, "block_index": 0, "text": "55", "bold": false}, {"bbox": [312, 287, 372, 299], "page_number": 1, "block_index": 0, "text": "号中软大厦", "bold": false}, {"bbox": [375, 287, 381, 299], "page_number": 1, "block_index": 0, "text": "C", "bold": false}, {"bbox": [384, 287, 396, 299], "page_number": 1, "block_index": 0, "text": "座", "bold": false}, {"bbox": [399, 287, 405, 299], "page_number": 1, "block_index": 0, "text": "1", "bold": false}, {"bbox": [408, 287, 480, 299], "page_number": 1, "block_index": 0, "text": "层第一会议室", "bold": false}]}]}, "children": []}]}, {"id": 17, "type": "title", "parent_path": [1, 9], "page_number": 1, "data": {"text": "（五）网络投票的系统、起止日期和投票时间。", "textlines": [{"bbox": [114, 264, 366, 276], "page_number": 1, "block_index": 0, "text": "（五）网络投票的系统、起止日期和投票时间。", "bold": false, "spans": [{"bbox": [114, 264, 366, 276], "page_number": 1, "block_index": 0, "text": "（五）网络投票的系统、起止日期和投票时间。", "bold": false}]}], "target": null, "no": {"text": "（五）", "value": "五", "int_value": 5, "is_roman": false, "bbox": [114, 264, 150, 276], "page_number": 1}}, "children": [{"id": 18, "type": "section", "parent_path": [1, 9, 17], "page_number": 1, "data": {"textlines": [{"bbox": [114, 240, 402, 252], "page_number": 1, "block_index": 0, "text": "网络投票系统：上海证券交易所股东大会网络投票系统", "bold": false, "spans": [{"bbox": [114, 240, 402, 252], "page_number": 1, "block_index": 0, "text": "网络投票系统：上海证券交易所股东大会网络投票系统", "bold": false}]}]}, "children": []}, {"id": 19, "type": "section", "parent_path": [1, 9, 17], "page_number": 1, "data": {"textlines": [{"bbox": [114, 217, 324, 229], "page_number": 1, "block_index": 0, "text": "网络投票起止时间：自2024年4月1日", "bold": false, "spans": [{"bbox": [114, 217, 234, 229], "page_number": 1, "block_index": 0, "text": "网络投票起止时间：自", "bold": false}, {"bbox": [237, 217, 261, 229], "page_number": 1, "block_index": 0, "text": "2024", "bold": false}, {"bbox": [264, 217, 276, 229], "page_number": 1, "block_index": 0, "text": "年", "bold": false}, {"bbox": [279, 217, 285, 229], "page_number": 1, "block_index": 0, "text": "4", "bold": false}, {"bbox": [288, 217, 300, 229], "page_number": 1, "block_index": 0, "text": "月", "bold": false}, {"bbox": [303, 217, 309, 229], "page_number": 1, "block_index": 0, "text": "1", "bold": false}, {"bbox": [312, 217, 324, 229], "page_number": 1, "block_index": 0, "text": "日", "bold": false}]}]}, "children": []}, {"id": 20, "type": "section", "parent_path": [1, 9, 17], "page_number": 1, "data": {"textlines": [{"bbox": [222, 194, 324, 206], "page_number": 1, "block_index": 0, "text": "至2024年4月1日", "bold": false, "spans": [{"bbox": [222, 194, 234, 206], "page_number": 1, "block_index": 0, "text": "至", "bold": false}, {"bbox": [237, 194, 261, 206], "page_number": 1, "block_index": 0, "text": "2024", "bold": false}, {"bbox": [264, 194, 276, 206], "page_number": 1, "block_index": 0, "text": "年", "bold": false}, {"bbox": [279, 194, 285, 206], "page_number": 1, "block_index": 0, "text": "4", "bold": false}, {"bbox": [288, 194, 300, 206], "page_number": 1, "block_index": 0, "text": "月", "bold": false}, {"bbox": [303, 194, 309, 206], "page_number": 1, "block_index": 0, "text": "1", "bold": false}, {"bbox": [312, 194, 324, 206], "page_number": 1, "block_index": 0, "text": "日", "bold": false}]}, {"bbox": [114, 170, 505, 182], "page_number": 1, "block_index": 0, "text": "采用上海证券交易所网络投票系统，通过交易系统投票平台的投票时间为股", "bold": false, "spans": [{"bbox": [114, 170, 505, 182], "page_number": 1, "block_index": 0, "text": "采用上海证券交易所网络投票系统，通过交易系统投票平台的投票时间为股", "bold": false}]}]}, "children": []}, {"id": 21, "type": "section", "parent_path": [1, 9, 17], "page_number": 1, "data": {"textlines": [{"bbox": [90, 147, 505, 159], "page_number": 1, "block_index": 0, "text": "东大会召开当日的交易时间段，即9:15-9:25,9:30-11:30，13:00-15:00；通过", "bold": false, "spans": [{"bbox": [90, 147, 269, 159], "page_number": 1, "block_index": 0, "text": "东大会召开当日的交易时间段，即", "bold": false}, {"bbox": [272, 147, 392, 159], "page_number": 1, "block_index": 0, "text": "9:15-9:25,9:30-11:30", "bold": false}, {"bbox": [392, 147, 404, 159], "page_number": 1, "block_index": 0, "text": "，", "bold": false}, {"bbox": [404, 147, 470, 159], "page_number": 1, "block_index": 0, "text": "13:00-15:00", "bold": false}, {"bbox": [470, 147, 505, 159], "page_number": 1, "block_index": 0, "text": "；通过", "bold": false}]}, {"bbox": [90, 124, 429, 136], "page_number": 1, "block_index": 0, "text": "互联网投票平台的投票时间为股东大会召开当日的9:15-15:00。", "bold": false, "spans": [{"bbox": [90, 124, 354, 136], "page_number": 1, "block_index": 0, "text": "互联网投票平台的投票时间为股东大会召开当日的", "bold": false}, {"bbox": [357, 124, 417, 136], "page_number": 1, "block_index": 0, "text": "9:15-15:00", "bold": false}, {"bbox": [417, 124, 429, 136], "page_number": 1, "block_index": 0, "text": "。", "bold": false}]}]}, "children": []}]}, {"id": 22, "type": "title", "parent_path": [1, 9], "page_number": 1, "data": {"text": "（六）融资融券、转融通、约定购回业务账户和沪股通投资者的投票程序", "textlines": [{"bbox": [114, 100, 498, 112], "page_number": 1, "block_index": 0, "text": "（六）融资融券、转融通、约定购回业务账户和沪股通投资者的投票程序", "bold": false, "spans": [{"bbox": [114, 100, 498, 112], "page_number": 1, "block_index": 0, "text": "（六）融资融券、转融通、约定购回业务账户和沪股通投资者的投票程序", "bold": false}]}], "target": null, "no": {"text": "（六）", "value": "六", "int_value": 6, "is_roman": false, "bbox": [114, 100, 150, 112], "page_number": 1}}, "children": [{"id": 23, "type": "section", "parent_path": [1, 9, 22], "page_number": 2, "data": {"textlines": [{"bbox": [114, 756, 498, 768], "page_number": 2, "block_index": 0, "text": "涉及融资融券、转融通业务、约定购回业务相关账户以及沪股通投资者的", "bold": false, "spans": [{"bbox": [114, 756, 498, 768], "page_number": 2, "block_index": 0, "text": "涉及融资融券、转融通业务、约定购回业务相关账户以及沪股通投资者的", "bold": false}]}, {"bbox": [90, 733, 498, 745], "page_number": 2, "block_index": 0, "text": "投票，应按照《上海证券交易所上市公司自律监管指引第1号—规范运作》", "bold": false, "spans": [{"bbox": [90, 733, 390, 745], "page_number": 2, "block_index": 0, "text": "投票，应按照《上海证券交易所上市公司自律监管指引第", "bold": false}, {"bbox": [393, 733, 399, 745], "page_number": 2, "block_index": 0, "text": "1", "bold": false}, {"bbox": [402, 733, 414, 745], "page_number": 2, "block_index": 0, "text": "号", "bold": false}, {"bbox": [420, 733, 432, 745], "page_number": 2, "block_index": 0, "text": "—", "bold": false}, {"bbox": [438, 733, 498, 745], "page_number": 2, "block_index": 0, "text": "规范运作》", "bold": false}]}, {"bbox": [90, 710, 186, 722], "page_number": 2, "block_index": 0, "text": "等有关规定执行。", "bold": false, "spans": [{"bbox": [90, 710, 186, 722], "page_number": 2, "block_index": 0, "text": "等有关规定执行。", "bold": false}]}]}, "children": []}]}, {"id": 24, "type": "title", "parent_path": [1, 9], "page_number": 2, "data": {"text": "（七）涉及公开征集股东投票权无", "textlines": [{"bbox": [114, 686, 282, 698], "page_number": 2, "block_index": 0, "text": "（七）涉及公开征集股东投票权", "bold": false, "spans": [{"bbox": [114, 686, 282, 698], "page_number": 2, "block_index": 0, "text": "（七）涉及公开征集股东投票权", "bold": false}]}, {"bbox": [114, 663, 126, 675], "page_number": 2, "block_index": 0, "text": "无", "bold": false, "spans": [{"bbox": [114, 663, 126, 675], "page_number": 2, "block_index": 0, "text": "无", "bold": false}]}], "target": null, "no": {"text": "（七）", "value": "七", "int_value": 7, "is_roman": false, "bbox": [114, 686, 150, 698], "page_number": 2}}, "children": []}]}, {"id": 25, "type": "title", "parent_path": [1], "page_number": 2, "data": {"text": "二、会议审议事项", "textlines": [{"bbox": [114, 639, 211, 651], "page_number": 2, "block_index": 0, "text": "二、会议审议事项", "bold": true, "spans": [{"bbox": [114, 639, 211, 651], "page_number": 2, "block_index": 0, "text": "二、会议审议事项", "bold": true}]}], "target": null, "no": {"text": "二、", "value": "二", "int_value": 2, "is_roman": false, "bbox": [114, 639, 139, 651], "page_number": 2}}, "children": [{"id": 26, "type": "section", "parent_path": [1, 25], "page_number": 2, "data": {"textlines": [{"bbox": [114, 616, 318, 628], "page_number": 2, "block_index": 0, "text": "本次股东大会审议议案及投票股东类型", "bold": false, "spans": [{"bbox": [114, 616, 318, 628], "page_number": 2, "block_index": 0, "text": "本次股东大会审议议案及投票股东类型", "bold": false}]}]}, "children": []}, {"id": 27, "type": "table", "parent_path": [1, 25], "page_number": 2, "data": {"row_num": 4, "col_num": 3, "cells": [{"text": "序号", "bold": true, "row_index": 0, "col_index": 0, "row_span": 2, "col_span": 1, "figures": [], "features": {}, "merged": false, "bbox": [90, 555, 133, 590], "spans": [{"bbox": [103, 569, 121, 578], "page_number": 2, "block_index": 0, "text": "序号", "bold": true}], "page_number": 2, "block_index": 0}, {"text": "议案名称", "bold": true, "row_index": 0, "col_index": 1, "row_span": 2, "col_span": 1, "figures": [], "features": {}, "merged": false, "bbox": [133, 555, 416, 590], "spans": [{"bbox": [256, 569, 293, 578], "page_number": 2, "block_index": 0, "text": "议案名称", "bold": true}], "page_number": 2, "block_index": 0}, {"text": "投票股东类型", "bold": true, "row_index": 0, "col_index": 2, "row_span": 1, "col_span": 1, "figures": [], "features": {}, "merged": false, "bbox": [416, 573, 505, 590], "spans": [{"bbox": [434, 577, 488, 586], "page_number": 2, "block_index": 0, "text": "投票股东类型", "bold": true}], "page_number": 2, "block_index": 0}, {"text": "A股股东", "bold": true, "row_index": 1, "col_index": 2, "row_span": 1, "col_span": 1, "figures": [], "features": {}, "merged": false, "bbox": [416, 555, 505, 573], "spans": [{"bbox": [444, 560, 448, 569], "page_number": 2, "block_index": 0, "text": "A", "bold": true}, {"bbox": [451, 560, 478, 569], "page_number": 2, "block_index": 0, "text": "股股东", "bold": true}], "page_number": 2, "block_index": 0}, {"text": "非累积投票议案", "bold": true, "row_index": 2, "col_index": 0, "row_span": 1, "col_span": 3, "figures": [], "features": {}, "merged": false, "bbox": [90, 538, 505, 555], "spans": [{"bbox": [266, 543, 329, 552], "page_number": 2, "block_index": 0, "text": "非累积投票议案", "bold": true}], "page_number": 2, "block_index": 0}, {"text": "1", "bold": false, "row_index": 3, "col_index": 0, "row_span": 1, "col_span": 1, "figures": [], "features": {}, "merged": false, "bbox": [90, 520, 133, 538], "spans": [{"bbox": [109, 525, 114, 534], "page_number": 2, "block_index": 0, "text": "1", "bold": false}], "page_number": 2, "block_index": 0}, {"text": "关于参股公司其他股东转让股权并增资扩股公司放弃相关权利的议案", "bold": false, "row_index": 3, "col_index": 1, "row_span": 1, "col_span": 1, "figures": [], "features": {}, "merged": false, "bbox": [133, 520, 416, 538], "spans": [{"bbox": [138, 525, 408, 534], "page_number": 2, "block_index": 0, "text": "关于参股公司其他股东转让股权并增资扩股公司放弃相关权利的议案", "bold": false}], "page_number": 2, "block_index": 0}, {"text": "√", "bold": false, "row_index": 3, "col_index": 2, "row_span": 1, "col_span": 1, "figures": [], "features": {}, "merged": false, "bbox": [416, 520, 505, 538], "spans": [{"bbox": [456, 525, 465, 534], "page_number": 2, "block_index": 0, "text": "√", "bold": false}], "page_number": 2, "block_index": 0}], "merged": false, "bbox": [90, 520, 505, 590], "lines": [], "features": {"v_edges": true, "border": true}, "page_number": 2, "block_index": 0}, "children": []}, {"id": 28, "type": "title", "parent_path": [1, 25], "page_number": 2, "data": {"text": "1、各议案已披露的时间和披露媒体", "textlines": [{"bbox": [111, 491, 297, 503], "page_number": 2, "block_index": 0, "text": "1、各议案已披露的时间和披露媒体", "bold": false, "spans": [{"bbox": [111, 491, 117, 503], "page_number": 2, "block_index": 0, "text": "1", "bold": false}, {"bbox": [117, 491, 297, 503], "page_number": 2, "block_index": 0, "text": "、各议案已披露的时间和披露媒体", "bold": false}]}], "target": null, "no": {"text": "1、", "value": "1", "int_value": 1, "is_roman": false, "bbox": [111, 491, 129, 503], "page_number": 2}}, "children": [{"id": 29, "type": "section", "parent_path": [1, 25, 28], "page_number": 2, "data": {"textlines": [{"bbox": [114, 468, 267, 480], "page_number": 2, "block_index": 0, "text": "披露时间：2024年3月16日", "bold": false, "spans": [{"bbox": [114, 468, 174, 480], "page_number": 2, "block_index": 0, "text": "披露时间：", "bold": false}, {"bbox": [174, 468, 198, 480], "page_number": 2, "block_index": 0, "text": "2024", "bold": false}, {"bbox": [201, 468, 213, 480], "page_number": 2, "block_index": 0, "text": "年", "bold": false}, {"bbox": [216, 468, 222, 480], "page_number": 2, "block_index": 0, "text": "3", "bold": false}, {"bbox": [225, 468, 237, 480], "page_number": 2, "block_index": 0, "text": "月", "bold": false}, {"bbox": [240, 468, 252, 480], "page_number": 2, "block_index": 0, "text": "16", "bold": false}, {"bbox": [255, 468, 267, 480], "page_number": 2, "block_index": 0, "text": "日", "bold": false}]}]}, "children": []}, {"id": 30, "type": "section", "parent_path": [1, 25, 28], "page_number": 2, "data": {"textlines": [{"bbox": [114, 444, 505, 456], "page_number": 2, "block_index": 0, "text": "披露媒体：上海证券交易所网站www.sse.com.cn及《中国证券报》、《上海", "bold": false, "spans": [{"bbox": [114, 444, 282, 456], "page_number": 2, "block_index": 0, "text": "披露媒体：上海证券交易所网站", "bold": false}, {"bbox": [286, 444, 370, 456], "page_number": 2, "block_index": 0, "text": "www.sse.com.cn", "bold": false}, {"bbox": [373, 444, 505, 456], "page_number": 2, "block_index": 0, "text": "及《中国证券报》、《上海", "bold": false}]}, {"bbox": [90, 421, 282, 433], "page_number": 2, "block_index": 0, "text": "证券报》、《证券时报》、《证券日报》", "bold": false, "spans": [{"bbox": [90, 421, 144, 433], "page_number": 2, "block_index": 0, "text": "证券报》、", "bold": false}, {"bbox": [138, 421, 282, 433], "page_number": 2, "block_index": 0, "text": "《证券时报》、《证券日报》", "bold": false}]}]}, "children": []}]}, {"id": 31, "type": "title", "parent_path": [1, 25], "page_number": 2, "data": {"text": "2、特别决议议案：无", "textlines": [{"bbox": [114, 398, 228, 410], "page_number": 2, "block_index": 0, "text": "2、特别决议议案：无", "bold": false, "spans": [{"bbox": [114, 398, 120, 410], "page_number": 2, "block_index": 0, "text": "2", "bold": false}, {"bbox": [120, 398, 228, 410], "page_number": 2, "block_index": 0, "text": "、特别决议议案：无", "bold": false}]}], "target": null, "no": {"text": "2、", "value": "2", "int_value": 2, "is_roman": false, "bbox": [114, 398, 132, 410], "page_number": 2}}, "children": []}, {"id": 32, "type": "title", "parent_path": [1, 25], "page_number": 2, "data": {"text": "3、对中小投资者单独计票的议案：无", "textlines": [{"bbox": [114, 374, 312, 386], "page_number": 2, "block_index": 0, "text": "3、对中小投资者单独计票的议案：无", "bold": false, "spans": [{"bbox": [114, 374, 120, 386], "page_number": 2, "block_index": 0, "text": "3", "bold": false}, {"bbox": [120, 374, 312, 386], "page_number": 2, "block_index": 0, "text": "、对中小投资者单独计票的议案：无", "bold": false}]}], "target": null, "no": {"text": "3、", "value": "3", "int_value": 3, "is_roman": false, "bbox": [114, 374, 132, 386], "page_number": 2}}, "children": []}, {"id": 33, "type": "title", "parent_path": [1, 25], "page_number": 2, "data": {"text": "4、涉及关联股东回避表决的议案：无", "textlines": [{"bbox": [114, 351, 312, 363], "page_number": 2, "block_index": 0, "text": "4、涉及关联股东回避表决的议案：无", "bold": false, "spans": [{"bbox": [114, 351, 120, 363], "page_number": 2, "block_index": 0, "text": "4", "bold": false}, {"bbox": [120, 351, 312, 363], "page_number": 2, "block_index": 0, "text": "、涉及关联股东回避表决的议案：无", "bold": false}]}], "target": null, "no": {"text": "4、", "value": "4", "int_value": 4, "is_roman": false, "bbox": [114, 351, 132, 363], "page_number": 2}}, "children": [{"id": 34, "type": "section", "parent_path": [1, 25, 33], "page_number": 2, "data": {"textlines": [{"bbox": [132, 328, 300, 340], "page_number": 2, "block_index": 0, "text": "应回避表决的关联股东名称：无", "bold": false, "spans": [{"bbox": [132, 328, 300, 340], "page_number": 2, "block_index": 0, "text": "应回避表决的关联股东名称：无", "bold": false}]}]}, "children": []}]}, {"id": 35, "type": "title", "parent_path": [1, 25], "page_number": 2, "data": {"text": "5、涉及优先股股东参与表决的议案：无", "textlines": [{"bbox": [114, 304, 324, 316], "page_number": 2, "block_index": 0, "text": "5、涉及优先股股东参与表决的议案：无", "bold": false, "spans": [{"bbox": [114, 304, 120, 316], "page_number": 2, "block_index": 0, "text": "5", "bold": false}, {"bbox": [120, 304, 324, 316], "page_number": 2, "block_index": 0, "text": "、涉及优先股股东参与表决的议案：无", "bold": false}]}], "target": null, "no": {"text": "5、", "value": "5", "int_value": 5, "is_roman": false, "bbox": [114, 304, 132, 316], "page_number": 2}}, "children": []}]}, {"id": 36, "type": "title", "parent_path": [1], "page_number": 2, "data": {"text": "三、股东大会投票注意事项", "textlines": [{"bbox": [114, 258, 260, 270], "page_number": 2, "block_index": 0, "text": "三、股东大会投票注意事项", "bold": true, "spans": [{"bbox": [114, 258, 260, 270], "page_number": 2, "block_index": 0, "text": "三、股东大会投票注意事项", "bold": true}]}], "target": null, "no": {"text": "三、", "value": "三", "int_value": 3, "is_roman": false, "bbox": [114, 258, 140, 270], "page_number": 2}}, "children": [{"id": 37, "type": "title", "parent_path": [1, 36], "page_number": 2, "data": {"text": "（一）本公司股东通过上海证券交易所股东大会网络投票系统行使表决权的，既可以登陆交易系统投票平台（通过指定交易的证券公司交易终端）进行投票，也可以登陆互联网投票平台（网址：vote.sseinfo.com）进行投票。首次登陆互联网投票平台进行投票的，投资者需要完成股东身份认证。具体操作请见互联网投票平台网站说明。", "textlines": [{"bbox": [114, 234, 517, 246], "page_number": 2, "block_index": 0, "text": "（一）本公司股东通过上海证券交易所股东大会网络投票系统行使表决权的，", "bold": false, "spans": [{"bbox": [114, 234, 517, 246], "page_number": 2, "block_index": 0, "text": "（一）本公司股东通过上海证券交易所股东大会网络投票系统行使表决权的，", "bold": false}]}, {"bbox": [90, 211, 511, 223], "page_number": 2, "block_index": 0, "text": "既可以登陆交易系统投票平台（通过指定交易的证券公司交易终端）进行投票，", "bold": false, "spans": [{"bbox": [90, 211, 511, 223], "page_number": 2, "block_index": 0, "text": "既可以登陆交易系统投票平台（通过指定交易的证券公司交易终端）进行投票，", "bold": false}]}, {"bbox": [90, 188, 505, 200], "page_number": 2, "block_index": 0, "text": "也可以登陆互联网投票平台（网址：vote.sseinfo.com）进行投票。首次登陆互", "bold": false, "spans": [{"bbox": [90, 188, 281, 200], "page_number": 2, "block_index": 0, "text": "也可以登陆互联网投票平台（网址：", "bold": false}, {"bbox": [280, 188, 376, 200], "page_number": 2, "block_index": 0, "text": "vote.sseinfo.com", "bold": false}, {"bbox": [376, 188, 505, 200], "page_number": 2, "block_index": 0, "text": "）进行投票。首次登陆互", "bold": false}]}, {"bbox": [90, 164, 505, 176], "page_number": 2, "block_index": 0, "text": "联网投票平台进行投票的，投资者需要完成股东身份认证。具体操作请见互联网", "bold": false, "spans": [{"bbox": [90, 164, 505, 176], "page_number": 2, "block_index": 0, "text": "联网投票平台进行投票的，投资者需要完成股东身份认证。具体操作请见互联网", "bold": false}]}, {"bbox": [90, 141, 198, 153], "page_number": 2, "block_index": 0, "text": "投票平台网站说明。", "bold": false, "spans": [{"bbox": [90, 141, 198, 153], "page_number": 2, "block_index": 0, "text": "投票平台网站说明。", "bold": false}]}], "target": null, "no": {"text": "（一）", "value": "一", "int_value": 1, "is_roman": false, "bbox": [114, 234, 145, 246], "page_number": 2}}, "children": []}, {"id": 38, "type": "title", "parent_path": [1, 36], "page_number": 2, "data": {"text": "（二）持有多个股东账户的股东，可行使的表决权数量是其名下全部股东账户所持相同类别普通股和相同品种优先股的数量总和。", "textlines": [{"bbox": [114, 117, 505, 129], "page_number": 2, "block_index": 0, "text": "（二）持有多个股东账户的股东，可行使的表决权数量是其名下全部股东账", "bold": false, "spans": [{"bbox": [114, 117, 505, 129], "page_number": 2, "block_index": 0, "text": "（二）持有多个股东账户的股东，可行使的表决权数量是其名下全部股东账", "bold": false}]}, {"bbox": [90, 94, 378, 106], "page_number": 2, "block_index": 0, "text": "户所持相同类别普通股和相同品种优先股的数量总和。", "bold": false, "spans": [{"bbox": [90, 94, 378, 106], "page_number": 2, "block_index": 0, "text": "户所持相同类别普通股和相同品种优先股的数量总和。", "bold": false}]}], "target": null, "no": {"text": "（二）", "value": "二", "int_value": 2, "is_roman": false, "bbox": [114, 117, 145, 129], "page_number": 2}}, "children": [{"id": 39, "type": "section", "parent_path": [1, 36, 38], "page_number": 3, "data": {"textlines": [{"bbox": [114, 756, 511, 768], "page_number": 3, "block_index": 0, "text": "持有多个股东账户的股东通过本所网络投票系统参与股东大会网络投票的，", "bold": false, "spans": [{"bbox": [114, 756, 511, 768], "page_number": 3, "block_index": 0, "text": "持有多个股东账户的股东通过本所网络投票系统参与股东大会网络投票的，", "bold": false}]}, {"bbox": [90, 733, 505, 745], "page_number": 3, "block_index": 0, "text": "可以通过其任一股东账户参加。投票后，视为其全部股东账户下的相同类别普通", "bold": false, "spans": [{"bbox": [90, 733, 505, 745], "page_number": 3, "block_index": 0, "text": "可以通过其任一股东账户参加。投票后，视为其全部股东账户下的相同类别普通", "bold": false}]}, {"bbox": [90, 710, 378, 722], "page_number": 3, "block_index": 0, "text": "股和相同品种优先股均已分别投出同一意见的表决票。", "bold": false, "spans": [{"bbox": [90, 710, 378, 722], "page_number": 3, "block_index": 0, "text": "股和相同品种优先股均已分别投出同一意见的表决票。", "bold": false}]}]}, "children": []}, {"id": 40, "type": "section", "parent_path": [1, 36, 38], "page_number": 3, "data": {"textlines": [{"bbox": [114, 686, 505, 698], "page_number": 3, "block_index": 0, "text": "持有多个股东账户的股东，通过多个股东账户重复进行表决的，其全部股东", "bold": false, "spans": [{"bbox": [114, 686, 505, 698], "page_number": 3, "block_index": 0, "text": "持有多个股东账户的股东，通过多个股东账户重复进行表决的，其全部股东", "bold": false}]}, {"bbox": [90, 663, 505, 675], "page_number": 3, "block_index": 0, "text": "账户下的相同类别普通股和相同品种优先股的表决意见，分别以各类别和品种股", "bold": false, "spans": [{"bbox": [90, 663, 505, 675], "page_number": 3, "block_index": 0, "text": "账户下的相同类别普通股和相同品种优先股的表决意见，分别以各类别和品种股", "bold": false}]}, {"bbox": [90, 639, 234, 651], "page_number": 3, "block_index": 0, "text": "票的第一次投票结果为准。", "bold": false, "spans": [{"bbox": [90, 639, 234, 651], "page_number": 3, "block_index": 0, "text": "票的第一次投票结果为准。", "bold": false}]}]}, "children": []}]}, {"id": 41, "type": "title", "parent_path": [1, 36], "page_number": 3, "data": {"text": "（三）同一表决权通过现场、本所网络投票平台或其他方式重复进行表决的，以第一次投票结果为准。", "textlines": [{"bbox": [114, 616, 511, 628], "page_number": 3, "block_index": 0, "text": "（三）同一表决权通过现场、本所网络投票平台或其他方式重复进行表决的，", "bold": false, "spans": [{"bbox": [114, 616, 511, 628], "page_number": 3, "block_index": 0, "text": "（三）同一表决权通过现场、本所网络投票平台或其他方式重复进行表决的，", "bold": false}]}, {"bbox": [90, 593, 222, 605], "page_number": 3, "block_index": 0, "text": "以第一次投票结果为准。", "bold": false, "spans": [{"bbox": [90, 593, 222, 605], "page_number": 3, "block_index": 0, "text": "以第一次投票结果为准。", "bold": false}]}], "target": null, "no": {"text": "（三）", "value": "三", "int_value": 3, "is_roman": false, "bbox": [114, 616, 139, 628], "page_number": 3}}, "children": []}, {"id": 42, "type": "title", "parent_path": [1, 36], "page_number": 3, "data": {"text": "（四）股东对所有议案均表决完毕才能提交。", "textlines": [{"bbox": [114, 569, 354, 581], "page_number": 3, "block_index": 0, "text": "（四）股东对所有议案均表决完毕才能提交。", "bold": false, "spans": [{"bbox": [114, 569, 354, 581], "page_number": 3, "block_index": 0, "text": "（四）股东对所有议案均表决完毕才能提交。", "bold": false}]}], "target": null, "no": {"text": "（四）", "value": "四", "int_value": 4, "is_roman": false, "bbox": [114, 569, 150, 581], "page_number": 3}}, "children": []}]}, {"id": 43, "type": "title", "parent_path": [1], "page_number": 3, "data": {"text": "四、会议出席对象", "textlines": [{"bbox": [114, 523, 210, 535], "page_number": 3, "block_index": 0, "text": "四、会议出席对象", "bold": true, "spans": [{"bbox": [114, 523, 210, 535], "page_number": 3, "block_index": 0, "text": "四、会议出席对象", "bold": true}]}], "target": null, "no": {"text": "四、", "value": "四", "int_value": 4, "is_roman": false, "bbox": [114, 523, 138, 535], "page_number": 3}}, "children": [{"id": 44, "type": "title", "parent_path": [1, 43], "page_number": 3, "data": {"text": "（一）股权登记日收市后在中国证券登记结算有限责任公司上海分公司登记在册的公司股东有权出席股东大会（具体情况详见下表），并可以以书面形式委托代理人出席会议和参加表决。该代理人不必是公司股东。", "textlines": [{"bbox": [114, 499, 505, 511], "page_number": 3, "block_index": 0, "text": "（一）股权登记日收市后在中国证券登记结算有限责任公司上海分公司登记", "bold": false, "spans": [{"bbox": [114, 499, 505, 511], "page_number": 3, "block_index": 0, "text": "（一）股权登记日收市后在中国证券登记结算有限责任公司上海分公司登记", "bold": false}]}, {"bbox": [90, 476, 505, 488], "page_number": 3, "block_index": 0, "text": "在册的公司股东有权出席股东大会（具体情况详见下表），并可以以书面形式委", "bold": false, "spans": [{"bbox": [90, 476, 505, 488], "page_number": 3, "block_index": 0, "text": "在册的公司股东有权出席股东大会（具体情况详见下表），并可以以书面形式委", "bold": false}]}, {"bbox": [90, 453, 402, 465], "page_number": 3, "block_index": 0, "text": "托代理人出席会议和参加表决。该代理人不必是公司股东。", "bold": false, "spans": [{"bbox": [90, 453, 402, 465], "page_number": 3, "block_index": 0, "text": "托代理人出席会议和参加表决。该代理人不必是公司股东。", "bold": false}]}], "target": null, "no": {"text": "（一）", "value": "一", "int_value": 1, "is_roman": false, "bbox": [114, 499, 145, 511], "page_number": 3}}, "children": [{"id": 45, "type": "table", "parent_path": [1, 43, 44], "page_number": 3, "data": {"row_num": 2, "col_num": 4, "cells": [{"text": "股份类别", "bold": true, "row_index": 0, "col_index": 0, "row_span": 1, "col_span": 1, "figures": [], "features": {}, "merged": false, "bbox": [90, 425, 202, 442], "spans": [{"bbox": [128, 430, 165, 439], "page_number": 3, "block_index": 0, "text": "股份类别", "bold": true}], "page_number": 3, "block_index": 0}, {"text": "股票代码", "bold": true, "row_index": 0, "col_index": 1, "row_span": 1, "col_span": 1, "figures": [], "features": {}, "merged": false, "bbox": [202, 425, 274, 442], "spans": [{"bbox": [221, 430, 257, 439], "page_number": 3, "block_index": 0, "text": "股票代码", "bold": true}], "page_number": 3, "block_index": 0}, {"text": "股票简称", "bold": true, "row_index": 0, "col_index": 2, "row_span": 1, "col_span": 1, "figures": [], "features": {}, "merged": false, "bbox": [274, 425, 378, 442], "spans": [{"bbox": [309, 430, 345, 439], "page_number": 3, "block_index": 0, "text": "股票简称", "bold": true}], "page_number": 3, "block_index": 0}, {"text": "股权登记日", "bold": true, "row_index": 0, "col_index": 3, "row_span": 1, "col_span": 1, "figures": [], "features": {}, "merged": false, "bbox": [378, 425, 505, 442], "spans": [{"bbox": [419, 430, 464, 439], "page_number": 3, "block_index": 0, "text": "股权登记日", "bold": true}], "page_number": 3, "block_index": 0}, {"text": "Ａ股", "bold": false, "row_index": 1, "col_index": 0, "row_span": 1, "col_span": 1, "figures": [], "features": {}, "merged": false, "bbox": [90, 407, 202, 425], "spans": [{"bbox": [137, 412, 155, 421], "page_number": 3, "block_index": 0, "text": "Ａ股", "bold": false}], "page_number": 3, "block_index": 0}, {"text": "600536", "bold": false, "row_index": 1, "col_index": 1, "row_span": 1, "col_span": 1, "figures": [], "features": {}, "merged": false, "bbox": [202, 407, 274, 425], "spans": [{"bbox": [225, 412, 252, 421], "page_number": 3, "block_index": 0, "text": "600536", "bold": false}], "page_number": 3, "block_index": 0}, {"text": "中国软件", "bold": false, "row_index": 1, "col_index": 2, "row_span": 1, "col_span": 1, "figures": [], "features": {}, "merged": false, "bbox": [274, 407, 378, 425], "spans": [{"bbox": [309, 412, 345, 421], "page_number": 3, "block_index": 0, "text": "中国软件", "bold": false}], "page_number": 3, "block_index": 0}, {"text": "2024/3/25", "bold": false, "row_index": 1, "col_index": 3, "row_span": 1, "col_span": 1, "figures": [], "features": {}, "merged": false, "bbox": [378, 407, 505, 425], "spans": [{"bbox": [422, 412, 462, 421], "page_number": 3, "block_index": 0, "text": "2024/3/25", "bold": false}], "page_number": 3, "block_index": 0}], "merged": false, "bbox": [90, 407, 505, 442], "lines": [], "features": {"v_edges": true, "border": true}, "page_number": 3, "block_index": 0}, "children": []}]}, {"id": 46, "type": "title", "parent_path": [1, 43], "page_number": 3, "data": {"text": "（二）公司董事、监事和高级管理人员。", "textlines": [{"bbox": [114, 370, 330, 382], "page_number": 3, "block_index": 0, "text": "（二）公司董事、监事和高级管理人员。", "bold": false, "spans": [{"bbox": [114, 370, 330, 382], "page_number": 3, "block_index": 0, "text": "（二）公司董事、监事和高级管理人员。", "bold": false}]}], "target": null, "no": {"text": "（二）", "value": "二", "int_value": 2, "is_roman": false, "bbox": [114, 370, 150, 382], "page_number": 3}}, "children": []}, {"id": 47, "type": "title", "parent_path": [1, 43], "page_number": 3, "data": {"text": "（三）公司聘请的律师。", "textlines": [{"bbox": [114, 347, 246, 359], "page_number": 3, "block_index": 0, "text": "（三）公司聘请的律师。", "bold": false, "spans": [{"bbox": [114, 347, 246, 359], "page_number": 3, "block_index": 0, "text": "（三）公司聘请的律师。", "bold": false}]}], "target": null, "no": {"text": "（三）", "value": "三", "int_value": 3, "is_roman": false, "bbox": [114, 347, 150, 359], "page_number": 3}}, "children": []}, {"id": 48, "type": "title", "parent_path": [1, 43], "page_number": 3, "data": {"text": "（四）其他人员", "textlines": [{"bbox": [114, 324, 198, 336], "page_number": 3, "block_index": 0, "text": "（四）其他人员", "bold": false, "spans": [{"bbox": [114, 324, 198, 336], "page_number": 3, "block_index": 0, "text": "（四）其他人员", "bold": false}]}], "target": null, "no": {"text": "（四）", "value": "四", "int_value": 4, "is_roman": false, "bbox": [114, 324, 150, 336], "page_number": 3}}, "children": []}]}, {"id": 49, "type": "title", "parent_path": [1], "page_number": 3, "data": {"text": "五、会议登记方法", "textlines": [{"bbox": [114, 278, 210, 290], "page_number": 3, "block_index": 0, "text": "五、会议登记方法", "bold": true, "spans": [{"bbox": [114, 278, 210, 290], "page_number": 3, "block_index": 0, "text": "五、会议登记方法", "bold": true}]}], "target": null, "no": {"text": "五、", "value": "五", "int_value": 5, "is_roman": false, "bbox": [114, 278, 138, 290], "page_number": 3}}, "children": [{"id": 50, "type": "title", "parent_path": [1, 49], "page_number": 3, "data": {"text": "（一）登记时间：2024年3月29日，9:00－11:30，13:30－16:00", "textlines": [{"bbox": [114, 255, 471, 267], "page_number": 3, "block_index": 0, "text": "（一）登记时间：2024年3月29日，9:00－11:30，13:30－16:00", "bold": false, "spans": [{"bbox": [114, 255, 210, 267], "page_number": 3, "block_index": 0, "text": "（一）登记时间：", "bold": false}, {"bbox": [210, 255, 234, 267], "page_number": 3, "block_index": 0, "text": "2024", "bold": false}, {"bbox": [237, 255, 249, 267], "page_number": 3, "block_index": 0, "text": "年", "bold": false}, {"bbox": [252, 255, 258, 267], "page_number": 3, "block_index": 0, "text": "3", "bold": false}, {"bbox": [261, 255, 273, 267], "page_number": 3, "block_index": 0, "text": "月", "bold": false}, {"bbox": [276, 255, 288, 267], "page_number": 3, "block_index": 0, "text": "29", "bold": false}, {"bbox": [291, 255, 315, 267], "page_number": 3, "block_index": 0, "text": "日，", "bold": false}, {"bbox": [321, 255, 345, 267], "page_number": 3, "block_index": 0, "text": "9:00", "bold": false}, {"bbox": [345, 255, 357, 267], "page_number": 3, "block_index": 0, "text": "－", "bold": false}, {"bbox": [357, 255, 387, 267], "page_number": 3, "block_index": 0, "text": "11:30", "bold": false}, {"bbox": [387, 255, 399, 267], "page_number": 3, "block_index": 0, "text": "，", "bold": false}, {"bbox": [399, 255, 429, 267], "page_number": 3, "block_index": 0, "text": "13:30", "bold": false}, {"bbox": [429, 255, 441, 267], "page_number": 3, "block_index": 0, "text": "－", "bold": false}, {"bbox": [441, 255, 471, 267], "page_number": 3, "block_index": 0, "text": "16:00", "bold": false}]}], "target": null, "no": {"text": "（一）", "value": "一", "int_value": 1, "is_roman": false, "bbox": [114, 255, 150, 267], "page_number": 3}}, "children": []}, {"id": 51, "type": "title", "parent_path": [1, 49], "page_number": 3, "data": {"text": "（二）登记地点：北京市海淀区学院南路55号中软大厦A座4层公司董事会办公室", "textlines": [{"bbox": [114, 232, 505, 244], "page_number": 3, "block_index": 0, "text": "（二）登记地点：北京市海淀区学院南路55号中软大厦A座4层公司董事", "bold": false, "spans": [{"bbox": [114, 232, 330, 244], "page_number": 3, "block_index": 0, "text": "（二）登记地点：北京市海淀区学院南路", "bold": false}, {"bbox": [333, 232, 345, 244], "page_number": 3, "block_index": 0, "text": "55", "bold": false}, {"bbox": [349, 232, 409, 244], "page_number": 3, "block_index": 0, "text": "号中软大厦", "bold": false}, {"bbox": [412, 232, 418, 244], "page_number": 3, "block_index": 0, "text": "A", "bold": false}, {"bbox": [421, 232, 433, 244], "page_number": 3, "block_index": 0, "text": "座", "bold": false}, {"bbox": [436, 232, 442, 244], "page_number": 3, "block_index": 0, "text": "4", "bold": false}, {"bbox": [446, 232, 505, 244], "page_number": 3, "block_index": 0, "text": "层公司董事", "bold": false}]}, {"bbox": [90, 208, 138, 220], "page_number": 3, "block_index": 0, "text": "会办公室", "bold": false, "spans": [{"bbox": [90, 208, 138, 220], "page_number": 3, "block_index": 0, "text": "会办公室", "bold": false}]}], "target": null, "no": {"text": "（二）", "value": "二", "int_value": 2, "is_roman": false, "bbox": [114, 232, 150, 244], "page_number": 3}}, "children": []}, {"id": 52, "type": "title", "parent_path": [1, 49], "page_number": 3, "data": {"text": "（三）登记方式：出席会议的自然人股东持本人身份证、证券账户卡办理登记手续；法人股东持营业执照复印件（加盖公章）、证券账户卡、法定代表人授权委托书及其身份证复印件、委托代理人身份证明办理出席登记。", "textlines": [{"bbox": [114, 185, 505, 197], "page_number": 3, "block_index": 0, "text": "（三）登记方式：出席会议的自然人股东持本人身份证、证券账户卡办理登", "bold": false, "spans": [{"bbox": [114, 185, 505, 197], "page_number": 3, "block_index": 0, "text": "（三）登记方式：出席会议的自然人股东持本人身份证、证券账户卡办理登", "bold": false}]}, {"bbox": [90, 162, 505, 174], "page_number": 3, "block_index": 0, "text": "记手续；法人股东持营业执照复印件（加盖公章）、证券账户卡、法定代表人授", "bold": false, "spans": [{"bbox": [90, 162, 505, 174], "page_number": 3, "block_index": 0, "text": "记手续；法人股东持营业执照复印件（加盖公章）、证券账户卡、法定代表人授", "bold": false}]}, {"bbox": [90, 138, 438, 150], "page_number": 3, "block_index": 0, "text": "权委托书及其身份证复印件、委托代理人身份证明办理出席登记。", "bold": false, "spans": [{"bbox": [90, 138, 438, 150], "page_number": 3, "block_index": 0, "text": "权委托书及其身份证复印件、委托代理人身份证明办理出席登记。", "bold": false}]}], "target": null, "no": {"text": "（三）", "value": "三", "int_value": 3, "is_roman": false, "bbox": [114, 185, 145, 197], "page_number": 3}}, "children": []}, {"id": 53, "type": "section", "parent_path": [1, 49], "page_number": 3, "data": {"textlines": [{"bbox": [114, 115, 511, 127], "page_number": 3, "block_index": 0, "text": "委托他人出席的，受托人持本人身份证、授权委托书、委托人证券帐户卡、", "bold": false, "spans": [{"bbox": [114, 115, 511, 127], "page_number": 3, "block_index": 0, "text": "委托他人出席的，受托人持本人身份证、授权委托书、委托人证券帐户卡、", "bold": false}]}, {"bbox": [90, 92, 282, 104], "page_number": 3, "block_index": 0, "text": "委托人身份证复印件办理登记手续。", "bold": false, "spans": [{"bbox": [90, 92, 282, 104], "page_number": 3, "block_index": 0, "text": "委托人身份证复印件办理登记手续。", "bold": false}]}]}, "children": []}, {"id": 54, "type": "section", "parent_path": [1, 49], "page_number": 4, "data": {"textlines": [{"bbox": [100, 756, 505, 768], "page_number": 4, "block_index": 0, "text": "异地股东可于2024年3月29日下午4:00前通过信函或传真方式进行登记（需", "bold": false, "spans": [{"bbox": [100, 756, 172, 768], "page_number": 4, "block_index": 0, "text": "异地股东可于", "bold": false}, {"bbox": [175, 756, 199, 768], "page_number": 4, "block_index": 0, "text": "2024", "bold": false}, {"bbox": [201, 756, 213, 768], "page_number": 4, "block_index": 0, "text": "年", "bold": false}, {"bbox": [216, 756, 222, 768], "page_number": 4, "block_index": 0, "text": "3", "bold": false}, {"bbox": [225, 756, 237, 768], "page_number": 4, "block_index": 0, "text": "月", "bold": false}, {"bbox": [239, 756, 251, 768], "page_number": 4, "block_index": 0, "text": "29", "bold": false}, {"bbox": [254, 756, 290, 768], "page_number": 4, "block_index": 0, "text": "日下午", "bold": false}, {"bbox": [293, 756, 317, 768], "page_number": 4, "block_index": 0, "text": "4:00", "bold": false}, {"bbox": [319, 756, 505, 768], "page_number": 4, "block_index": 0, "text": "前通过信函或传真方式进行登记（需", "bold": false}]}, {"bbox": [90, 733, 498, 745], "page_number": 4, "block_index": 0, "text": "提供前款规定的有效证件的复印件），附联系电话,并注明“股东大会”字样。", "bold": false, "spans": [{"bbox": [90, 733, 348, 745], "page_number": 4, "block_index": 0, "text": "提供前款规定的有效证件的复印件），附联系电话", "bold": false}, {"bbox": [348, 733, 354, 745], "page_number": 4, "block_index": 0, "text": ",", "bold": false}, {"bbox": [354, 733, 498, 745], "page_number": 4, "block_index": 0, "text": "并注明“股东大会”字样。", "bold": false}]}]}, "children": []}]}, {"id": 55, "type": "title", "parent_path": [1], "page_number": 4, "data": {"text": "六、其他事项", "textlines": [{"bbox": [114, 690, 201, 702], "page_number": 4, "block_index": 0, "text": "六、其他事项", "bold": true, "spans": [{"bbox": [114, 690, 138, 702], "page_number": 4, "block_index": 0, "text": "六、", "bold": true}, {"bbox": [153, 690, 201, 702], "page_number": 4, "block_index": 0, "text": "其他事项", "bold": true}]}], "target": null, "no": {"text": "六、", "value": "六", "int_value": 6, "is_roman": false, "bbox": [114, 690, 138, 702], "page_number": 4}}, "children": [{"id": 56, "type": "title", "parent_path": [1, 55], "page_number": 4, "data": {"text": "（一）出席会议的股东或代理人交通及食宿费自理", "textlines": [{"bbox": [114, 667, 378, 679], "page_number": 4, "block_index": 0, "text": "（一）出席会议的股东或代理人交通及食宿费自理", "bold": false, "spans": [{"bbox": [114, 667, 378, 679], "page_number": 4, "block_index": 0, "text": "（一）出席会议的股东或代理人交通及食宿费自理", "bold": false}]}], "target": null, "no": {"text": "（一）", "value": "一", "int_value": 1, "is_roman": false, "bbox": [114, 667, 150, 679], "page_number": 4}}, "children": []}, {"id": 57, "type": "title", "parent_path": [1, 55], "page_number": 4, "data": {"text": "（二）会议联系方式", "textlines": [{"bbox": [114, 644, 222, 656], "page_number": 4, "block_index": 0, "text": "（二）会议联系方式", "bold": false, "spans": [{"bbox": [114, 644, 222, 656], "page_number": 4, "block_index": 0, "text": "（二）会议联系方式", "bold": false}]}], "target": null, "no": {"text": "（二）", "value": "二", "int_value": 2, "is_roman": false, "bbox": [114, 644, 150, 656], "page_number": 4}}, "children": [{"id": 58, "type": "section", "parent_path": [1, 55, 57], "page_number": 4, "data": {"textlines": [{"bbox": [114, 620, 492, 632], "page_number": 4, "block_index": 0, "text": "联系地址：北京市海淀区学院南路55号中软大厦A座4层董事会办公室", "bold": false, "spans": [{"bbox": [114, 620, 294, 632], "page_number": 4, "block_index": 0, "text": "联系地址：北京市海淀区学院南路", "bold": false}, {"bbox": [297, 620, 309, 632], "page_number": 4, "block_index": 0, "text": "55", "bold": false}, {"bbox": [312, 620, 372, 632], "page_number": 4, "block_index": 0, "text": "号中软大厦", "bold": false}, {"bbox": [375, 620, 381, 632], "page_number": 4, "block_index": 0, "text": "A", "bold": false}, {"bbox": [384, 620, 396, 632], "page_number": 4, "block_index": 0, "text": "座", "bold": false}, {"bbox": [399, 620, 405, 632], "page_number": 4, "block_index": 0, "text": "4", "bold": false}, {"bbox": [408, 620, 492, 632], "page_number": 4, "block_index": 0, "text": "层董事会办公室", "bold": false}]}]}, "children": []}, {"id": 59, "type": "section", "parent_path": [1, 55, 57], "page_number": 4, "data": {"textlines": [{"bbox": [114, 597, 264, 609], "page_number": 4, "block_index": 0, "text": "联系人：郑海雯管丹玥", "bold": false, "spans": [{"bbox": [114, 597, 126, 609], "page_number": 4, "block_index": 0, "text": "联", "bold": false}, {"bbox": [132, 597, 144, 609], "page_number": 4, "block_index": 0, "text": "系", "bold": false}, {"bbox": [150, 597, 210, 609], "page_number": 4, "block_index": 0, "text": "人：郑海雯", "bold": false}, {"bbox": [228, 597, 264, 609], "page_number": 4, "block_index": 0, "text": "管丹玥", "bold": false}]}]}, "children": []}, {"id": 60, "type": "section", "parent_path": [1, 55, 57], "page_number": 4, "data": {"textlines": [{"bbox": [114, 574, 246, 586], "page_number": 4, "block_index": 0, "text": "电话：010-62158879", "bold": false, "spans": [{"bbox": [114, 574, 126, 586], "page_number": 4, "block_index": 0, "text": "电", "bold": false}, {"bbox": [150, 574, 174, 586], "page_number": 4, "block_index": 0, "text": "话：", "bold": false}, {"bbox": [174, 574, 246, 586], "page_number": 4, "block_index": 0, "text": "010-62158879", "bold": false}]}]}, "children": []}, {"id": 61, "type": "section", "parent_path": [1, 55, 57], "page_number": 4, "data": {"textlines": [{"bbox": [114, 550, 246, 562], "page_number": 4, "block_index": 0, "text": "传真：010-62169523", "bold": false, "spans": [{"bbox": [114, 550, 126, 562], "page_number": 4, "block_index": 0, "text": "传", "bold": false}, {"bbox": [150, 550, 174, 562], "page_number": 4, "block_index": 0, "text": "真：", "bold": false}, {"bbox": [174, 550, 246, 562], "page_number": 4, "block_index": 0, "text": "010-62169523", "bold": false}]}]}, "children": []}, {"id": 62, "type": "section", "parent_path": [1, 55, 57], "page_number": 4, "data": {"textlines": [{"bbox": [114, 527, 210, 539], "page_number": 4, "block_index": 0, "text": "邮政编码：100081", "bold": false, "spans": [{"bbox": [114, 527, 174, 539], "page_number": 4, "block_index": 0, "text": "邮政编码：", "bold": false}, {"bbox": [174, 527, 210, 539], "page_number": 4, "block_index": 0, "text": "100081", "bold": false}]}]}, "children": []}, {"id": 63, "type": "section", "parent_path": [1, 55, 57], "page_number": 4, "data": {"textlines": [{"bbox": [114, 480, 174, 492], "page_number": 4, "block_index": 0, "text": "特此公告。", "bold": false, "spans": [{"bbox": [114, 480, 174, 492], "page_number": 4, "block_index": 0, "text": "特此公告。", "bold": false}]}]}, "children": []}, {"id": 64, "type": "section", "parent_path": [1, 55, 57], "page_number": 4, "data": {"textlines": [{"bbox": [289, 441, 505, 453], "page_number": 4, "block_index": 0, "text": "中国软件与技术服务股份有限公司董事会", "bold": false, "spans": [{"bbox": [289, 441, 505, 453], "page_number": 4, "block_index": 0, "text": "中国软件与技术服务股份有限公司董事会", "bold": false}]}, {"bbox": [412, 418, 505, 430], "page_number": 4, "block_index": 0, "text": "2024年3月16日", "bold": false, "spans": [{"bbox": [412, 418, 436, 430], "page_number": 4, "block_index": 0, "text": "2024", "bold": false}, {"bbox": [439, 418, 451, 430], "page_number": 4, "block_index": 0, "text": "年", "bold": false}, {"bbox": [454, 418, 460, 430], "page_number": 4, "block_index": 0, "text": "3", "bold": false}, {"bbox": [463, 418, 475, 430], "page_number": 4, "block_index": 0, "text": "月", "bold": false}, {"bbox": [478, 418, 490, 430], "page_number": 4, "block_index": 0, "text": "16", "bold": false}, {"bbox": [493, 418, 505, 430], "page_number": 4, "block_index": 0, "text": "日", "bold": false}]}]}, "children": []}, {"id": 65, "type": "section", "parent_path": [1, 55, 57], "page_number": 4, "data": {"textlines": [{"bbox": [90, 371, 195, 383], "page_number": 4, "block_index": 0, "text": "附件1：授权委托书", "bold": false, "spans": [{"bbox": [90, 371, 114, 383], "page_number": 4, "block_index": 0, "text": "附件", "bold": false}, {"bbox": [117, 371, 123, 383], "page_number": 4, "block_index": 0, "text": "1", "bold": false}, {"bbox": [123, 371, 195, 383], "page_number": 4, "block_index": 0, "text": "：授权委托书", "bold": false}]}]}, "children": []}]}]}], "chapter_type": "cover"}, {"id": 66, "type": "title", "parent_path": [], "page_number": 5, "data": {"text": "附件1：授权委托书", "textlines": [{"bbox": [90, 745, 195, 757], "page_number": 5, "block_index": 0, "text": "附件1：授权委托书", "bold": true, "spans": [{"bbox": [90, 745, 114, 757], "page_number": 5, "block_index": 0, "text": "附件", "bold": true}, {"bbox": [117, 745, 123, 757], "page_number": 5, "block_index": 0, "text": "1", "bold": true}, {"bbox": [123, 745, 195, 757], "page_number": 5, "block_index": 0, "text": "：授权委托书", "bold": true}]}], "target": null, "no": null}, "children": [{"id": 67, "type": "section", "parent_path": [66], "page_number": 5, "data": {"textlines": [{"bbox": [234, 678, 364, 704], "page_number": 5, "block_index": 0, "text": "授权委托书", "bold": true, "spans": [{"bbox": [234, 678, 364, 704], "page_number": 5, "block_index": 0, "text": "授权委托书", "bold": true}]}]}, "children": []}, {"id": 68, "type": "section", "parent_path": [66], "page_number": 5, "data": {"textlines": [{"bbox": [90, 641, 282, 653], "page_number": 5, "block_index": 0, "text": "中国软件与技术服务股份有限公司：", "bold": false, "spans": [{"bbox": [90, 641, 282, 653], "page_number": 5, "block_index": 0, "text": "中国软件与技术服务股份有限公司：", "bold": false}]}]}, "children": []}, {"id": 69, "type": "section", "parent_path": [66], "page_number": 5, "data": {"textlines": [{"bbox": [114, 602, 504, 614], "page_number": 5, "block_index": 0, "text": "兹委托先生（女士）代表本单位（或本人）出席2024年", "bold": false, "spans": [{"bbox": [114, 602, 150, 614], "page_number": 5, "block_index": 0, "text": "兹委托", "bold": false}, {"bbox": [246, 602, 462, 614], "page_number": 5, "block_index": 0, "text": "先生（女士）代表本单位（或本人）出席", "bold": false}, {"bbox": [465, 602, 489, 614], "page_number": 5, "block_index": 0, "text": "2024", "bold": false}, {"bbox": [492, 602, 504, 614], "page_number": 5, "block_index": 0, "text": "年", "bold": false}]}, {"bbox": [90, 579, 477, 591], "page_number": 5, "block_index": 0, "text": "4月1日召开的贵公司2024年第四次临时股东大会，并代为行使表决权。", "bold": false, "spans": [{"bbox": [90, 579, 96, 591], "page_number": 5, "block_index": 0, "text": "4", "bold": false}, {"bbox": [99, 579, 111, 591], "page_number": 5, "block_index": 0, "text": "月", "bold": false}, {"bbox": [114, 579, 120, 591], "page_number": 5, "block_index": 0, "text": "1", "bold": false}, {"bbox": [123, 579, 207, 591], "page_number": 5, "block_index": 0, "text": "日召开的贵公司", "bold": false}, {"bbox": [210, 579, 234, 591], "page_number": 5, "block_index": 0, "text": "2024", "bold": false}, {"bbox": [237, 579, 477, 591], "page_number": 5, "block_index": 0, "text": "年第四次临时股东大会，并代为行使表决权。", "bold": false}]}]}, "children": []}, {"id": 70, "type": "section", "parent_path": [66], "page_number": 5, "data": {"textlines": [{"bbox": [90, 538, 198, 550], "page_number": 5, "block_index": 0, "text": "委托人持普通股数：", "bold": false, "spans": [{"bbox": [90, 538, 198, 550], "page_number": 5, "block_index": 0, "text": "委托人持普通股数：", "bold": false}]}]}, "children": []}, {"id": 71, "type": "section", "parent_path": [66], "page_number": 5, "data": {"textlines": [{"bbox": [90, 500, 198, 512], "page_number": 5, "block_index": 0, "text": "委托人股东帐户号：", "bold": false, "spans": [{"bbox": [90, 500, 198, 512], "page_number": 5, "block_index": 0, "text": "委托人股东帐户号：", "bold": false}]}]}, "children": []}, {"id": 72, "type": "table", "parent_path": [66], "page_number": 5, "data": {"row_num": 2, "col_num": 5, "cells": [{"text": "序号", "bold": false, "row_index": 0, "col_index": 0, "row_span": 1, "col_span": 1, "figures": [], "features": {}, "merged": false, "bbox": [90, 451, 132, 464], "spans": [{"bbox": [102, 454, 120, 463], "page_number": 5, "block_index": 0, "text": "序号", "bold": false}], "page_number": 5, "block_index": 0}, {"text": "非累积投票议案名称", "bold": false, "row_index": 0, "col_index": 1, "row_span": 1, "col_span": 1, "figures": [], "features": {}, "merged": false, "bbox": [132, 451, 338, 464], "spans": [{"bbox": [195, 454, 276, 463], "page_number": 5, "block_index": 0, "text": "非累积投票议案名称", "bold": false}], "page_number": 5, "block_index": 0}, {"text": "同意", "bold": false, "row_index": 0, "col_index": 2, "row_span": 1, "col_span": 1, "figures": [], "features": {}, "merged": false, "bbox": [338, 451, 392, 464], "spans": [{"bbox": [357, 454, 375, 463], "page_number": 5, "block_index": 0, "text": "同意", "bold": false}], "page_number": 5, "block_index": 0}, {"text": "反对", "bold": false, "row_index": 0, "col_index": 3, "row_span": 1, "col_span": 1, "figures": [], "features": {}, "merged": false, "bbox": [392, 451, 447, 464], "spans": [{"bbox": [411, 454, 429, 463], "page_number": 5, "block_index": 0, "text": "反对", "bold": false}], "page_number": 5, "block_index": 0}, {"text": "弃权", "bold": false, "row_index": 0, "col_index": 4, "row_span": 1, "col_span": 1, "figures": [], "features": {}, "merged": false, "bbox": [447, 451, 492, 464], "spans": [{"bbox": [460, 454, 478, 463], "page_number": 5, "block_index": 0, "text": "弃权", "bold": false}], "page_number": 5, "block_index": 0}, {"text": "1", "bold": false, "row_index": 1, "col_index": 0, "row_span": 1, "col_span": 1, "figures": [], "features": {}, "merged": false, "bbox": [90, 427, 132, 451], "spans": [{"bbox": [109, 435, 114, 444], "page_number": 5, "block_index": 0, "text": "1", "bold": false}], "page_number": 5, "block_index": 0}, {"text": "关于参股公司其他股东转让股权并增资扩股公司放弃相关权利的议案", "bold": false, "row_index": 1, "col_index": 1, "row_span": 1, "col_span": 1, "figures": [], "features": {}, "merged": false, "bbox": [132, 427, 338, 451], "spans": [{"bbox": [138, 441, 334, 450], "page_number": 5, "block_index": 0, "text": "关于参股公司其他股东转让股权并增资扩股公司", "bold": false}, {"bbox": [138, 429, 219, 438], "page_number": 5, "block_index": 0, "text": "放弃相关权利的议案", "bold": false}], "page_number": 5, "block_index": 0}, {"text": "", "bold": false, "row_index": 1, "col_index": 2, "row_span": 1, "col_span": 1, "figures": [], "features": {}, "merged": false, "bbox": [338, 427, 392, 451], "spans": [], "page_number": 5, "block_index": 0}, {"text": "", "bold": false, "row_index": 1, "col_index": 3, "row_span": 1, "col_span": 1, "figures": [], "features": {}, "merged": false, "bbox": [392, 427, 447, 451], "spans": [], "page_number": 5, "block_index": 0}, {"text": "", "bold": false, "row_index": 1, "col_index": 4, "row_span": 1, "col_span": 1, "figures": [], "features": {}, "merged": false, "bbox": [447, 427, 492, 451], "spans": [], "page_number": 5, "block_index": 0}], "merged": false, "bbox": [90, 427, 492, 464], "lines": [], "features": {"v_edges": true, "border": true}, "page_number": 5, "block_index": 0}, "children": []}, {"id": 73, "type": "section", "parent_path": [66], "page_number": 5, "data": {"textlines": [{"bbox": [90, 364, 384, 376], "page_number": 5, "block_index": 0, "text": "委托人签名（盖章）：受托人签名：", "bold": false, "spans": [{"bbox": [90, 364, 210, 376], "page_number": 5, "block_index": 0, "text": "委托人签名（盖章）：", "bold": false}, {"bbox": [312, 364, 384, 376], "page_number": 5, "block_index": 0, "text": "受托人签名：", "bold": false}]}]}, "children": []}, {"id": 74, "type": "section", "parent_path": [66], "page_number": 5, "data": {"textlines": [{"bbox": [90, 327, 408, 339], "page_number": 5, "block_index": 0, "text": "委托人身份证号：受托人身份证号：", "bold": false, "spans": [{"bbox": [90, 327, 186, 339], "page_number": 5, "block_index": 0, "text": "委托人身份证号：", "bold": false}, {"bbox": [312, 327, 408, 339], "page_number": 5, "block_index": 0, "text": "受托人身份证号：", "bold": false}]}]}, "children": []}, {"id": 75, "type": "section", "parent_path": [66], "page_number": 5, "data": {"textlines": [{"bbox": [306, 289, 474, 301], "page_number": 5, "block_index": 0, "text": "委托日期：年月日", "bold": false, "spans": [{"bbox": [306, 289, 366, 301], "page_number": 5, "block_index": 0, "text": "委托日期：", "bold": false}, {"bbox": [390, 289, 402, 301], "page_number": 5, "block_index": 0, "text": "年", "bold": false}, {"bbox": [426, 289, 438, 301], "page_number": 5, "block_index": 0, "text": "月", "bold": false}, {"bbox": [462, 289, 474, 301], "page_number": 5, "block_index": 0, "text": "日", "bold": false}]}, {"bbox": [90, 252, 126, 264], "page_number": 5, "block_index": 0, "text": "备注：", "bold": false, "spans": [{"bbox": [90, 252, 126, 264], "page_number": 5, "block_index": 0, "text": "备注：", "bold": false}]}]}, "children": []}, {"id": 76, "type": "section", "parent_path": [66], "page_number": 5, "data": {"textlines": [{"bbox": [90, 214, 486, 226], "page_number": 5, "block_index": 0, "text": "委托人应当在委托书中“同意”、“反对”或“弃权”意向中选择一个并打", "bold": false, "spans": [{"bbox": [90, 214, 486, 226], "page_number": 5, "block_index": 0, "text": "委托人应当在委托书中“同意”、“反对”或“弃权”意向中选择一个并打", "bold": false}]}, {"bbox": [90, 191, 498, 203], "page_number": 5, "block_index": 0, "text": "“√”，对于委托人在本授权委托书中未作具体指示的，受托人有权按自己的", "bold": false, "spans": [{"bbox": [90, 191, 498, 203], "page_number": 5, "block_index": 0, "text": "“√”，对于委托人在本授权委托书中未作具体指示的，受托人有权按自己的", "bold": false}]}, {"bbox": [90, 168, 174, 180], "page_number": 5, "block_index": 0, "text": "意愿进行表决。", "bold": false, "spans": [{"bbox": [90, 168, 174, 180], "page_number": 5, "block_index": 0, "text": "意愿进行表决。", "bold": false}]}]}, "children": []}], "chapter_type": "normal"}]}}, "outline": {"root": {"id": 0, "title": "", "level": 0, "page_number": 0, "bbox": [0, 0, 0, 0], "target": null, "children": [{"id": 1, "title": "<封面>", "level": 1, "page_number": 1, "bbox": [90, 768, 486, 768], "target": null, "children": [{"id": 9, "title": "一、召开会议的基本情况", "level": 2, "page_number": 1, "bbox": [114, 474, 261, 486], "target": null, "children": [{"id": 10, "title": "（一）股东大会类型和届次", "level": 3, "page_number": 1, "bbox": [114, 450, 258, 462], "target": null, "children": []}, {"id": 12, "title": "（二）股东大会召集人：董事会", "level": 3, "page_number": 1, "bbox": [114, 404, 282, 416], "target": null, "children": []}, {"id": 13, "title": "（三）投票方式：本次股东大会所采用的表决方式是现场投票和网络投票相结合的方式", "level": 3, "page_number": 1, "bbox": [114, 380, 505, 392], "target": null, "children": []}, {"id": 14, "title": "（四）现场会议召开的日期、时间和地点", "level": 3, "page_number": 1, "bbox": [114, 334, 330, 346], "target": null, "children": []}, {"id": 17, "title": "（五）网络投票的系统、起止日期和投票时间。", "level": 3, "page_number": 1, "bbox": [114, 264, 366, 276], "target": null, "children": []}, {"id": 22, "title": "（六）融资融券、转融通、约定购回业务账户和沪股通投资者的投票程序", "level": 3, "page_number": 1, "bbox": [114, 100, 498, 112], "target": null, "children": []}, {"id": 24, "title": "（七）涉及公开征集股东投票权无", "level": 3, "page_number": 2, "bbox": [114, 686, 282, 698], "target": null, "children": []}]}, {"id": 25, "title": "二、会议审议事项", "level": 2, "page_number": 2, "bbox": [114, 639, 211, 651], "target": null, "children": [{"id": 28, "title": "1、各议案已披露的时间和披露媒体", "level": 3, "page_number": 2, "bbox": [111, 491, 297, 503], "target": null, "children": []}, {"id": 31, "title": "2、特别决议议案：无", "level": 3, "page_number": 2, "bbox": [114, 398, 228, 410], "target": null, "children": []}, {"id": 32, "title": "3、对中小投资者单独计票的议案：无", "level": 3, "page_number": 2, "bbox": [114, 374, 312, 386], "target": null, "children": []}, {"id": 33, "title": "4、涉及关联股东回避表决的议案：无", "level": 3, "page_number": 2, "bbox": [114, 351, 312, 363], "target": null, "children": []}, {"id": 35, "title": "5、涉及优先股股东参与表决的议案：无", "level": 3, "page_number": 2, "bbox": [114, 304, 324, 316], "target": null, "children": []}]}, {"id": 36, "title": "三、股东大会投票注意事项", "level": 2, "page_number": 2, "bbox": [114, 258, 260, 270], "target": null, "children": [{"id": 37, "title": "（一）本公司股东通过上海证券交易所股东大会网络投票系统行使表决权的，既可以登陆交易系统投票平台（通过指定交易的证券公司交易终端）进行投票，也可以登陆互联网投票平台（网址：vote.sseinfo.com）进行投票。首次登陆互联网投票平台进行投票的，投资者需要完成股东身份认证。具体操作请见互联网投票平台网站说明。", "level": 3, "page_number": 2, "bbox": [114, 234, 517, 246], "target": null, "children": []}, {"id": 38, "title": "（二）持有多个股东账户的股东，可行使的表决权数量是其名下全部股东账户所持相同类别普通股和相同品种优先股的数量总和。", "level": 3, "page_number": 2, "bbox": [114, 117, 505, 129], "target": null, "children": []}, {"id": 41, "title": "（三）同一表决权通过现场、本所网络投票平台或其他方式重复进行表决的，以第一次投票结果为准。", "level": 3, "page_number": 3, "bbox": [114, 616, 511, 628], "target": null, "children": []}, {"id": 42, "title": "（四）股东对所有议案均表决完毕才能提交。", "level": 3, "page_number": 3, "bbox": [114, 569, 354, 581], "target": null, "children": []}]}, {"id": 43, "title": "四、会议出席对象", "level": 2, "page_number": 3, "bbox": [114, 523, 210, 535], "target": null, "children": [{"id": 44, "title": "（一）股权登记日收市后在中国证券登记结算有限责任公司上海分公司登记在册的公司股东有权出席股东大会（具体情况详见下表），并可以以书面形式委托代理人出席会议和参加表决。该代理人不必是公司股东。", "level": 3, "page_number": 3, "bbox": [114, 499, 505, 511], "target": null, "children": []}, {"id": 46, "title": "（二）公司董事、监事和高级管理人员。", "level": 3, "page_number": 3, "bbox": [114, 370, 330, 382], "target": null, "children": []}, {"id": 47, "title": "（三）公司聘请的律师。", "level": 3, "page_number": 3, "bbox": [114, 347, 246, 359], "target": null, "children": []}, {"id": 48, "title": "（四）其他人员", "level": 3, "page_number": 3, "bbox": [114, 324, 198, 336], "target": null, "children": []}]}, {"id": 49, "title": "五、会议登记方法", "level": 2, "page_number": 3, "bbox": [114, 278, 210, 290], "target": null, "children": [{"id": 50, "title": "（一）登记时间：2024年3月29日，9:00－11:30，13:30－16:00", "level": 3, "page_number": 3, "bbox": [114, 255, 471, 267], "target": null, "children": []}, {"id": 51, "title": "（二）登记地点：北京市海淀区学院南路55号中软大厦A座4层公司董事会办公室", "level": 3, "page_number": 3, "bbox": [114, 232, 505, 244], "target": null, "children": []}, {"id": 52, "title": "（三）登记方式：出席会议的自然人股东持本人身份证、证券账户卡办理登记手续；法人股东持营业执照复印件（加盖公章）、证券账户卡、法定代表人授权委托书及其身份证复印件、委托代理人身份证明办理出席登记。", "level": 3, "page_number": 3, "bbox": [114, 185, 505, 197], "target": null, "children": []}]}, {"id": 55, "title": "六、其他事项", "level": 2, "page_number": 4, "bbox": [114, 690, 201, 702], "target": null, "children": [{"id": 56, "title": "（一）出席会议的股东或代理人交通及食宿费自理", "level": 3, "page_number": 4, "bbox": [114, 667, 378, 679], "target": null, "children": []}, {"id": 57, "title": "（二）会议联系方式", "level": 3, "page_number": 4, "bbox": [114, 644, 222, 656], "target": null, "children": []}]}]}, {"id": 66, "title": "附件1：授权委托书", "level": 1, "page_number": 5, "bbox": [90, 745, 195, 757], "target": null, "children": []}]}}, "feature": null, "execute": {"python": "pypy", "elapsed_time": 0.1534}}