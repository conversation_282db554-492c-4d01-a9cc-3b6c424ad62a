#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@File    : test_api_parser.py
<AUTHOR> XExtract Team
@Date    : 2023-05-08
@Desc    : Unit tests for APIDocumentParser class
"""

import os
import sys
import json
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
from src.xextract.parsers.api_parser import APIDocumentParser
from src.server.api.document_api import DocumentAPIClient


class TestAPIDocumentParser:
    """Test suite for APIDocumentParser class."""

    @pytest.fixture
    def mock_client(self):
        """Create a mock DocumentAPIClient."""
        with patch("src.xextract.parsers.api_parser.DocumentAPIClient") as mock:
            # Setup mock
            mock_instance = MagicMock()
            mock.return_value = mock_instance
            
            # Setup pdf2doc method
            mock_instance.pdf2doc = AsyncMock()
            mock_instance.pdf2doc.return_value = {"pages": [{"text": "Test content"}]}
            
            yield mock_instance

    @pytest.fixture
    def parser(self, mock_client):
        """Create an APIDocumentParser instance for testing."""
        parser = APIDocumentParser(
            api_base_url="http://test-api.example.com",
            api_key="test-api-key",
            timeout=30,
            max_retries=2
        )
        # Replace the client with our mock
        parser.client = mock_client
        return parser

    def test_init(self):
        """Test initialization of APIDocumentParser."""
        # Test with explicit parameters
        with patch("src.xextract.parsers.api_parser.DocumentAPIClient") as mock:
            parser = APIDocumentParser(
                api_base_url="http://test-api.example.com",
                api_key="test-api-key",
                timeout=30,
                max_retries=2
            )
            
            # Verify DocumentAPIClient was called with correct parameters
            mock.assert_called_once_with(
                api_base_url="http://test-api.example.com",
                api_key="test-api-key",
                timeout=30,
                max_retries=2
            )

    @pytest.mark.asyncio
    async def test_parse_pdf(self, parser, mock_client, tmp_path):
        """Test parse_pdf method."""
        # Create a test PDF file
        pdf_path = tmp_path / "test.pdf"
        pdf_path.write_bytes(b"%PDF-1.5\n%Test PDF file")
        
        # Test parse_pdf without output_path
        result = await parser.parse_pdf(pdf_path)
        
        # Verify result
        assert result == {"pages": [{"text": "Test content"}]}
        
        # Verify client method was called with correct parameters
        mock_client.pdf2doc.assert_called_once()
        call_args = mock_client.pdf2doc.call_args[1]
        assert call_args["file_path"] == pdf_path
        assert isinstance(call_args["params"], dict)
        
        # Reset mock
        mock_client.pdf2doc.reset_mock()
        
        # Test parse_pdf with output_path
        output_path = tmp_path / "output.json"
        
        # Mock the file read operation
        with patch("builtins.open", MagicMock()) as mock_open:
            mock_file = MagicMock()
            mock_open.return_value.__enter__.return_value = mock_file
            mock_file.read.return_value = json.dumps({"pages": [{"text": "Test content"}]})
            
            # Mock json.load
            with patch("json.load", return_value={"pages": [{"text": "Test content"}]}):
                result = await parser.parse_pdf(pdf_path, output_path=output_path)
        
        # Verify result
        assert result == {"pages": [{"text": "Test content"}]}
        
        # Verify client method was called with correct parameters
        mock_client.pdf2doc.assert_called_once()
        call_args = mock_client.pdf2doc.call_args[1]
        assert call_args["file_path"] == pdf_path
        assert call_args["output_path"] == output_path
        assert isinstance(call_args["params"], dict)

    def test_to_markdown(self, parser):
        """Test to_markdown method."""
        # Mock DocJsonParser
        with patch("src.xextract.parsers.api_parser.DocJsonParser") as mock:
            mock_instance = MagicMock()
            mock.return_value = mock_instance
            mock_instance.to_markdown.return_value = "# Test Markdown"
            
            # Call to_markdown
            doc_content = {"pages": [{"text": "Test content"}]}
            result = parser.to_markdown(doc_content)
            
            # Verify result
            assert result == "# Test Markdown"
            
            # Verify DocJsonParser was called with correct parameters
            mock.assert_called_once_with(doc_content=doc_content)
            mock_instance.to_markdown.assert_called_once()

    def test_to_text(self, parser):
        """Test to_text method."""
        # Mock DocJsonParser
        with patch("src.xextract.parsers.api_parser.DocJsonParser") as mock:
            mock_instance = MagicMock()
            mock.return_value = mock_instance
            mock_instance.to_text.return_value = "Test text content"
            
            # Call to_text
            doc_content = {"pages": [{"text": "Test content"}]}
            result = parser.to_text(doc_content)
            
            # Verify result
            assert result == "Test text content"
            
            # Verify DocJsonParser was called with correct parameters
            mock.assert_called_once_with(doc_content=doc_content)
            mock_instance.to_text.assert_called_once()
