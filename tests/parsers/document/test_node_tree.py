#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@File    : test_node_tree.py
<AUTHOR> lhh
@Date    : 2025-05-12 01:23:43
@Desc    : 节点树测试
"""


# import pytest
import os
from xextract.utils import read_json
from xextract.parsers import <PERSON><PERSON>ttr, NodeTree, DocJson, DocJsonAnalyzer


def test_node_tree():
    # file_name = "tests/data/doc.json"
    file_path = "tests/output/600760_20250401_2NFF.json"
    file_name = os.path.basename(file_path)
    outputname = f"{os.path.splitext(file_name)[0]}"
    output_node_tree = f"output/{outputname}.json"
    output2md = f"output/{outputname}.md"
    output2csv = f"output/{outputname}.csv"  
    # doc_json = read_json(file_name)
    # print(doc_json)
    analyzer = DocJsonAnalyzer().analyze(file_path)
    # node_tree = NodeTree(file_name, doc_meta=doc_json.get(DocAttr.META, {}),  pdf_info=doc_json.get(DocJson.PDF_INFO, {}),)
    # print(analyzer.doc_to_dict())
    # print(analyzer.jsonify())
    # analyzer.to_db_json(output_node_tree)
    analyzer.get_doc_table()
    analyzer.to_md(output2md)
    analyzer.to_csv(output2csv)

if __name__ == "__main__":
    test_node_tree()
