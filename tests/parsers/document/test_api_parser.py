
import os
import sys
import asyncio
import pytest
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = str(Path(__file__).parent.parent.parent.parent)
sys.path.insert(0, project_root)

from src.xextract.parsers.api_parser import APIDocumentParser


async def test_parser_pdf():
    input_pdf = "tests/data/600760_20250401_2NFF.pdf"
    output_dir = "tests/output"

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 构建输出文件路径
    pdf_name = os.path.basename(input_pdf)
    output_file = os.path.join(output_dir, f"{os.path.splitext(pdf_name)[0]}.json")

    parser = APIDocumentParser(
        api_base_url="http://api.memect.cn:6111",
        timeout=30,
        max_retries=2
    )
    result = await parser.parse_pdf(input_pdf, output_file)

    print(result)
    return result


if __name__ == "__main__":
    # 使用异步运行时运行测试
    asyncio.run(test_parser_pdf())