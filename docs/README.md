# XExtract 架构文档

本目录包含 XExtract 提取引擎的架构设计文档。

## 文档索引

1. [核心架构](xextract_core_architecture.md) - 提取引擎的核心架构和数据流
2. [架构概览](architecture.md) - 系统整体架构和主要组件
3. [组件关系图](component_diagram.md) - 详细的组件关系和数据流程
4. [部署架构](deployment_architecture.md) - 不同场景下的部署方案

## 架构设计原则

XExtract 的架构设计遵循以下原则：

1. **模块化设计** - 系统被分解为独立的模块，每个模块负责特定的功能
2. **可扩展性** - 系统设计允许轻松添加新的提取器、大模型和处理器
3. **可测试性** - 组件间的清晰边界使得单元测试和集成测试更加容易
4. **可配置性** - 系统的行为可以通过配置文件和环境变量进行调整
5. **可维护性** - 代码组织和文档使得系统易于理解和维护

## 核心功能

XExtract 提供以下核心功能：

1. **多源文档处理** - 支持 PDF、文本等多种文档格式
2. **基于模式的提取** - 使用结构化模式定义提取字段和规则
3. **混合提取策略** - 结合正则表达式和大模型的提取能力
4. **置信度评分** - 为提取结果提供可靠性评估
5. **灵活的处理管道** - 支持自定义的预处理和后处理逻辑

## 技术栈

XExtract 使用以下技术栈：

- **Python** - 主要开发语言
- **PyPDF2/pypdf** - PDF 文档处理
- **OpenAI/Gemini API** - 大模型集成
- **Pydantic** - 数据验证和设置管理
- **asyncio** - 异步处理支持
- **httpx** - HTTP 客户端

## 未来规划

1. **更多提取器** - 添加对 Word、Excel 等文档格式的支持
2. **更多大模型** - 集成更多的大语言模型
3. **分布式处理** - 支持分布式文档处理
4. **Web 界面** - 提供可视化的提取配置和管理界面
5. **监控和日志** - 增强系统监控和日志功能
