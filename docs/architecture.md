# XExtract 架构设计

## 架构概览

XExtract 是一个基于规则和大模型的文档信息提取引擎，支持多场景文档处理，特别是 PDF 文档的结构化信息提取。系统采用模块化设计，将提取逻辑、模型管理、置信度计算等功能分离，以提高代码的可维护性和扩展性。

## 架构图

```mermaid
graph TD
    Client[客户端应用] --> API[API 接口层]
    API --> ExtractEngine[提取引擎 ExtractEngine]
    
    %% 核心组件
    ExtractEngine --> SchemaManager[模式管理器]
    ExtractEngine --> Extractors[文档提取器]
    ExtractEngine --> LLMProvider[大模型提供者]
    ExtractEngine --> ConfidenceScorer[置信度评分器]
    ExtractEngine --> OutputFormatter[输出格式化器]
    
    %% 文档提取器
    Extractors --> PDFExtractor[PDF提取器]
    Extractors --> TextExtractor[文本提取器]
    
    %% 大模型提供者
    LLMProvider --> OpenAILLM[OpenAI LLM]
    LLMProvider --> GeminiLLM[Gemini LLM]
    
    %% 模式管理
    SchemaManager --> SchemaLoader[模式加载器]
    SchemaManager --> SchemaValidator[模式验证器]
    
    %% 置信度评分
    ConfidenceScorer --> RegexConfidence[正则表达式置信度]
    ConfidenceScorer --> LLMConfidence[大模型置信度]
    ConfidenceScorer --> HybridConfidence[混合置信度]
    
    %% 处理器
    ExtractEngine --> Processors[处理器]
    Processors --> PreProcessors[预处理器]
    Processors --> PostProcessors[后处理器]
    
    %% 存储
    SchemaManager --> SchemaStorage[模式存储]
    SchemaStorage --> FileStorage[文件存储]
    
    %% 配置
    Config[配置管理] --> ExtractEngine
    Config --> LLMProvider
    Config --> Extractors
```

## 核心组件说明

### 1. 提取引擎 (ExtractEngine)

提取引擎是系统的核心组件，负责协调各个模块完成信息提取任务。它接收输入文档和提取模式，调用相应的提取器和大模型，最终生成结构化的提取结果。

主要功能：
- 文档处理与文本提取
- 字段提取策略选择与执行
- 结果整合与格式化

### 2. 模式管理 (Schema Management)

模式定义了需要从文档中提取的字段结构和提取规则。模式管理器负责加载、验证和管理这些提取模式。

主要组件：
- **Schema**: 定义提取模式的结构
- **SchemaField**: 定义单个字段的提取规则
- **SchemaLoader**: 从文件或数据库加载模式定义

### 3. 文档提取器 (Extractors)

文档提取器负责从不同类型的文档中提取文本内容，为后续的结构化信息提取做准备。

主要提取器：
- **PDFExtractor**: 处理 PDF 文档
- **TextExtractor**: 处理纯文本文档

### 4. 大模型提供者 (LLM Provider)

大模型提供者封装了与不同大语言模型的交互逻辑，提供统一的接口进行文本生成和信息提取。

支持的模型：
- **OpenAI LLM**: 集成 OpenAI 的模型
- **Gemini LLM**: 集成 Google 的 Gemini 模型

### 5. 置信度评分器 (Confidence Scorer)

置信度评分器为每个提取的字段计算置信度分数，帮助评估提取结果的可靠性。

评分方法：
- **RegexConfidence**: 基于正则表达式提取的置信度计算
- **LLMConfidence**: 基于大模型提取的置信度计算
- **HybridConfidence**: 结合正则和大模型的混合置信度计算

### 6. 处理器 (Processors)

处理器提供了对提取过程中文本和结果的处理能力，包括预处理和后处理。

处理器类型：
- **PreProcessors**: 在提取前对文本进行处理
- **PostProcessors**: 在提取后对结果进行处理

### 7. 输出格式化器 (Output Formatter)

输出格式化器负责将提取结果转换为指定的输出格式，并添加元数据和置信度信息。

## 数据流

1. 客户端提供文档和模式名称
2. API 接口接收请求并传递给提取引擎
3. 提取引擎加载指定的模式
4. 提取引擎使用适当的提取器从文档中提取文本
5. 对于每个字段，根据其提取方法（正则表达式、大模型或混合）进行提取
6. 计算每个提取字段的置信度分数
7. 应用后处理器处理提取结果
8. 格式化输出结果并返回给客户端

## 扩展性设计

系统设计考虑了以下扩展点：

1. **新的提取器**: 可以添加新的文档类型提取器
2. **新的大模型**: 可以集成其他大语言模型
3. **新的置信度计算方法**: 可以实现新的置信度评分算法
4. **自定义处理器**: 可以添加自定义的预处理和后处理逻辑

## 配置管理

系统使用分层配置管理：

1. 默认配置
2. 配置文件 (JSON/YAML)
3. 环境变量
4. 运行时参数

配置项包括：
- LLM 设置 (API 密钥、模型名称、参数等)
- 提取器设置
- 存储设置
- 日志设置
