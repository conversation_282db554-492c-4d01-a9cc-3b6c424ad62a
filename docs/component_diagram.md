# XExtract 组件关系图

## 提取引擎组件详细设计

以下组件图展示了 XExtract 提取引擎的详细内部结构和组件间的关系。

```mermaid
classDiagram
    class ExtractEngine {
        +llm: BaseLLM
        +pdf_extractor: PDFExtractor
        +config: Dict
        +extract(file_path, schema_name) Dict
        +extract_from_text(text, schema_name) Dict
        -_extract_from_text(text, schema) Dict
        -_extract_field(text, field) Any
        -_extract_with_regex(text, field) Any
        -_extract_with_llm(text, field) Any
        -_create_field_prompt(text, field) str
    }
    
    class Schema {
        +name: str
        +description: str
        +version: str
        +fields: Dict[str, SchemaField]
        +metadata: Dict
        +from_json_file(file_path) Schema
        +from_yaml_file(file_path) Schema
        +get_by_name(name) Schema
    }
    
    class SchemaField {
        +name: str
        +type: FieldType
        +description: str
        +required: bool
        +array: bool
        +extraction_method: ExtractionMethod
        +regex_pattern: str
        +regex_group: int
        +prompt_hint: str
        +format: str
        +default: Any
        +fields: List[SchemaField]
        +pre_processors: List[str]
        +post_processors: List[str]
    }
    
    class FieldType {
        <<enumeration>>
        STRING
        INTEGER
        FLOAT
        BOOLEAN
        DATE
        DATETIME
        OBJECT
    }
    
    class ExtractionMethod {
        <<enumeration>>
        REGEX
        LLM
        HYBRID
    }
    
    class BaseLLM {
        <<abstract>>
        +generate(prompt) str
        +generate_json(prompt) Dict
    }
    
    class OpenAILLM {
        +client: OpenAI
        +model: str
        +generate(prompt) str
        +generate_json(prompt) Dict
    }
    
    class GeminiLLM {
        +client: GeminiClient
        +model: str
        +generate(prompt) str
        +generate_json(prompt) Dict
    }
    
    class PDFExtractor {
        +extract(file_path) Dict
        -_extract_text(pdf) str
        -_extract_metadata(pdf) Dict
    }
    
    class ConfidenceScorer {
        +calculate_regex_confidence(value, field) float
        +calculate_llm_confidence(value, field, llm_response) float
        +calculate_hybrid_confidence(regex_value, llm_value, regex_confidence, llm_confidence, field) float
        +calculate_nested_confidence(field_confidences) float
        +calculate_array_confidence(item_confidences) float
    }
    
    class ExtractionResultFormatter {
        +format_result(extracted_data, schema, file_path, ...) Dict
        -_format_metadata(schema, file_path, ...) Dict
        -_format_confidence_scores(confidence_scores) Dict
    }
    
    class Processor {
        <<interface>>
        +process(value) Any
    }
    
    class PreProcessor {
        +process(text) str
    }
    
    class PostProcessor {
        +process(value) Any
    }
    
    ExtractEngine --> Schema : uses
    ExtractEngine --> SchemaField : uses
    ExtractEngine --> BaseLLM : uses
    ExtractEngine --> PDFExtractor : uses
    ExtractEngine --> ConfidenceScorer : uses
    ExtractEngine --> ExtractionResultFormatter : uses
    ExtractEngine --> Processor : uses
    
    Schema "1" *-- "many" SchemaField : contains
    SchemaField --> FieldType : has
    SchemaField --> ExtractionMethod : has
    SchemaField "1" *-- "many" SchemaField : nested fields
    
    BaseLLM <|-- OpenAILLM : implements
    BaseLLM <|-- GeminiLLM : implements
    
    Processor <|-- PreProcessor : implements
    Processor <|-- PostProcessor : implements
```

## 数据流程图

以下数据流程图展示了从输入文档到输出结构化数据的完整流程。

```mermaid
sequenceDiagram
    participant Client
    participant ExtractEngine
    participant SchemaManager
    participant PDFExtractor
    participant LLMProvider
    participant ConfidenceScorer
    participant OutputFormatter
    
    Client->>ExtractEngine: extract(file_path, schema_name)
    ExtractEngine->>SchemaManager: get_schema(schema_name)
    SchemaManager-->>ExtractEngine: schema
    
    ExtractEngine->>PDFExtractor: extract(file_path)
    PDFExtractor-->>ExtractEngine: extracted_text, metadata
    
    loop For each field in schema
        alt Regex Extraction
            ExtractEngine->>ExtractEngine: _extract_with_regex(text, field)
            ExtractEngine->>ConfidenceScorer: calculate_confidence(value, field, REGEX)
        else LLM Extraction
            ExtractEngine->>LLMProvider: generate(prompt)
            LLMProvider-->>ExtractEngine: response
            ExtractEngine->>ConfidenceScorer: calculate_confidence(value, field, LLM)
        else Hybrid Extraction
            ExtractEngine->>ExtractEngine: _extract_with_regex(text, field)
            ExtractEngine->>LLMProvider: generate(prompt)
            LLMProvider-->>ExtractEngine: response
            ExtractEngine->>ConfidenceScorer: calculate_confidence(value, field, HYBRID)
        end
        ConfidenceScorer-->>ExtractEngine: confidence_score
    end
    
    ExtractEngine->>OutputFormatter: format_result(extracted_data, confidence_scores, ...)
    OutputFormatter-->>ExtractEngine: formatted_result
    ExtractEngine-->>Client: extraction_result
```

## 置信度计算流程

以下流程图展示了置信度计算模块的工作流程。

```mermaid
flowchart TD
    Start([开始]) --> InputCheck{检查输入类型}
    
    InputCheck -->|正则表达式| RegexConfidence[计算正则置信度]
    InputCheck -->|大模型| LLMConfidence[计算LLM置信度]
    InputCheck -->|混合方法| HybridConfidence[计算混合置信度]
    InputCheck -->|对象类型| ObjectConfidence[计算嵌套对象置信度]
    InputCheck -->|数组类型| ArrayConfidence[计算数组置信度]
    
    RegexConfidence --> FieldTypeCheck{检查字段类型}
    LLMConfidence --> ResponseCheck{检查响应质量}
    HybridConfidence --> CompareResults{比较结果}
    ObjectConfidence --> AggregateNested[聚合嵌套字段置信度]
    ArrayConfidence --> AggregateItems[聚合数组项置信度]
    
    FieldTypeCheck -->|字符串| StringConfidence[字符串置信度]
    FieldTypeCheck -->|数字| NumberConfidence[数字置信度]
    FieldTypeCheck -->|布尔| BooleanConfidence[布尔置信度]
    FieldTypeCheck -->|日期| DateConfidence[日期置信度]
    
    ResponseCheck -->|空响应| LowConfidence[低置信度]
    ResponseCheck -->|不确定响应| MediumLowConfidence[中低置信度]
    ResponseCheck -->|明确响应| HighConfidence[高置信度]
    
    CompareResults -->|结果一致| VeryHighConfidence[很高置信度]
    CompareResults -->|结果不一致| UseHigherConfidence[使用较高置信度]
    CompareResults -->|部分一致| AdjustedConfidence[调整置信度]
    
    StringConfidence --> FinalScore
    NumberConfidence --> FinalScore
    BooleanConfidence --> FinalScore
    DateConfidence --> FinalScore
    
    LowConfidence --> FinalScore
    MediumLowConfidence --> FinalScore
    HighConfidence --> FinalScore
    
    VeryHighConfidence --> FinalScore
    UseHigherConfidence --> FinalScore
    AdjustedConfidence --> FinalScore
    
    AggregateNested --> FinalScore
    AggregateItems --> FinalScore
    
    FinalScore[最终置信度分数] --> End([结束])
```

## 提取引擎工作流程

```mermaid
stateDiagram-v2
    [*] --> 接收输入
    接收输入 --> 加载模式
    加载模式 --> 提取文本
    
    提取文本 --> 字段提取
    
    state 字段提取 {
        [*] --> 选择提取方法
        选择提取方法 --> 正则提取: 正则方法
        选择提取方法 --> LLM提取: LLM方法
        选择提取方法 --> 混合提取: 混合方法
        
        正则提取 --> 计算置信度
        LLM提取 --> 计算置信度
        混合提取 --> 计算置信度
        
        计算置信度 --> 应用后处理器
        应用后处理器 --> [*]
    }
    
    字段提取 --> 格式化结果
    格式化结果 --> 返回结果
    返回结果 --> [*]
```

## 模块依赖关系

```mermaid
graph LR
    subgraph 核心模块
        engine[提取引擎]
        schema[模式定义]
        confidence[置信度计算]
    end
    
    subgraph 提取器
        pdf[PDF提取器]
        text[文本提取器]
    end
    
    subgraph LLM集成
        llm_base[LLM基类]
        openai[OpenAI集成]
        gemini[Gemini集成]
    end
    
    subgraph 输出处理
        formatter[结果格式化]
        processors[处理器]
    end
    
    subgraph 配置与工具
        config[配置管理]
        utils[工具函数]
    end
    
    engine --> schema
    engine --> confidence
    engine --> pdf
    engine --> text
    engine --> llm_base
    engine --> formatter
    engine --> processors
    
    llm_base --> openai
    llm_base --> gemini
    
    schema --> utils
    confidence --> schema
    formatter --> schema
    formatter --> confidence
    
    config --> engine
    config --> llm_base
    config --> pdf
    config --> text
```
