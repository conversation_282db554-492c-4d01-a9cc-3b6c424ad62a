# XExtract 核心架构

以下是 XExtract 提取引擎的核心架构图，展示了主要组件和数据流。

```mermaid
graph TB
    subgraph 输入层
        Input[文档输入] --> DocProcessor[文档处理器]
        Schema[提取模式] --> SchemaLoader[模式加载器]
    end
    
    subgraph 提取引擎核心
        DocProcessor --> TextExtractor[文本提取]
        SchemaLoader --> FieldProcessor[字段处理器]
        
        TextExtractor --> ExtractionPipeline[提取管道]
        FieldProcessor --> ExtractionPipeline
        
        ExtractionPipeline --> RegexExtractor[正则提取器]
        ExtractionPipeline --> LLMExtractor[大模型提取器]
        ExtractionPipeline --> HybridExtractor[混合提取器]
        
        RegexExtractor --> ResultMerger[结果合并器]
        LLMExtractor --> ResultMerger
        HybridExtractor --> ResultMerger
        
        ResultMerger --> ConfidenceScorer[置信度评分器]
        ConfidenceScorer --> PostProcessor[后处理器]
    end
    
    subgraph 输出层
        PostProcessor --> ResultFormatter[结果格式化器]
        ResultFormatter --> Output[结构化输出]
    end
    
    subgraph 配置与管理
        Config[配置管理] -.-> DocProcessor
        Config -.-> SchemaLoader
        Config -.-> LLMExtractor
        Config -.-> ResultFormatter
        
        Logger[日志系统] -.-> ExtractionPipeline
        Logger -.-> ResultMerger
        Logger -.-> ConfidenceScorer
    end
    
    classDef core fill:#f9f,stroke:#333,stroke-width:2px
    classDef input fill:#bbf,stroke:#333,stroke-width:1px
    classDef output fill:#bfb,stroke:#333,stroke-width:1px
    classDef config fill:#fbb,stroke:#333,stroke-width:1px
    
    class ExtractionPipeline,ResultMerger,ConfidenceScorer core
    class Input,Schema,DocProcessor,SchemaLoader input
    class ResultFormatter,Output output
    class Config,Logger config
```

## 核心组件说明

### 1. 文档处理器 (Document Processor)

负责接收输入文档并进行初步处理，包括文件类型识别、文档解析等。

### 2. 模式加载器 (Schema Loader)

负责加载和验证提取模式，模式定义了需要从文档中提取的字段和提取规则。

### 3. 文本提取器 (Text Extractor)

从文档中提取纯文本内容，为后续的结构化信息提取做准备。

### 4. 字段处理器 (Field Processor)

解析模式中的字段定义，确定每个字段的提取方法和规则。

### 5. 提取管道 (Extraction Pipeline)

协调整个提取过程，根据字段定义选择合适的提取器。

### 6. 提取器 (Extractors)

包括正则提取器、大模型提取器和混合提取器，负责从文本中提取特定字段的值。

### 7. 结果合并器 (Result Merger)

合并来自不同提取器的结果，解决冲突并生成初步的提取结果。

### 8. 置信度评分器 (Confidence Scorer)

为每个提取的字段计算置信度分数，评估提取结果的可靠性。

### 9. 后处理器 (Post Processor)

对提取结果进行后处理，如数据清洗、格式转换、值规范化等。

### 10. 结果格式化器 (Result Formatter)

将提取结果格式化为指定的输出格式，添加元数据和置信度信息。

## 数据流程

1. 输入文档经过文档处理器解析为文本
2. 提取模式通过模式加载器加载和验证
3. 提取管道根据字段定义选择合适的提取器
4. 提取器从文本中提取字段值
5. 结果合并器合并来自不同提取器的结果
6. 置信度评分器计算每个字段的置信度分数
7. 后处理器对提取结果进行处理
8. 结果格式化器生成最终的输出结果
