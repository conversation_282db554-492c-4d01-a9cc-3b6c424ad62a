# Document Parser Service Testing

This document provides instructions for testing the document parser service with different parsing methods.

## Overview

The document parser service supports the following methods:

1. **pdf2doc** - Extract document structure from PDF
2. **pdf2table** - Extract tables from PDF
3. **docx2pdf** - Convert DOCX to PDF

## Prerequisites

Before testing, make sure you have:

1. A valid API key for the document parser service
2. Sample PDF and DOCX files for testing
3. The XExtract package installed

## Configuration

The document parser service is configured in the `.env` file or through environment variables. The following settings are available:

```bash
# Document Parser settings
XEXTRACT_DOCUMENT_PARSER__API_BASE_URL=http://api.memect.cn:6111
XEXTRACT_DOCUMENT_PARSER__API_KEY=your-document-parser-api-key
XEXTRACT_DOCUMENT_PARSER__TIMEOUT=60
XEXTRACT_DOCUMENT_PARSER__MAX_RETRIES=3
XEXTRACT_DOCUMENT_PARSER__MAX_FILE_SIZE=10485760
XEXTRACT_DOCUMENT_PARSER__POLL_INTERVAL=1
XEXTRACT_DOCUMENT_PARSER__ASYNC_MODE=false

# DOCX to PDF conversion settings
XEXTRACT_DOCX2PDF__TABLE_BORDER=2

# PDF to Table extraction settings
XEXTRACT_PDF2TABLE__EXTRACT_IMAGE=false
XEXTRACT_PDF2TABLE__OCR=auto
XEXTRACT_PDF2TABLE__OCR_LINE=true
XEXTRACT_PDF2TABLE__FORMAT=4
XEXTRACT_PDF2TABLE__TEXTLINES=false
XEXTRACT_PDF2TABLE__TABLE=all
XEXTRACT_PDF2TABLE__LAYOUT=auto
XEXTRACT_PDF2TABLE__ANALYZER=auto
XEXTRACT_PDF2TABLE__TEXT=false
XEXTRACT_PDF2TABLE__EXCEL=false
XEXTRACT_PDF2TABLE__HTML=false
XEXTRACT_PDF2TABLE__HTML_SCALE=1
XEXTRACT_PDF2TABLE__TABLE_SCREENSHOT=false

# PDF to Document structure extraction settings
XEXTRACT_PDF2DOC__EXTRACT_IMAGE=false
XEXTRACT_PDF2DOC__OCR=auto
XEXTRACT_PDF2DOC__OCR_LINE=true
XEXTRACT_PDF2DOC__FORMAT=4
XEXTRACT_PDF2DOC__TEXTLINES=false
XEXTRACT_PDF2DOC__TABLE=all
XEXTRACT_PDF2DOC__LAYOUT=auto
XEXTRACT_PDF2DOC__ANALYZER=auto
XEXTRACT_PDF2DOC__MODE=3
XEXTRACT_PDF2DOC__MERGE_TABLE=false
XEXTRACT_PDF2DOC__HTML=false
XEXTRACT_PDF2DOC__HTML_SCALE=1
XEXTRACT_PDF2DOC__TABLE_SCREENSHOT=false
```

All test scripts and tools will automatically load these settings from the configuration file. You can also override these settings using command-line arguments when running the tests.

## Testing Methods

### Using the Command-Line Interface

You can test the document parser service using the command-line interface:

```bash
# Test PDF to document structure extraction
python -m src.xextract docparser --method pdf2doc --input tests/data/sample.pdf --output-dir tests/output --wait --verbose

# Test PDF to table extraction
python -m src.xextract docparser --method pdf2table --input tests/data/sample.pdf --output-dir tests/output --wait --verbose

# Test DOCX to PDF conversion
python -m src.xextract docparser --method docx2pdf --input tests/data/sample.docx --output-dir tests/output --wait --verbose
```

### Using the Test Script

Alternatively, you can use the provided test script:

```bash
# Test PDF to document structure extraction
./tests/run_docparser_test.sh tests/data/sample.pdf pdf2doc

# Test PDF to table extraction
./tests/run_docparser_test.sh tests/data/sample.pdf pdf2table

# Test DOCX to PDF conversion
./tests/run_docparser_test.sh tests/data/sample.docx docx2pdf
```

### Using the Integration Test

For more detailed testing, you can use the integration test:

```bash
# Test PDF to document structure extraction
python -m tests.integration.test_docparser_service --method pdf2doc --input tests/data/sample.pdf --output-dir tests/output --verbose

# Test PDF to table extraction
python -m tests.integration.test_docparser_service --method pdf2table --input tests/data/sample.pdf --output-dir tests/output --verbose

# Test DOCX to PDF conversion
python -m tests.integration.test_docparser_service --method docx2pdf --input tests/data/sample.docx --output-dir tests/output --verbose

# Override API settings
python -m tests.integration.test_docparser_service --method pdf2doc --input tests/data/sample.pdf --api-url http://custom-api.example.com --api-key your-api-key --timeout 120 --verbose
```

The integration test supports the following command-line options:

| Option | Description |
|--------|-------------|
| `--method` | Parsing method to test (pdf2doc, pdf2table, docx2pdf) |
| `--input` | Input file path |
| `--output-dir` | Directory to save output files |
| `--params` | JSON string with additional parameters |
| `--api-url` | Override API base URL from config |
| `--api-key` | Override API key from config |
| `--timeout` | Override timeout from config (in seconds) |
| `--verbose` | Enable verbose logging |

## Command-Line Options

The following command-line options are available for the `docparser` command:

| Option | Description |
|--------|-------------|
| `--method` | Parsing method to test (pdf2doc, pdf2table, docx2pdf) |
| `--input` | Input file path |
| `--output-dir` | Directory to save output files |
| `--output-format` | Output format (json, zip, bz2) for pdf2doc and pdf2table |
| `--params` | JSON string with additional parameters |
| `--storage-dir` | Directory for storing task data |
| `--max-concurrent` | Maximum number of concurrent tasks |
| `--async-mode` | Use asynchronous mode for API calls |
| `--wait` | Wait for task to complete |
| `--timeout` | Timeout in seconds when waiting for task completion |
| `--verbose` | Enable verbose logging |

## Example Parameters

You can provide additional parameters to the API using the `--params` option:

```bash
# Extract tables with OCR enabled
python -m src.xextract docparser --method pdf2table --input tests/data/sample.pdf --params '{"ocr": "true", "table": "all"}'

# Extract document structure with specific settings
python -m src.xextract docparser --method pdf2doc --input tests/data/sample.pdf --params '{"mode": "3", "ocr": "auto", "layout": "auto"}'

# Convert DOCX to PDF with table border settings
python -m src.xextract docparser --method docx2pdf --input tests/data/sample.docx --params '{"table-border": "2"}'
```

## Troubleshooting

If you encounter issues with the document parser service, try the following:

1. Check that your API key is valid and correctly configured
2. Verify that the API base URL is correct
3. Check that the input file exists and is in the correct format
4. Increase the timeout value if the task is taking too long
5. Enable verbose logging with the `--verbose` option for more detailed information
6. Check the task status using the task management CLI: `python -m src.xextract task show <task_id>`
