# XExtract Documentation

Welcome to the XExtract documentation!

## Overview

XExtract is a Python package designed for data extraction from various sources.

## Installation

```bash
uv pip install xextract
```

## Basic Usage

```python
from xextract import extract

# Extract data from a file
data = extract.from_source("example.txt")

# Extract data from text
text_data = extract.from_text("Sample text for extraction")
```

## API Reference

See the [API Reference](api.md) for detailed documentation of all functions and classes.
