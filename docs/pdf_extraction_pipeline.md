# PDF Extraction Pipeline

This document describes the complete PDF extraction pipeline, which automates the process of extracting structured information from PDF documents using document parsing services and large language models (LLMs).

## Overview

The PDF extraction pipeline consists of the following steps:

1. **PDF to doc.json**: The PDF file is sent to the document parser service, which analyzes the document structure, extracts text and tables, and returns a structured JSON representation (doc.json).

2. **doc.json to Markdown**: The doc.json file is converted to Markdown format, which is more suitable for LLM processing. This step preserves the document structure, including headings, paragraphs, and tables.

3. **Markdown to Extraction Results**: The Markdown representation is sent to an LLM along with a schema defining the information to extract. The LLM analyzes the document and returns structured extraction results.

4. **Validation and Post-processing**: The extraction results are validated against the schema and post-processed to ensure data quality.

## Usage

### Command Line Interface

The simplest way to use the PDF extraction pipeline is through the provided command-line interface:

```bash
# Extract information from a PDF file using the default schema
./tests/test_pdf_extraction.sh path/to/document.pdf

# Extract information using a specific schema
./tests/test_pdf_extraction.sh path/to/document.pdf schema_name

# With custom options
python -m tests.integration.test_pdf_extraction_pipeline \
    --input path/to/document.pdf \
    --schema schema_name \
    --output-dir path/to/output \
    --llm-provider openai \
    --llm-model gpt-4o \
    --verbose
```

### Python API

You can also use the PDF extraction pipeline programmatically in your Python code:

```python
import asyncio
from pathlib import Path
from tests.integration.test_pdf_extraction_pipeline import PDFExtractionPipeline

async def extract_from_pdf(pdf_path, schema_name):
    # Initialize the pipeline
    pipeline = PDFExtractionPipeline(
        schema_name=schema_name,
        llm_provider="gemini",  # or "openai"
        llm_model="gemini-2.0-flash"  # or "gpt-4o"
    )
    
    # Process the PDF
    results = await pipeline.process_pdf(
        pdf_path=pdf_path,
        save_intermediate=True  # Save doc.json and Markdown files
    )
    
    return results

# Run the extraction
pdf_path = Path("path/to/document.pdf")
schema_name = "shareholder_meeting"
results = asyncio.run(extract_from_pdf(pdf_path, schema_name))

# Print the results
import json
print(json.dumps(results, ensure_ascii=False, indent=2))
```

## Configuration

The PDF extraction pipeline uses the following configuration settings:

### Document Parser Settings

These settings control the document parser service used to convert PDF to doc.json:

```bash
# Document Parser settings
XEXTRACT_DOCUMENT_PARSER__API_BASE_URL=http://api.memect.cn:6111
XEXTRACT_DOCUMENT_PARSER__API_KEY=your-document-parser-api-key
XEXTRACT_DOCUMENT_PARSER__TIMEOUT=60
XEXTRACT_DOCUMENT_PARSER__MAX_RETRIES=3
XEXTRACT_DOCUMENT_PARSER__POLL_INTERVAL=1
```

### LLM Settings

These settings control the LLM used for information extraction:

```bash
# LLM settings
XEXTRACT_LLM__PROVIDER=gemini
XEXTRACT_LLM__MODEL=gemini-2.0-flash
XEXTRACT_LLM__API_KEY=your-llm-api-key
XEXTRACT_LLM__API_BASE_URL=https://generativelanguage.googleapis.com/v1beta/openai
XEXTRACT_LLM__TEMPERATURE=0.0
```

## Schemas

The extraction schema defines the information to extract from the document. Schemas are stored in the `models` directory and can be loaded by name.

Example schema for shareholder meeting announcements:

```json
{
  "name": "shareholder_meeting",
  "description": "Extract key information from shareholder meeting announcements",
  "fields": [
    {
      "name": "company_name",
      "type": "string",
      "description": "Name of the company holding the meeting",
      "required": true
    },
    {
      "name": "meeting_type",
      "type": "string",
      "description": "Type of the meeting (annual, extraordinary, etc.)",
      "required": true
    },
    {
      "name": "meeting_date",
      "type": "string",
      "description": "Date of the meeting",
      "required": true
    },
    {
      "name": "meeting_time",
      "type": "string",
      "description": "Time of the meeting",
      "required": true
    },
    {
      "name": "meeting_location",
      "type": "string",
      "description": "Location of the meeting",
      "required": true
    },
    {
      "name": "record_date",
      "type": "string",
      "description": "Record date for determining shareholders eligible to attend",
      "required": false
    },
    {
      "name": "agenda_items",
      "type": "array",
      "description": "List of agenda items to be discussed",
      "required": true,
      "items": {
        "type": "string"
      }
    }
  ]
}
```

## Output Format

The extraction results are returned as a JSON object with the following structure:

```json
{
  "company_name": "Example Corporation",
  "meeting_type": "Annual General Meeting",
  "meeting_date": "2024-05-15",
  "meeting_time": "10:00 AM",
  "meeting_location": "123 Main Street, Anytown, USA",
  "record_date": "2024-04-30",
  "agenda_items": [
    "Approval of the 2023 Annual Report",
    "Election of Directors",
    "Appointment of Auditors",
    "Approval of Dividend Distribution"
  ],
  "_metadata": {
    "schema_name": "shareholder_meeting",
    "llm_provider": "gemini",
    "llm_model": "gemini-2.0-flash"
  }
}
```

## Troubleshooting

If you encounter issues with the PDF extraction pipeline, try the following:

1. **Check the document parser service**: Ensure that the document parser service is running and accessible. You can test it directly using the `test_docparser.sh` script.

2. **Check the LLM API**: Ensure that the LLM API is accessible and that you have provided a valid API key. You can test it directly using the LLM client.

3. **Check the PDF file**: Ensure that the PDF file is valid and readable. Some PDFs may be encrypted, scanned images, or have other issues that make them difficult to process.

4. **Enable verbose logging**: Use the `--verbose` flag to enable detailed logging, which can help identify issues.

5. **Check intermediate files**: If you enable `save_intermediate=True`, check the generated doc.json and Markdown files to see if they contain the expected content.

## Performance Considerations

- **Document Size**: Large documents may take longer to process and may exceed token limits for LLMs. Consider splitting large documents into smaller chunks.

- **LLM Selection**: Different LLMs have different capabilities and performance characteristics. GPT-4 and Gemini 2.0 Pro generally provide the best extraction quality but may be slower and more expensive than smaller models.

- **Schema Design**: Well-designed schemas with clear field descriptions help the LLM extract information more accurately. Consider including examples in the schema for complex fields.

- **Document Quality**: The quality of the extraction depends on the quality of the document. Well-structured documents with clear headings and tables are easier to extract information from than poorly structured documents.
