# XExtract
## 总体原则
我的目标是实现一个关于多场景文档下PDF提取的引擎，支持规则和大模型，或混合方法实现，主要是想发挥大模型的能力，避免规则的泛化性低问题。实现提取需要定义待提取字段的schema，输出PDF。提取后可能还需要后处理等操作保证数据的准确率。
如果用大模型那就要考虑每类公告提示词的管理，版本迭代等，如果是规则，也要考虑规则的复用性和管理，因为规则本身就是一种知识。
提取的方法从过往经验看，需要大模型和规则相结合，因为大模型可能有幻觉，导致无中生有，所以我们提出了如果对提取的字段计算一个置信度分数，我们以此来评价提取字段的可信程度。

要求服务是无状态的，尽量不要有过多第三方依赖，所以尽量保证使用python内置的特性实现，由于是第一版本，所以我们可以考虑本地存储或基于内存存储。
整体实现可以遵循TDD的开发模式。
其他要求：
1. 配置文件放在项目根目录下。
2. 单元测试使用pytest。
3.  减少冗余，不必要的实现代码。



## 架构设计原则
1. 各package不直接依赖settings配置文件导入，而是通过参数传递。
2. 各目录需单一职责。如LLM就支持接入API，能传入参数返回结果，和相关鲁棒性完善。不应涉及到提取逻辑。


### 接口设计
1. 具备token认证能力
2. 提取结果响应的结构
   ```json
   {
     "code": 0, // 0表示成功，1表示失败
     "message": "success",
     "data": {} // 提取结果
   }
 ```
 1. 异步提取，提取结果异步返回。
 2. 提取结果返回时，需要提供提取任务id，用于查询提取结果。
 ```json
 {
     "code": 0, // 0表示成功，1表示失败, 2表示任务处理中
     "message": "success",
     "data": { "task_id": "123" }
 }
 ```

## 提取开发
### 资源管理
1. 提示词
2. 建立提取字段（中文，英文，类型，默认值，属于的文档类别），然后自动生成提取字段schema。
3. 每一种场景下的解析工具及对应参数

### 开发及测试
1. 支持按提取类别编写提示词，版本管理，提取词统一管理。
2. 上传文档，可以选择解析方式。
3. 填写提取字段schema，也支持自动化配置schema.
4. 配置模型API或选择系统提供的大模型API。
5. 提交测试，输出提取结果，根据情况保留测试结果。
6. 优化提示词-可以留存版本，迭代测试。
7. 达到预期，发布提取API。


## 对外提取API
1. 输入：文档（.pdf,.txt,.md等格式），提供, 提示词（可选）。
2. 输出提取结果。



## 数据管理
 - 文档（数据标签：合同，研报，公告，年报），文档存储管理，使用 MINIO。
 - 提取字段（单字段管理：名称，类型，描述）
 - 构建scheam。基于单字段组装schema。


## 评测
1. 提取评估集自动化生成或人工导入csv
2. 评估，得出准确率，召回率，F1
3. 存储字段粒度的评估结果。


## web端
1. 使用vue3+js+vite开发
2. 整体布局简洁，大方。有简单的登录页面，进入后，左侧为导航栏，右侧为内容。
   1. 文档管理（列表，增删改查）
   2. 提取开发页面(保持简洁设计：在一个页面能展示文档文件和提取结果- json结果和字段结果。可以定义新的的提取字段)
      1. 配置字段
      2. 生成schema
      3. 选择模型
      4. 上传或选择文档，设置解析方式，测试
      5. 优化提示词
      6. 发布API
3. 后台管理
   1. 提示词管理。
   2. API管理及监控。


## 项目验证

### 项目：深交所临时公告和定期报告抽取
> 错误案例文档：https://hujialou.quip.com/YrrRO6zrYTWx
0. 目标：DDL：5月底，并行每日统计准确率和人工比较，观察后，逐步对接到测试和生产环境。
   1. 提升现有提取字段的准确率
      1. 提取工具迭代
   2. 降低人工审核时长--从全量审核到抽样审核
      1. 置信度算法

1. 股东大会通知：验证短文档（<10页）提取实现
   1. 召开情况表 0.96
   2. 议案表 0.68 【优先】议案内容漏字，漏书名号，比较费人力。
      1. 投票序号与议案序号格式化要求
            | 议案序号 | 投票序号 |
            | -------- | -------- |
            | 1        | 1        |
            | 1.1      | 1.01     |
            | 1.2      | 1.02     |
            | 1.10     | 1.1      |
            | 1.11     | 1.11     |
            | 1.12     | 1.12     |
            | 1.20     | 1.2      |
            | 1.21     | 1.21     |
            | 2        | 2        |
            | 11       | 11       |
            | 111      | 111      |
            | 111.10   | 111.1    |
            | 123.50   | 123.5    |

2. 定期报告（部分类别）：验证长文档（100+页的提取实现），1,2,4需要人工补齐
   1. 分红转赠（7）：0.564 （0426）
   2. 股本结构表（30～36）：0.691（0426） 
      1. 原文提取+组合运算
   3. 主要财务指标（32） 0.578（0426）
      1. 原文提取+组合运算
   4. 定期报告审计意见表（11） 0.731 （0426)