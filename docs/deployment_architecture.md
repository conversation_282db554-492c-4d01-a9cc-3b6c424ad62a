# XExtract 部署架构

## 部署架构概览

XExtract 系统可以以多种方式部署，从简单的单机部署到复杂的分布式部署。以下是几种常见的部署架构。

## 单机部署架构

适用于开发环境或小规模使用场景。

```mermaid
graph TD
    Client[客户端] --> API[XExtract API]
    API --> Engine[提取引擎]
    Engine --> LocalStorage[本地存储]
    Engine --> LLMService[外部LLM服务]
    
    subgraph 单机服务器
        API
        Engine
        LocalStorage
    end
    
    subgraph 外部服务
        LLMService
    end
```

## 微服务部署架构

适用于生产环境和大规模使用场景。

```mermaid
graph TD
    Client[客户端] --> APIGateway[API网关]
    
    APIGateway --> ExtractService[提取服务]
    APIGateway --> SchemaService[模式管理服务]
    APIGateway --> StorageService[存储服务]
    
    ExtractService --> LLMService[LLM服务]
    ExtractService --> PDFService[PDF处理服务]
    ExtractService --> SchemaService
    ExtractService --> StorageService
    
    SchemaService --> Database[(模式数据库)]
    StorageService --> ObjectStorage[(对象存储)]
    
    subgraph 外部服务
        LLMService
    end
    
    subgraph 内部服务
        ExtractService
        SchemaService
        StorageService
        PDFService
    end
    
    subgraph 数据存储
        Database
        ObjectStorage
    end
```

## 容器化部署架构

使用 Docker 和 Kubernetes 进行容器化部署。

```mermaid
graph TD
    Client[客户端] --> Ingress[Ingress控制器]
    
    subgraph Kubernetes集群
        Ingress --> APIService[API服务]
        
        APIService --> ExtractService[提取服务]
        APIService --> SchemaService[模式服务]
        
        ExtractService --> PDFService[PDF服务]
        ExtractService --> LLMProxy[LLM代理]
        
        SchemaService --> SchemaDB[(模式数据库)]
        ExtractService --> ResultDB[(结果数据库)]
        PDFService --> ObjectStorage[(对象存储)]
    end
    
    LLMProxy --> ExternalLLM[外部LLM服务]
```

## 高可用部署架构

适用于需要高可用性和可扩展性的企业级部署。

```mermaid
graph TD
    Client[客户端] --> LoadBalancer[负载均衡器]
    
    LoadBalancer --> APIGateway1[API网关 1]
    LoadBalancer --> APIGateway2[API网关 2]
    
    subgraph 服务集群1
        APIGateway1 --> ExtractService1[提取服务]
        APIGateway1 --> SchemaService1[模式服务]
    end
    
    subgraph 服务集群2
        APIGateway2 --> ExtractService2[提取服务]
        APIGateway2 --> SchemaService2[模式服务]
    end
    
    ExtractService1 --> PDFService[PDF处理服务集群]
    ExtractService2 --> PDFService
    
    ExtractService1 --> LLMProxy[LLM代理集群]
    ExtractService2 --> LLMProxy
    
    SchemaService1 --> DBCluster[(数据库集群)]
    SchemaService2 --> DBCluster
    
    PDFService --> ObjectStorage[(分布式对象存储)]
    
    LLMProxy --> LLMService1[LLM服务 1]
    LLMProxy --> LLMService2[LLM服务 2]
```

## 离线部署架构

适用于无法访问外部 LLM 服务的环境。

```mermaid
graph TD
    Client[客户端] --> API[XExtract API]
    
    subgraph 内部网络
        API --> Engine[提取引擎]
        Engine --> LocalLLM[本地LLM服务]
        Engine --> LocalStorage[本地存储]
        
        LocalLLM --> GPUServer[GPU服务器]
    end
```

## 混合提取架构

结合规则引擎和大模型的混合提取架构。

```mermaid
graph TD
    Input[输入文档] --> Preprocessor[预处理器]
    
    Preprocessor --> RuleEngine[规则引擎]
    Preprocessor --> LLMEngine[大模型引擎]
    
    RuleEngine --> RuleResults[规则提取结果]
    LLMEngine --> LLMResults[大模型提取结果]
    
    RuleResults --> Merger[结果合并器]
    LLMResults --> Merger
    
    Merger --> ConfidenceCalculator[置信度计算器]
    ConfidenceCalculator --> PostProcessor[后处理器]
    PostProcessor --> Output[输出结果]
    
    subgraph 配置
        ExtractionSchema[提取模式] --> RuleEngine
        ExtractionSchema --> LLMEngine
        ExtractionSchema --> Merger
    end
```

## 批处理架构

适用于大批量文档处理场景。

```mermaid
graph TD
    InputBatch[输入批次] --> Splitter[任务分割器]
    
    Splitter --> Queue[(任务队列)]
    
    Queue --> Worker1[工作节点 1]
    Queue --> Worker2[工作节点 2]
    Queue --> Worker3[工作节点 3]
    
    Worker1 --> ResultCollector[结果收集器]
    Worker2 --> ResultCollector
    Worker3 --> ResultCollector
    
    ResultCollector --> OutputBatch[输出批次]
    
    subgraph 工作节点
        Worker1
        Worker2
        Worker3
    end
    
    subgraph 监控系统
        Monitor[监控服务] --> Worker1
        Monitor --> Worker2
        Monitor --> Worker3
        Monitor --> Queue
        Monitor --> ResultCollector
    end
```
