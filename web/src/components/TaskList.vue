<template>
  <div class="task-list-container">
    <div class="task-list-header">
      <h3>提取任务列表</h3>
      <div class="task-list-actions">
        <el-button size="small" type="primary" @click="refreshTasks">
          <el-icon><Refresh /></el-icon> 刷新
        </el-button>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="tasks"
      style="width: 100%"
      :default-sort="{ prop: 'createdAt', order: 'descending' }"
      @row-click="handleRowClick"
    >
      <el-table-column prop="id" label="任务ID" width="100" :show-overflow-tooltip="true" />
      <el-table-column prop="schemaName" label="Schema" width="150" :show-overflow-tooltip="true" />
      <el-table-column prop="fileName" label="文件名" min-width="200" :show-overflow-tooltip="true" />
      <el-table-column prop="createdAt" label="创建时间" width="180" sortable>
        <template #default="scope">
          {{ formatDate(scope.row.createdAt) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="120">
        <template #default="scope">
          <div class="status-cell">
            <el-tag
              :type="getStatusType(scope.row.status)"
              size="small"
            >
              {{ getStatusText(scope.row.status) }}
            </el-tag>
            <el-progress
              v-if="scope.row.status === 'processing' && scope.row.progress !== undefined"
              :percentage="scope.row.progress"
              :stroke-width="5"
              :show-text="false"
              class="task-progress"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="scope">
          <el-button
            size="small"
            type="primary"
            :disabled="scope.row.status !== 'success'"
            @click.stop="viewResult(scope.row)"
          >
            查看
          </el-button>
          <el-button
            size="small"
            type="danger"
            :disabled="scope.row.status === 'processing'"
            @click.stop="deleteTask(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="task-list-pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalTasks"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 确认删除对话框 -->
    <el-dialog
      v-model="deleteDialogVisible"
      title="确认删除"
      width="30%"
    >
      <span>确定要删除该任务吗？此操作不可恢复。</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="confirmDelete">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { useExtractionStore } from '../stores/extraction';

const props = defineProps({
  // 是否自动加载任务
  autoLoad: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['task-selected', 'task-deleted', 'refresh']);

// 状态
const loading = ref(false);
const tasks = ref([]);
const currentPage = ref(1);
const pageSize = ref(20);
const totalTasks = ref(0);
const deleteDialogVisible = ref(false);
const taskToDelete = ref(null);

// 获取提取服务
const extractionStore = useExtractionStore();

// 加载任务列表
const loadTasks = async () => {
  loading.value = true;
  try {
    // 调用API获取任务列表
    const response = await extractionStore.getTasks(currentPage.value, pageSize.value);

    if (response && response.data) {
      tasks.value = response.data.tasks || [];
      totalTasks.value = response.data.total || tasks.value.length;
    } else {
      // 如果没有任务数据，显示空列表
      tasks.value = [];
      totalTasks.value = 0;
      console.warn('未获取到任务数据');
    }
  } catch (error) {
    console.error('加载任务列表失败:', error);
    ElMessage.error('加载任务列表失败');
  } finally {
    loading.value = false;
  }
};

// 刷新任务列表
const refreshTasks = () => {
  loadTasks();
  emit('refresh');
};

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  loadTasks();
};

// 处理每页显示数量变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  loadTasks();
};

// 查看任务结果
const viewResult = (task) => {
  emit('task-selected', task);
};

// 删除任务
const deleteTask = (task) => {
  taskToDelete.value = task;
  deleteDialogVisible.value = true;
};

// 确认删除任务
const confirmDelete = async () => {
  if (!taskToDelete.value) return;

  try {
    // 调用API删除任务
    await extractionStore.deleteTask(taskToDelete.value.id);

    ElMessage.success('删除任务成功');
    tasks.value = tasks.value.filter(t => t.id !== taskToDelete.value.id);
    emit('task-deleted', taskToDelete.value);
  } catch (error) {
    console.error('删除任务失败:', error);
    ElMessage.error('删除任务失败');
  } finally {
    deleteDialogVisible.value = false;
    taskToDelete.value = null;
  }
};

// 处理行点击
const handleRowClick = (row) => {
  if (row.status === 'success') {
    viewResult(row);
  }
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case 'success':
      return 'success';
    case 'processing':
      return 'warning';
    case 'failed':
      return 'danger';
    default:
      return 'info';
  }
};

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'success':
      return '成功';
    case 'processing':
      return '处理中';
    case 'failed':
      return '失败';
    default:
      return '未知';
  }
};

// 组件挂载时加载任务列表
onMounted(() => {
  if (props.autoLoad) {
    loadTasks();
  }
});

// 暴露方法给父组件
defineExpose({
  loadTasks,
  refreshTasks
});
</script>

<style scoped>
.task-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.task-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.task-list-header h3 {
  margin: 0;
}

.task-list-pagination {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.el-table {
  --el-table-border-color: var(--el-border-color-lighter);
  --el-table-border: 1px solid var(--el-table-border-color);
}

.el-table :deep(.hover-row) {
  cursor: pointer;
}

.status-cell {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.task-progress {
  margin-top: 5px;
  width: 100%;
}

.el-table :deep(.el-table__row) {
  transition: background-color 0.2s;
}

.el-table :deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}
</style>
