<template>
  <div class="document-viewer">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <div v-else-if="error" class="error-container">
      <el-empty description="无法加载文档">
        <template #description>
          <p>{{ error }}</p>
        </template>
        <el-button @click="retry">重试</el-button>
      </el-empty>
    </div>

    <!-- PDF预览 -->
    <div v-else-if="fileType === 'pdf'" class="pdf-container">
      <iframe
        v-if="documentUrl"
        :src="documentUrl + '#toolbar=1&navpanes=1&scrollbar=1'"
        class="pdf-iframe"
        frameborder="0"
      ></iframe>
    </div>

    <!-- 图片预览 -->
    <div v-else-if="isImage" class="image-container">
      <el-image
        :src="documentUrl"
        fit="contain"
        :preview-src-list="[documentUrl]"
      />
    </div>

    <!-- 文本预览 -->
    <div v-else-if="fileType === 'text'" class="text-container">
      <pre>{{ textContent }}</pre>
    </div>

    <!-- 不支持的文件类型 -->
    <div v-else class="unsupported-container">
      <el-empty description="不支持的文件类型">
        <template #description>
          <p>当前不支持预览 {{ fileType }} 类型的文件</p>
        </template>
        <el-button @click="downloadFile">下载文件</el-button>
      </el-empty>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <el-button-group>
        <el-button size="small" @click="zoomIn" :disabled="!canZoom">
          <el-icon><ZoomIn /></el-icon>
        </el-button>
        <el-button size="small" @click="zoomOut" :disabled="!canZoom">
          <el-icon><ZoomOut /></el-icon>
        </el-button>
        <el-button size="small" @click="resetZoom" :disabled="!canZoom">
          <el-icon><FullScreen /></el-icon>
        </el-button>
      </el-button-group>

      <el-button size="small" @click="downloadFile" :disabled="!documentUrl">
        <el-icon><Download /></el-icon>
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ZoomIn, ZoomOut, FullScreen, Download } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  // 文件对象
  file: {
    type: Object,
    default: null
  },
  // 文件URL
  url: {
    type: String,
    default: ''
  },
  // 文件类型
  type: {
    type: String,
    default: ''
  },
  // 文本内容（当type为text时使用）
  text: {
    type: String,
    default: ''
  }
})

const loading = ref(true)
const error = ref(null)
const documentUrl = ref('')
const textContent = ref('')
const zoom = ref(100)

// 计算属性
const fileType = computed(() => {
  if (props.type) return props.type.toLowerCase()

  if (props.file) {
    const fileName = props.file.name || ''
    const extension = fileName.split('.').pop().toLowerCase()

    if (['pdf'].includes(extension)) return 'pdf'
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension)) return 'image'
    if (['txt', 'md', 'json', 'xml', 'html', 'css', 'js'].includes(extension)) return 'text'

    return extension
  }

  if (props.url) {
    const extension = props.url.split('.').pop().toLowerCase()
    return extension
  }

  return 'unknown'
})

const isImage = computed(() => {
  return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'image'].includes(fileType.value)
})

const canZoom = computed(() => {
  return fileType.value === 'pdf' || isImage.value
})

// 监听props变化
watch(() => props.file, loadDocument, { immediate: true })
watch(() => props.url, loadDocument, { immediate: true })
watch(() => props.text, () => {
  if (props.text) {
    textContent.value = props.text
    loading.value = false
  }
}, { immediate: true })

// 方法
function loadDocument() {
  loading.value = true
  error.value = null

  try {
    // 如果提供了URL，直接使用
    if (props.url) {
      documentUrl.value = props.url
      loading.value = false
      return
    }

    // 如果提供了文件对象
    if (props.file) {
      // 如果是文本文件，读取内容
      if (fileType.value === 'text') {
        const reader = new FileReader()
        reader.onload = (e) => {
          textContent.value = e.target.result
          loading.value = false
        }
        reader.onerror = () => {
          error.value = '无法读取文件内容'
          loading.value = false
        }
        reader.readAsText(props.file)
      } else {
        // 其他文件类型，创建URL
        documentUrl.value = URL.createObjectURL(props.file)
        loading.value = false
      }
      return
    }

    // 如果提供了文本内容
    if (props.text) {
      textContent.value = props.text
      loading.value = false
      return
    }

    // 没有提供任何内容
    error.value = '未提供文档内容'
    loading.value = false
  } catch (err) {
    console.error('加载文档失败:', err)
    error.value = '加载文档失败: ' + (err.message || '未知错误')
    loading.value = false
  }
}

function retry() {
  loadDocument()
}

function zoomIn() {
  zoom.value += 10
  applyZoom()
}

function zoomOut() {
  if (zoom.value > 10) {
    zoom.value -= 10
    applyZoom()
  }
}

function resetZoom() {
  zoom.value = 100
  applyZoom()
}

function applyZoom() {
  // 应用缩放
  const container = document.querySelector('.pdf-iframe, .image-container img')
  if (container) {
    container.style.transform = `scale(${zoom.value / 100})`
    container.style.transformOrigin = 'top left'
  }
}

function downloadFile() {
  if (!documentUrl.value && !textContent.value) {
    ElMessage.warning('没有可下载的文件')
    return
  }

  try {
    if (documentUrl.value) {
      // 创建一个链接并点击它来下载文件
      const a = document.createElement('a')
      a.href = documentUrl.value
      a.download = props.file?.name || 'document.' + fileType.value
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
    } else if (textContent.value) {
      // 为文本内容创建一个Blob并下载
      const blob = new Blob([textContent.value], { type: 'text/plain' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = props.file?.name || 'document.txt'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }
  } catch (err) {
    console.error('下载文件失败:', err)
    ElMessage.error('下载文件失败: ' + (err.message || '未知错误'))
  }
}

// 组件卸载时清理资源
onMounted(() => {
  return () => {
    if (documentUrl.value && documentUrl.value.startsWith('blob:')) {
      URL.revokeObjectURL(documentUrl.value)
    }
  }
})
</script>

<style scoped>
.document-viewer {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #f5f5f5;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}

.loading-container,
.error-container,
.unsupported-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;
}

.pdf-container,
.image-container,
.text-container {
  flex: 1;
  overflow: auto;
  padding: 10px;
  position: relative;
  height: calc(100% - 40px); /* 减去工具栏高度 */
}

.pdf-iframe {
  width: 100%;
  height: 100%;
  border: none;
  display: block;
}

.image-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-container .el-image {
  max-width: 100%;
  max-height: 100%;
}

.text-container {
  background-color: white;
  padding: 20px;
  border-radius: 4px;
}

.text-container pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  font-family: 'Courier New', Courier, monospace;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  background-color: #f0f0f0;
  border-top: 1px solid #ddd;
}
</style>
