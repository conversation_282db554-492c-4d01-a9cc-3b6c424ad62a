<template>
  <div
    class="split-pane"
    :class="{ 'split-pane-horizontal': direction === 'horizontal', 'split-pane-vertical': direction === 'vertical' }"
  >
    <div class="split-pane-left" :style="leftStyle">
      <slot name="left"></slot>
    </div>

    <div
      class="split-pane-resizer"
      :class="{ 'split-pane-resizer-horizontal': direction === 'horizontal', 'split-pane-resizer-vertical': direction === 'vertical' }"
      @mousedown="startResize"
    ></div>

    <div class="split-pane-right" :style="rightStyle">
      <slot name="right"></slot>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  // 分割方向：horizontal(左右分割) 或 vertical(上下分割)
  direction: {
    type: String,
    default: 'horizontal',
    validator: (value) => ['horizontal', 'vertical'].includes(value)
  },
  // 初始分割比例（百分比，0-100）
  initialSplit: {
    type: Number,
    default: 50,
    validator: (value) => value >= 0 && value <= 100
  },
  // 最小分割比例（百分比，0-100）
  minSplit: {
    type: Number,
    default: 10,
    validator: (value) => value >= 0 && value <= 100
  },
  // 最大分割比例（百分比，0-100）
  maxSplit: {
    type: Number,
    default: 90,
    validator: (value) => value >= 0 && value <= 100
  }
})

const emit = defineEmits(['update:split', 'resize-start', 'resize-end'])

// 当前分割比例
const splitPercent = ref(props.initialSplit)

// 是否正在调整大小
const isResizing = ref(false)

// 计算左侧样式
const leftStyle = computed(() => {
  return {
    [props.direction === 'horizontal' ? 'width' : 'height']: `${splitPercent.value}%`
  }
})

// 计算右侧样式
const rightStyle = computed(() => {
  return {
    [props.direction === 'horizontal' ? 'width' : 'height']: `${100 - splitPercent.value}%`
  }
})

// 开始调整大小
function startResize(e) {
  e.preventDefault()
  isResizing.value = true

  // 发出调整开始事件
  emit('resize-start')

  // 添加事件监听
  document.addEventListener('mousemove', onMouseMove)
  document.addEventListener('mouseup', stopResize)

  // 防止文本选择
  document.body.style.userSelect = 'none'
}

// 鼠标移动时调整大小
function onMouseMove(e) {
  if (!isResizing.value) return

  // 获取分割面板元素
  const container = document.querySelector('.split-pane')
  if (!container) return

  const rect = container.getBoundingClientRect()
  let newSplitPercent

  if (props.direction === 'horizontal') {
    // 计算水平方向的分割比例
    newSplitPercent = ((e.clientX - rect.left) / rect.width) * 100
  } else {
    // 计算垂直方向的分割比例
    newSplitPercent = ((e.clientY - rect.top) / rect.height) * 100
  }

  // 限制在最小和最大范围内
  newSplitPercent = Math.max(props.minSplit, Math.min(props.maxSplit, newSplitPercent))

  // 更新分割比例
  splitPercent.value = newSplitPercent

  // 发出更新事件
  emit('update:split', newSplitPercent)
}

// 停止调整大小
function stopResize() {
  isResizing.value = false

  // 移除事件监听
  document.removeEventListener('mousemove', onMouseMove)
  document.removeEventListener('mouseup', stopResize)

  // 恢复文本选择
  document.body.style.userSelect = ''

  // 发出调整结束事件
  emit('resize-end', splitPercent.value)
}

// 组件挂载时
onMounted(() => {
  // 确保初始分割比例在有效范围内
  splitPercent.value = Math.max(props.minSplit, Math.min(props.maxSplit, props.initialSplit))
})

// 组件卸载时
onUnmounted(() => {
  // 确保清理事件监听
  if (isResizing.value) {
    document.removeEventListener('mousemove', onMouseMove)
    document.removeEventListener('mouseup', stopResize)
    document.body.style.userSelect = ''
  }
})
</script>

<style scoped>
.split-pane {
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.split-pane-horizontal {
  flex-direction: row;
}

.split-pane-vertical {
  flex-direction: column;
}

.split-pane-left,
.split-pane-right {
  overflow: hidden;
  position: relative;
}

.split-pane-resizer {
  background-color: #e0e0e0;
  position: relative;
  z-index: 10;
  flex-shrink: 0;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.split-pane-resizer:hover,
.split-pane-resizer:active {
  background-color: #2196f3;
}

.split-pane-resizer::after {
  content: "";
  position: absolute;
  background-color: rgba(0, 0, 0, 0.1);
}

.split-pane-resizer-horizontal::after {
  width: 1px;
  height: 30px;
}

.split-pane-resizer-vertical::after {
  width: 30px;
  height: 1px;
}

.split-pane-resizer-horizontal {
  width: 8px;
  cursor: col-resize;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
}

.split-pane-resizer-vertical {
  height: 8px;
  cursor: row-resize;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
}
</style>
