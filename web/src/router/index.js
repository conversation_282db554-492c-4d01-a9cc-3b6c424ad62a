import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'
import Extraction from '../views/Extraction.vue'
import SchemaManagement from '../views/SchemaManagement.vue'
import PromptManagement from '../views/PromptManagement.vue'
import SchemaDetail from '../views/SchemaDetail.vue'
import PromptDetail from '../views/PromptDetail.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'home',
      component: Home
    },
    {
      path: '/extraction',
      name: 'extraction',
      component: Extraction
    },
    {
      path: '/schemas',
      name: 'schemas',
      component: SchemaManagement
    },
    {
      path: '/schemas/:id(.*)',
      name: 'schema-detail',
      component: SchemaDetail
    },
    {
      path: '/prompts',
      name: 'prompts',
      component: PromptManagement
    },
    {
      path: '/prompts/:id(.*)',
      name: 'prompt-detail',
      component: PromptDetail
    }
  ]
})

export default router
