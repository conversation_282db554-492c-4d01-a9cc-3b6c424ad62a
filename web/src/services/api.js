import axios from 'axios'

const api = axios.create({
  baseURL: '/api/v1',
  timeout: 60000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 可以在这里添加认证信息等
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    return Promise.reject(error)
  }
)

// Schema相关API
export const schemaApi = {
  // 获取所有Schema
  getSchemas() {
    return api.get('/schemas')
  },

  // 获取单个Schema
  getSchema(id) {
    return api.get(`/schemas/${id}`)
  },

  // 创建Schema
  createSchema(data) {
    return api.post('/schemas', data)
  },

  // 更新Schema
  updateSchema(id, data) {
    return api.put(`/schemas/${id}`, data)
  },

  // 删除Schema
  deleteSchema(id) {
    return api.delete(`/schemas/${id}`)
  }
}

// 提示词相关API
export const promptApi = {
  // 获取所有提示词
  async getPrompts() {
    console.log('调用API获取提示词列表')
    try {
      const result = await api.get('/prompts')
      console.log('API返回提示词列表:', result)
      return result
    } catch (error) {
      console.error('获取提示词列表失败:', error)
      throw error
    }
  },

  // 获取单个提示词
  async getPrompt(id) {
    console.log('调用API获取提示词详情, ID:', id)
    try {
      const result = await api.get(`/prompts/${id}`)
      console.log('API返回提示词详情:', result)
      return result
    } catch (error) {
      console.error('获取提示词详情失败:', error)
      throw error
    }
  },

  // 创建提示词
  createPrompt(data) {
    return api.post('/prompts', data)
  },

  // 更新提示词
  updatePrompt(id, data) {
    return api.put(`/prompts/${id}`, data)
  },

  // 删除提示词
  deletePrompt(id) {
    return api.delete(`/prompts/${id}`)
  }
}

// 文档提取相关API
export const extractionApi = {
  // 上传文档并提取信息
  extractDocument(file, schemaId, options = {}) {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('schema_id', schemaId)

    if (options.promptId) {
      formData.append('prompt_id', options.promptId)
    }

    // 添加大模型参数
    if (options.llm) {
      formData.append('llm_model', options.llm.model)
      formData.append('llm_temperature', options.llm.temperature)
      formData.append('llm_max_tokens', options.llm.max_tokens)
    }

    return api.post('/extraction/file', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 从文本中提取信息
  extractFromText(text, schemaId, options = {}) {
    const payload = {
      text,
      schema_id: schemaId,
      prompt_id: options.promptId
    }

    // 添加大模型参数
    if (options.llm) {
      payload.llm = options.llm
    }

    return api.post('/extraction/text', payload)
  },

  // 获取提取历史
  getExtractionHistory() {
    return api.get('/extraction')
  },

  // 获取单个提取结果
  getExtractionResult(id) {
    return api.get(`/extraction/${id}`)
  },

  // 获取提取任务状态
  getExtractionStatus(taskId) {
    return api.get(`/extraction/status/${taskId}`)
  },

  // 取消提取任务
  cancelExtractionTask(taskId) {
    return api.post(`/extraction/cancel/${taskId}`)
  }
}

// 系统相关API
export const systemApi = {
  // 获取系统健康状态
  async getHealth() {
    try {
      // 使用axios直接调用，而不是使用api实例，避免baseURL前缀
      const response = await axios.get('/api/health')
      console.log('系统健康状态响应:', response.data)
      return response.data
    } catch (error) {
      console.error('获取系统健康状态失败:', error)
      throw error
    }
  },

  // 获取系统配置
  async getConfig() {
    try {
      // 使用axios直接调用，而不是使用api实例，避免baseURL前缀
      const response = await axios.get('/api/config')
      console.log('系统配置响应:', response.data)
      return response.data
    } catch (error) {
      console.error('获取系统配置失败:', error)
      throw error
    }
  },

  // 获取可用的大模型列表
  async getAvailableModels() {
    try {
      console.log('获取可用大模型列表')
      const response = await api.get('/llm/models')
      console.log('可用大模型列表响应:', response)
      return response
    } catch (error) {
      console.error('获取可用大模型列表失败:', error)
      throw error
    }
  }
}

export default {
  schema: schemaApi,
  prompt: promptApi,
  extraction: extractionApi,
  system: systemApi
}
