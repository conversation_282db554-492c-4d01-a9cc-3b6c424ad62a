<template>
  <el-config-provider>
    <div class="app-container">
      <el-container>
        <el-header height="60px">
          <div class="header-container">
            <div class="logo">
              <img src="@/assets/icon.png" alt="XExtract Logo" class="logo-img" />
              <h1>XExtract</h1>
            </div>
            <el-menu
              :default-active="activeIndex"
              class="el-menu-demo"
              mode="horizontal"
              router
            >
              <el-menu-item index="/">首页</el-menu-item>
              <el-menu-item index="/extraction">文档提取</el-menu-item>
              <el-menu-item index="/schemas">Schema管理</el-menu-item>
              <el-menu-item index="/prompts">提示词管理</el-menu-item>
            </el-menu>
          </div>
        </el-header>
        <el-main>
          <router-view />
        </el-main>
        <el-footer height="40px">
          <div class="footer-container">
            <p>© 2025 XExtract - 文档提取工具</p>
          </div>
        </el-footer>
      </el-container>
    </div>
  </el-config-provider>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const activeIndex = computed(() => route.path)
</script>

<style>
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

#app {
  height: 100vh;
}

.app-container {
  height: 100%;
}

.el-container {
  height: 100%;
}

.el-header {
  background-color: #fff;
  color: #333;
  border-bottom: 1px solid #eee;
  padding: 0;
}

.header-container {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.logo {
  margin-right: 20px;
  display: flex;
  align-items: center;
}

.logo-img {
  height: 32px;
  margin-right: 10px;
}

.logo h1 {
  margin: 0;
  font-size: 24px;
  color: #409EFF;
}

.el-main {
  background-color: #f5f7fa;
  padding: 10px;
  flex: 1;
  overflow: auto;
}

.el-footer {
  background-color: #fff;
  color: #999;
  text-align: center;
  border-top: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: center;
}

.footer-container p {
  margin: 0;
  font-size: 14px;
}
</style>
