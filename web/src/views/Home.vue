<template>
  <div class="home-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <div class="welcome-section">
          <h1>欢迎使用 XExtract 文档提取工具</h1>
          <p>XExtract 是一个强大的文档信息提取工具，可以从各种文档中自动提取结构化数据。</p>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="feature-section">
      <el-col :xs="24" :sm="8">
        <el-card class="feature-card">
          <template #header>
            <div class="card-header">
              <el-icon><Document /></el-icon>
              <span>文档提取</span>
            </div>
          </template>
          <div class="card-content">
            <p>上传文档，自动提取结构化数据。支持PDF、Word等多种格式。</p>
            <el-button type="primary" @click="goToExtraction">开始提取</el-button>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="8">
        <el-card class="feature-card">
          <template #header>
            <div class="card-header">
              <el-icon><Setting /></el-icon>
              <span>Schema管理</span>
            </div>
          </template>
          <div class="card-content">
            <p>创建和管理提取模式，定义需要从文档中提取的字段和规则。</p>
            <el-button type="primary" @click="goToSchemas">管理Schema</el-button>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="8">
        <el-card class="feature-card">
          <template #header>
            <div class="card-header">
              <el-icon><ChatLineRound /></el-icon>
              <span>提示词管理</span>
            </div>
          </template>
          <div class="card-content">
            <p>创建和管理提示词，优化大语言模型的提取效果。</p>
            <el-button type="primary" @click="goToPrompts">管理提示词</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统信息部分 -->
    <el-row :gutter="20" class="system-section">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <el-icon><Monitor /></el-icon>
              <span>提取系统信息</span>
            </div>
          </template>
          <div v-if="systemInfo">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="代码版本">
                {{ systemInfo.version }}
              </el-descriptions-item>
              <el-descriptions-item label="运行时间">
                {{ systemInfo.uptime }}
              </el-descriptions-item>
              <el-descriptions-item label="Git版本">
                {{ systemInfo.git_version?.commit?.substring(0, 7) || '未知' }}
                <el-tag v-if="systemInfo.git_version?.branch" size="small" type="info">
                  {{ systemInfo.git_version.branch }}
                </el-tag>
                <el-tag v-if="systemInfo.git_version?.dirty" size="small" type="warning">
                  有未提交更改
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="启动时间">
                {{ formatDate(systemInfo.start_time) }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <el-skeleton v-else :rows="2" animated />
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="recent-section">
      <el-col :span="24">
        <h2>最近提取</h2>
        <el-table
          v-if="extractionHistory.length > 0"
          :data="extractionHistory"
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="180" />
          <el-table-column prop="schema_name" label="Schema" width="180" />
          <el-table-column prop="file_name" label="文件名" />
          <el-table-column prop="extraction_time" label="提取时间">
            <template #default="scope">
              {{ formatDate(scope.row.extraction_time) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态">
            <template #default="scope">
              <el-tag
                :type="scope.row.status === 'success' ? 'success' : 'danger'"
              >
                {{ scope.row.status === 'success' ? '成功' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button
                size="small"
                @click="viewExtractionResult(scope.row.id)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-empty v-else description="暂无提取记录" />
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { Document, Setting, ChatLineRound, Monitor } from '@element-plus/icons-vue'
import { useExtractionStore } from '../stores/extraction'
import { systemApi } from '../services/api'

const router = useRouter()
const extractionStore = useExtractionStore()
const extractionHistory = ref([])
const systemInfo = ref(null)

onMounted(async () => {
  // 获取提取历史
  try {
    await extractionStore.fetchExtractionHistory()
    extractionHistory.value = extractionStore.extractionHistory
  } catch (error) {
    console.error('获取提取历史失败:', error)
  }

  // 获取系统信息
  try {
    const healthData = await systemApi.getHealth()
    console.log('获取到的系统信息:', healthData)
    systemInfo.value = healthData
  } catch (error) {
    console.error('获取系统信息失败:', error)
  }
})

const goToExtraction = () => {
  router.push('/extraction')
}

const goToSchemas = () => {
  router.push('/schemas')
}

const goToPrompts = () => {
  router.push('/prompts')
}

const viewExtractionResult = (id) => {
  router.push(`/extraction/${id}`)
}

const formatDate = (dateString) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  return date.toLocaleString()
}
</script>

<style scoped>
.home-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.welcome-section {
  text-align: center;
  margin-bottom: 40px;
}

.welcome-section h1 {
  font-size: 32px;
  color: #303133;
  margin-bottom: 16px;
}

.welcome-section p {
  font-size: 16px;
  color: #606266;
  max-width: 800px;
  margin: 0 auto;
}

.feature-section {
  margin-bottom: 40px;
}

.feature-card {
  height: 100%;
  transition: all 0.3s;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}

.card-header .el-icon {
  margin-right: 8px;
  font-size: 20px;
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: 120px;
}

.card-content p {
  margin-bottom: 20px;
  text-align: center;
  color: #606266;
}

.system-section {
  margin-bottom: 40px;
}

.recent-section h2 {
  font-size: 24px;
  color: #303133;
  margin-bottom: 20px;
}

@media (max-width: 768px) {
  .feature-card {
    margin-bottom: 20px;
  }
}
</style>
