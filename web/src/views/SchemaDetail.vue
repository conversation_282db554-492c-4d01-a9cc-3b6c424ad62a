<template>
  <div class="schema-detail">
    <div class="page-header">
      <el-button @click="goBack">
        <el-icon><ArrowLeft /></el-icon> 返回
      </el-button>
      <h1>Schema详情: {{ schema?.name }}</h1>
    </div>

    <el-card v-if="loading">
      <el-skeleton :rows="10" animated />
    </el-card>

    <template v-else-if="schema">
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-button type="primary" @click="showEditDialog">
              <el-icon><Edit /></el-icon> 编辑
            </el-button>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="ID">{{ schema.id }}</el-descriptions-item>
          <el-descriptions-item label="名称">{{ schema.name }}</el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">{{ schema.description }}</el-descriptions-item>
          <el-descriptions-item label="类型">{{ schema.type }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(schema.created_at) }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <el-card class="fields-card">
        <template #header>
          <div class="card-header">
            <span>字段定义</span>
            <el-button type="primary" @click="showAddFieldDialog">
              <el-icon><Plus /></el-icon> 添加字段
            </el-button>
          </div>
        </template>
        <el-table :data="schema.fields" style="width: 100%">
          <el-table-column prop="code" label="字段代码" width="180" />
          <el-table-column prop="name" label="名称" width="180" />
          <el-table-column prop="type" label="类型">
            <template #default="scope">
              <el-tag>{{ scope.row.type }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" />
          <el-table-column prop="required" label="必填">
            <template #default="scope">
              <el-tag :type="scope.row.required ? 'success' : 'info'">
                {{ scope.row.required ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="extraction_method" label="提取方法">
            <template #default="scope">
              <el-tag type="warning" v-if="scope.row.extraction_method === 'llm'">大模型</el-tag>
              <el-tag type="success" v-else-if="scope.row.extraction_method === 'regex'">正则表达式</el-tag>
              <el-tag type="info" v-else-if="scope.row.extraction_method === 'hybrid'">混合</el-tag>
              <el-tag v-else>{{ scope.row.extraction_method }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template #default="scope">
              <el-button
                size="small"
                @click="editField(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="confirmDeleteField(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <el-card class="related-card">
        <template #header>
          <div class="card-header">
            <span>关联提示词</span>
          </div>
        </template>
        <el-table :data="relatedPrompts" style="width: 100%">
          <el-table-column prop="id" label="ID" width="180" />
          <el-table-column prop="name" label="名称" width="180" />
          <el-table-column prop="description" label="描述" />
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <el-button
                size="small"
                @click="viewPrompt(scope.row.id)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-empty v-if="relatedPrompts.length === 0" description="暂无关联提示词" />
      </el-card>
    </template>

    <el-empty v-else description="未找到Schema" />

    <!-- 编辑Schema对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑Schema"
      width="50%"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入Schema名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="editForm.description"
            type="textarea"
            placeholder="请输入Schema描述"
          />
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="editForm.type" placeholder="请选择Schema类型">
            <el-option label="公告" value="announcement" />
            <el-option label="财报" value="financial_report" />
            <el-option label="IPO" value="ipo" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updateSchema">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加字段对话框 -->
    <el-dialog
      v-model="fieldDialogVisible"
      :title="isEditingField ? '编辑字段' : '添加字段'"
      width="50%"
    >
      <el-form
        ref="fieldFormRef"
        :model="fieldForm"
        :rules="fieldRules"
        label-width="100px"
      >
        <el-form-item label="ID" prop="id" v-if="!isEditingField">
          <el-input v-model="fieldForm.id" placeholder="请输入字段ID" />
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="fieldForm.name" placeholder="请输入字段名称" />
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="fieldForm.type" placeholder="请选择字段类型">
            <el-option label="文本" value="string" />
            <el-option label="数字" value="number" />
            <el-option label="布尔值" value="boolean" />
            <el-option label="日期" value="date" />
            <el-option label="对象" value="object" />
            <el-option label="数组" value="array" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="fieldForm.description"
            type="textarea"
            placeholder="请输入字段描述"
          />
        </el-form-item>
        <el-form-item label="必填">
          <el-switch v-model="fieldForm.required" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="fieldDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveField">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 删除字段确认对话框 -->
    <el-dialog
      v-model="deleteFieldDialogVisible"
      title="确认删除"
      width="30%"
    >
      <p>确定要删除字段 "{{ fieldToDelete?.name }}" 吗？此操作不可恢复。</p>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteFieldDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="deleteField">
            确认删除
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Edit, Plus } from '@element-plus/icons-vue'
import { useSchemaStore } from '../stores/schema'
import { usePromptStore } from '../stores/prompt'

const route = useRoute()
const router = useRouter()
const schemaStore = useSchemaStore()
const promptStore = usePromptStore()

const schemaId = computed(() => route.params.id)
const loading = ref(true)
const schema = ref(null)
const relatedPrompts = ref([])

const editDialogVisible = ref(false)
const fieldDialogVisible = ref(false)
const deleteFieldDialogVisible = ref(false)
const isEditingField = ref(false)
const fieldToDelete = ref(null)

const editFormRef = ref(null)
const fieldFormRef = ref(null)

const editForm = ref({
  name: '',
  description: '',
  type: ''
})

const fieldForm = ref({
  id: '',
  name: '',
  type: 'string',
  description: '',
  required: false
})

const formRules = {
  name: [
    { required: true, message: '请输入Schema名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择Schema类型', trigger: 'change' }
  ]
}

const fieldRules = {
  id: [
    { required: true, message: '请输入字段ID', trigger: 'blur' },
    { pattern: /^[a-z0-9_]+$/, message: 'ID只能包含小写字母、数字和下划线', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入字段名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择字段类型', trigger: 'change' }
  ]
}

onMounted(async () => {
  await loadData()
})

const loadData = async () => {
  loading.value = true
  try {
    // 加载Schema详情
    await schemaStore.fetchSchema(schemaId.value)
    schema.value = schemaStore.currentSchema

    // 加载关联的提示词
    await promptStore.fetchPrompts()
    relatedPrompts.value = promptStore.prompts.filter(
      prompt => prompt.schema_id === schemaId.value
    )
  } catch (error) {
    ElMessage.error('加载数据失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.push('/schemas')
}

const showEditDialog = () => {
  editForm.value = {
    name: schema.value.name,
    description: schema.value.description,
    type: schema.value.type
  }
  editDialogVisible.value = true
}

const updateSchema = async () => {
  if (!editFormRef.value) return

  await editFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await schemaStore.updateSchema(schemaId.value, editForm.value)
        ElMessage.success('更新Schema成功')
        editDialogVisible.value = false

        // 重新加载Schema详情
        await loadData()
      } catch (error) {
        ElMessage.error('更新Schema失败')
        console.error(error)
      }
    }
  })
}

const showAddFieldDialog = () => {
  isEditingField.value = false
  fieldForm.value = {
    id: '',
    name: '',
    type: 'string',
    description: '',
    required: false
  }
  fieldDialogVisible.value = true
}

const editField = (field) => {
  isEditingField.value = true
  fieldForm.value = { ...field }
  fieldDialogVisible.value = true
}

const saveField = async () => {
  if (!fieldFormRef.value) return

  await fieldFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (isEditingField.value) {
          // 更新字段
          const updatedFields = schema.value.fields.map(field =>
            field.id === fieldForm.value.id ? fieldForm.value : field
          )
          await schemaStore.updateSchema(schemaId.value, {
            fields: updatedFields
          })
          ElMessage.success('更新字段成功')
        } else {
          // 添加字段
          const newFields = [...(schema.value.fields || []), fieldForm.value]
          await schemaStore.updateSchema(schemaId.value, {
            fields: newFields
          })
          ElMessage.success('添加字段成功')
        }

        fieldDialogVisible.value = false

        // 重新加载Schema详情
        await loadData()
      } catch (error) {
        ElMessage.error(isEditingField.value ? '更新字段失败' : '添加字段失败')
        console.error(error)
      }
    }
  })
}

const confirmDeleteField = (field) => {
  fieldToDelete.value = field
  deleteFieldDialogVisible.value = true
}

const deleteField = async () => {
  if (!fieldToDelete.value) return

  try {
    const updatedFields = schema.value.fields.filter(
      field => field.id !== fieldToDelete.value.id
    )
    await schemaStore.updateSchema(schemaId.value, {
      fields: updatedFields
    })
    ElMessage.success('删除字段成功')
    deleteFieldDialogVisible.value = false

    // 重新加载Schema详情
    await loadData()
  } catch (error) {
    ElMessage.error('删除字段失败')
    console.error(error)
  }
}

const viewPrompt = (id) => {
  router.push(`/prompts/${id}`)
}

const formatDate = (dateString) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  return date.toLocaleString()
}
</script>

<style scoped>
.schema-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 0 20px;
  font-size: 24px;
  color: #303133;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-card,
.fields-card,
.related-card {
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
