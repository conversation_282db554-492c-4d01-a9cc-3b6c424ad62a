<template>
  <div class="prompt-management">
    <h1>提示词管理</h1>

    <el-card class="action-card">
      <el-button type="primary" @click="showCreateDialog">
        <el-icon><Plus /></el-icon> 创建提示词
      </el-button>
    </el-card>

    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="prompts"
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="180">
          <template #default="scope">
            <router-link :to="`/prompts/${scope.row.id}`">{{ scope.row.id }}</router-link>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="名称" width="180">
          <template #default="scope">
            <router-link :to="`/prompts/${scope.row.id}`">{{ scope.row.name }}</router-link>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="schema_id" label="关联Schema">
          <template #default="scope">
            <router-link v-if="scope.row.schema_id" :to="`/schemas/${scope.row.schema_id}`">{{ scope.row.schema_id }}</router-link>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button
              size="small"
              type="danger"
              @click="confirmDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 创建提示词对话框 -->
    <el-dialog
      v-model="createDialogVisible"
      title="创建提示词"
      width="50%"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="ID" prop="id">
          <el-input v-model="createForm.id" placeholder="请输入提示词ID" />
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="createForm.name" placeholder="请输入提示词名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="createForm.description"
            type="textarea"
            placeholder="请输入提示词描述"
          />
        </el-form-item>
        <el-form-item label="关联Schema" prop="schema_id">
          <el-select
            v-model="createForm.schema_id"
            placeholder="请选择关联的Schema"
            style="width: 100%"
          >
            <el-option
              v-for="schema in schemas"
              :key="schema.id"
              :label="schema.name"
              :value="schema.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="提示词内容" prop="content">
          <el-input
            v-model="createForm.content"
            type="textarea"
            :rows="10"
            placeholder="请输入提示词内容"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="createDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="createPrompt">
            创建
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog
      v-model="deleteDialogVisible"
      title="确认删除"
      width="30%"
    >
      <p>确定要删除提示词 "{{ promptToDelete?.name }}" 吗？此操作不可恢复。</p>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="deletePrompt">
            确认删除
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { usePromptStore } from '../stores/prompt'
import { useSchemaStore } from '../stores/schema'

const router = useRouter()
const promptStore = usePromptStore()
const schemaStore = useSchemaStore()

const loading = ref(false)
const prompts = ref([])
const schemas = ref([])
const createDialogVisible = ref(false)
const deleteDialogVisible = ref(false)
const promptToDelete = ref(null)
const createFormRef = ref(null)

const createForm = ref({
  id: '',
  name: '',
  description: '',
  schema_id: '',
  content: ''
})

const formRules = {
  id: [
    { required: true, message: '请输入提示词ID', trigger: 'blur' },
    { pattern: /^[a-z0-9_-]+$/, message: 'ID只能包含小写字母、数字、下划线和连字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入提示词名称', trigger: 'blur' }
  ],
  schema_id: [
    { required: true, message: '请选择关联的Schema', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入提示词内容', trigger: 'blur' }
  ]
}

onMounted(async () => {
  loading.value = true
  try {
    // 加载提示词列表
    await promptStore.fetchPrompts()
    prompts.value = promptStore.prompts

    // 加载Schema列表
    await schemaStore.fetchSchemas()
    schemas.value = schemaStore.schemas
  } catch (error) {
    ElMessage.error('加载数据失败')
    console.error(error)
  } finally {
    loading.value = false
  }
})

const showCreateDialog = () => {
  createForm.value = {
    id: '',
    name: '',
    description: '',
    schema_id: '',
    content: ''
  }
  createDialogVisible.value = true
}

const createPrompt = async () => {
  if (!createFormRef.value) return

  await createFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await promptStore.createPrompt(createForm.value)
        ElMessage.success('创建提示词成功')
        createDialogVisible.value = false

        // 重新加载提示词列表
        await promptStore.fetchPrompts()
        prompts.value = promptStore.prompts
      } catch (error) {
        ElMessage.error('创建提示词失败')
        console.error(error)
      }
    }
  })
}

const viewPrompt = (id) => {
  router.push(`/prompts/${id}`)
}

const confirmDelete = (prompt) => {
  promptToDelete.value = prompt
  deleteDialogVisible.value = true
}

const deletePrompt = async () => {
  if (!promptToDelete.value) return

  try {
    await promptStore.deletePrompt(promptToDelete.value.id)
    ElMessage.success('删除提示词成功')
    deleteDialogVisible.value = false

    // 重新加载提示词列表
    await promptStore.fetchPrompts()
    prompts.value = promptStore.prompts
  } catch (error) {
    ElMessage.error('删除提示词失败')
    console.error(error)
  }
}
</script>

<style scoped>
.prompt-management {
  padding: 20px;
}

.prompt-management h1 {
  margin-bottom: 20px;
  font-size: 24px;
  color: #303133;
}

.action-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
