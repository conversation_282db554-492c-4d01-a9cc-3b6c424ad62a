<template>
  <div class="prompt-detail">
    <div class="page-header">
      <el-button @click="goBack">
        <el-icon><ArrowLeft /></el-icon> 返回
      </el-button>
      <h1>提示词详情: {{ prompt?.name }}</h1>
    </div>

    <el-card v-if="loading">
      <el-skeleton :rows="10" animated />
    </el-card>

    <template v-else-if="prompt">
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-button type="primary" @click="showEditDialog">
              <el-icon><Edit /></el-icon> 编辑
            </el-button>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="ID">{{ prompt.id }}</el-descriptions-item>
          <el-descriptions-item label="名称">{{ prompt.name }}</el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">{{
            prompt.description
          }}</el-descriptions-item>
          <el-descriptions-item label="关联Schema">
            <router-link v-if="prompt.schema_id" :to="`/schemas/${prompt.schema_id}`">
              {{ relatedSchema?.name || prompt.schema_id }}
            </router-link>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{
            formatDate(prompt.created_at)
          }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <el-card class="content-card">
        <template #header>
          <div class="card-header">
            <span>提示词内容</span>
          </div>
        </template>
        <div class="prompt-content">
          <div v-if="prompt && prompt.content" class="content-wrapper">
            <h3>提示词内容</h3>

            <!-- 使用el-card显示内容 -->
            <el-card class="content-card">
              <div class="content-display">{{ displayContent }}</div>
            </el-card>

            <!-- 使用el-input显示内容 -->
            <h3>原始内容</h3>
            <el-input
              type="textarea"
              :modelValue="prompt.content"
              :rows="10"
              readonly
              class="content-textarea"
            ></el-input>
          </div>
          <div v-else class="no-content">
            <el-empty description="暂无内容" />
          </div>

          <!-- 调试按钮 -->
          <div class="debug-controls">
            <el-button @click="showDebugInfo = !showDebugInfo" size="small" type="info">
              {{ showDebugInfo ? "隐藏调试信息" : "显示调试信息" }}
            </el-button>
            <el-button @click="reloadData" size="small" type="primary">
              重新加载数据
            </el-button>
          </div>

          <!-- 调试信息面板 -->
          <div v-if="showDebugInfo" class="debug-info">
            <h3>调试信息</h3>
            <el-tabs>
              <el-tab-pane label="基本信息">
                <el-descriptions :column="1" border>
                  <el-descriptions-item label="提示词ID">{{
                    promptId
                  }}</el-descriptions-item>
                  <el-descriptions-item label="提示词对象类型">{{
                    typeof prompt
                  }}</el-descriptions-item>
                  <el-descriptions-item label="提示词内容类型">{{
                    typeof prompt?.content
                  }}</el-descriptions-item>
                  <el-descriptions-item label="提示词内容长度">{{
                    prompt?.content?.length || 0
                  }}</el-descriptions-item>
                  <el-descriptions-item label="提示词内容前50个字符">{{
                    prompt?.content?.substring(0, 50) || ""
                  }}</el-descriptions-item>
                </el-descriptions>
              </el-tab-pane>
              <el-tab-pane label="完整JSON">
                <pre class="json-preview">{{ JSON.stringify(prompt, null, 2) }}</pre>
              </el-tab-pane>
              <el-tab-pane label="原始内容">
                <div class="raw-content">
                  <p><strong>内容类型:</strong> {{ typeof prompt?.content }}</p>
                  <p><strong>内容长度:</strong> {{ prompt?.content?.length || 0 }}</p>
                  <pre class="raw-preview">{{ prompt?.content || "无内容" }}</pre>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </el-card>

      <el-card class="test-card">
        <template #header>
          <div class="card-header">
            <span>提示词测试</span>
          </div>
        </template>
        <div class="test-area">
          <el-form :model="testForm" label-position="top">
            <el-form-item label="测试文本">
              <el-input
                v-model="testForm.text"
                type="textarea"
                :rows="5"
                placeholder="请输入要测试的文本"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="testPrompt" :loading="testing">
                测试提取
              </el-button>
            </el-form-item>
          </el-form>

          <div v-if="testResult" class="test-result">
            <h3>提取结果</h3>
            <el-divider />
            <pre>{{ JSON.stringify(testResult, null, 2) }}</pre>
          </div>
        </div>
      </el-card>
    </template>

    <el-empty v-else description="未找到提示词" />

    <!-- 编辑提示词对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑提示词" width="60%">
      <el-form ref="editFormRef" :model="editForm" :rules="formRules" label-width="100px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入提示词名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="editForm.description"
            type="textarea"
            placeholder="请输入提示词描述"
          />
        </el-form-item>
        <el-form-item label="关联Schema" prop="schema_id">
          <el-select
            v-model="editForm.schema_id"
            placeholder="请选择关联的Schema"
            style="width: 100%"
          >
            <el-option
              v-for="schema in schemas"
              :key="schema.id"
              :label="schema.name"
              :value="schema.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="提示词内容" prop="content">
          <el-input
            v-model="editForm.content"
            type="textarea"
            :rows="15"
            placeholder="请输入提示词内容"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updatePrompt"> 保存 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { ArrowLeft, Edit } from "@element-plus/icons-vue";
import axios from "axios";
import { usePromptStore } from "../stores/prompt";
import { useSchemaStore } from "../stores/schema";
import { extractionApi } from "../services/api";

const route = useRoute();
const router = useRouter();
const promptStore = usePromptStore();
const schemaStore = useSchemaStore();

const promptId = computed(() => {
  console.log("路由参数:", route.params);
  return route.params.id;
});

// 计算属性：显示内容
const displayContent = computed(() => {
  if (!prompt.value || !prompt.value.content) return "暂无内容";
  return prompt.value.content;
});
const loading = ref(true);
const prompt = ref(null);
const schemas = ref([]);
const relatedSchema = ref(null);
const showDebugInfo = ref(false);

const editDialogVisible = ref(false);
const editFormRef = ref(null);

const editForm = ref({
  name: "",
  description: "",
  schema_id: "",
  content: "",
});

const testForm = ref({
  text: "",
});

const testing = ref(false);
const testResult = ref(null);

const formRules = {
  name: [{ required: true, message: "请输入提示词名称", trigger: "blur" }],
  schema_id: [{ required: true, message: "请选择关联的Schema", trigger: "change" }],
  content: [{ required: true, message: "请输入提示词内容", trigger: "blur" }],
};

onMounted(async () => {
  await loadData();
});

// 重新加载数据
const reloadData = async () => {
  try {
    loading.value = true;
    ElMessage.info("正在重新加载数据...");

    // 清空当前数据
    prompt.value = null;

    // 直接从API获取提示词详情
    const url = `/api/v1/prompts/${promptId.value}`;
    console.log("重新加载数据，请求URL:", url);

    const response = await axios.get(url);
    console.log("API响应:", response);

    if (response.data && response.data.id) {
      prompt.value = response.data;
      console.log("成功重新加载提示词数据:", prompt.value);
      ElMessage.success("数据重新加载成功");
    } else {
      ElMessage.warning("API返回数据为空");
    }
  } catch (error) {
    console.error("重新加载数据失败:", error);
    ElMessage.error("重新加载数据失败");
  } finally {
    loading.value = false;
  }
};

const loadData = async () => {
  loading.value = true;
  try {
    console.log("开始加载提示词详情，ID:", promptId.value);

    // 加载提示词详情
    await promptStore.fetchPrompt(promptId.value);
    console.log("提示词详情加载结果:", promptStore.currentPrompt);

    // 直接使用axios从API获取提示词详情
    try {
      const url = `/api/v1/prompts/${promptId.value}`;
      console.log("请求URL:", url);

      // 使用axios发送请求
      const response = await axios.get(url);
      console.log("API响应:", response);

      if (response.data && response.data.id) {
        // 直接使用API返回的数据，但确保所有字段都存在
        const data = response.data;

        // 创建一个新对象，确保所有字段都存在
        const promptData = {
          id: data.id || "",
          name: data.name || "",
          description: data.description || "",
          content: data.content || "",
          schema_id: data.schema_id || "",
          created_at: data.created_at || new Date().toISOString(),
          updated_at: data.updated_at || new Date().toISOString(),
        };

        // 打印内容的类型和长度
        console.log("提示词内容类型:", typeof promptData.content);
        console.log("提示词内容长度:", promptData.content.length);
        console.log("提示词内容前100个字符:", promptData.content.substring(0, 100));

        // 设置提示词数据
        prompt.value = promptData;
        console.log("成功设置提示词数据:", prompt.value);

        // 显示成功消息
        ElMessage.success("提示词数据加载成功");
      } else {
        // 如果API返回为空，使用store中的数据
        prompt.value = promptStore.currentPrompt;
        console.log("API返回为空，使用store中的数据:", prompt.value);

        // 显示警告消息
        ElMessage.warning("API返回数据为空，使用缓存数据");
      }
    } catch (error) {
      console.error("获取提示词详情失败:", error);

      // 如果API请求失败，使用store中的数据
      prompt.value = promptStore.currentPrompt;
      console.log("API请求失败，使用store中的数据:", prompt.value);

      // 显示错误消息
      ElMessage.error("获取提示词详情失败，请稍后重试");
    }

    console.log("设置当前提示词:", prompt.value);

    // 加载Schema列表
    await schemaStore.fetchSchemas();
    schemas.value = schemaStore.schemas;
    console.log("Schema列表加载完成，数量:", schemas.value.length);

    // 查找关联的Schema
    if (prompt.value?.schema_id) {
      relatedSchema.value = schemas.value.find(
        (schema) => schema.id === prompt.value.schema_id
      );
      console.log("找到关联Schema:", relatedSchema.value);
    }
  } catch (error) {
    ElMessage.error("加载数据失败");
    console.error("加载数据失败:", error);
  } finally {
    loading.value = false;
    console.log("数据加载完成，当前提示词:", prompt.value);
  }
};

const goBack = () => {
  router.push("/prompts");
};

const showEditDialog = () => {
  editForm.value = {
    name: prompt.value.name,
    description: prompt.value.description,
    schema_id: prompt.value.schema_id,
    content: prompt.value.content,
  };
  editDialogVisible.value = true;
};

const updatePrompt = async () => {
  if (!editFormRef.value) return;

  await editFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await promptStore.updatePrompt(promptId.value, editForm.value);
        ElMessage.success("更新提示词成功");
        editDialogVisible.value = false;

        // 重新加载提示词详情
        await loadData();
      } catch (error) {
        ElMessage.error("更新提示词失败");
        console.error(error);
      }
    }
  });
};

const testPrompt = async () => {
  if (!testForm.value.text) {
    ElMessage.warning("请输入测试文本");
    return;
  }

  testing.value = true;
  testResult.value = null;

  try {
    const result = await extractionApi.extractFromText(
      testForm.value.text,
      prompt.value.schema_id,
      { promptId: promptId.value }
    );
    testResult.value = result;
  } catch (error) {
    ElMessage.error("测试提取失败");
    console.error(error);
  } finally {
    testing.value = false;
  }
};

const formatDate = (dateString) => {
  if (!dateString) return "未知";
  const date = new Date(dateString);
  return date.toLocaleString();
};
</script>

<style scoped>
.prompt-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 0 20px;
  font-size: 24px;
  color: #303133;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-card,
.content-card,
.test-card {
  margin-bottom: 20px;
}

.prompt-content {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  font-family: monospace;
  white-space: pre-wrap;
  max-height: 400px;
  overflow-y: auto;
}

.test-area {
  display: flex;
  flex-direction: column;
}

.test-result {
  margin-top: 20px;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

.test-result pre {
  font-family: monospace;
  white-space: pre-wrap;
  max-height: 300px;
  overflow-y: auto;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.debug-info {
  margin-top: 20px;
  padding: 10px;
  background-color: #f0f9ff;
  border: 1px solid #d9ecff;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
}

.content-wrapper {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  font-family: monospace;
  white-space: pre-wrap;
  max-height: 400px;
  overflow-y: auto;
}

.no-content {
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
  text-align: center;
}

.debug-controls {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.json-preview {
  white-space: pre-wrap;
  max-height: 300px;
  overflow-y: auto;
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
}

.raw-preview {
  white-space: pre-wrap;
  max-height: 300px;
  overflow-y: auto;
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  border: 1px solid #e0e0e0;
}

.content-html {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  font-family: monospace;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
  margin-bottom: 15px;
  border: 1px solid #e0e0e0;
}

.content-textarea {
  margin-top: 15px;
  margin-bottom: 15px;
  font-family: monospace;
}

.content-text {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  font-family: monospace;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
  margin-bottom: 15px;
  border: 1px solid #e0e0e0;
}

.content-display {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  font-family: monospace;
  white-space: pre-wrap;
  max-height: 300px;
  overflow-y: auto;
  font-size: 14px;
  line-height: 1.5;
}

.content-card {
  margin-bottom: 20px;
}

h3 {
  margin-top: 20px;
  margin-bottom: 10px;
  font-size: 16px;
  color: #303133;
}
</style>
