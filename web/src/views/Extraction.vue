<template>
  <div class="extraction-container">
    <h1>文档提取</h1>

    <el-card class="extraction-card">
      <el-tabs v-model="activeTab" class="extraction-tabs">
        <el-tab-pane label="新建提取" name="new">
          <el-steps :active="activeStep" finish-status="success" simple>
            <el-step title="选择Schema" />
            <el-step title="上传文档" />
            <el-step title="提取结果" />
          </el-steps>

      <!-- 步骤1：选择Schema -->
      <div v-if="activeStep === 0" class="step-content">
        <h2>选择Schema</h2>
        <p>请选择要使用的提取模式（Schema）</p>

        <el-select
          v-model="selectedSchemaId"
          placeholder="选择Schema"
          style="width: 100%"
        >
          <el-option
            v-for="schema in schemas"
            :key="schema.id"
            :label="schema.name"
            :value="schema.id"
          />
        </el-select>

        <div v-if="selectedSchemaId" class="schema-details">
          <h3>{{ selectedSchema?.name }}</h3>
          <p>{{ selectedSchema?.description }}</p>

          <h4>字段列表</h4>
          <el-table :data="selectedSchema?.fields || []" style="width: 100%">
            <el-table-column prop="code" label="代码" width="180" />
            <el-table-column prop="name" label="名称" width="180" />
            <el-table-column prop="type" label="类型">
              <template #default="scope">
                {{ formatFieldType(scope.row.type) }}
              </template>
            </el-table-column>
            <el-table-column prop="required" label="必填">
              <template #default="scope">
                <el-tag :type="scope.row.required ? 'danger' : 'info'">
                  {{ scope.row.required ? "是" : "否" }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="step-actions">
          <el-button type="primary" @click="nextStep" :disabled="!selectedSchemaId">
            下一步
          </el-button>
        </div>
      </div>

      <!-- 步骤2：上传文档 -->
      <div v-if="activeStep === 1" class="step-content">
        <h2>上传文档</h2>
        <p>请上传要提取信息的文档，或直接输入文本内容</p>

        <el-tabs v-model="uploadType">
          <el-tab-pane label="上传文件" name="file">
            <el-upload
              class="upload-area"
              drag
              action="#"
              :auto-upload="false"
              :on-change="handleFileChange"
              :limit="1"
            >
              <el-icon class="el-icon--upload"><upload-filled /></el-icon>
              <div class="el-upload__text">拖拽文件到此处，或 <em>点击上传</em></div>
              <template #tip>
                <div class="el-upload__tip">支持PDF、Word等格式，文件大小不超过10MB</div>
              </template>
            </el-upload>
          </el-tab-pane>

          <el-tab-pane label="输入文本" name="text">
            <el-input
              v-model="inputText"
              type="textarea"
              :rows="15"
              placeholder="请输入文本内容"
              class="text-input"
            />
          </el-tab-pane>
        </el-tabs>

        <div class="prompt-section">
          <h3>提示词设置（可选）</h3>
          <el-select
            v-model="selectedPromptId"
            placeholder="选择提示词"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="prompt in filteredPrompts"
              :key="prompt.id"
              :label="prompt.name"
              :value="prompt.id"
            />
          </el-select>

          <div v-if="selectedPromptId" class="prompt-details">
            <h4>{{ selectedPrompt?.name }}</h4>
            <p>{{ selectedPrompt?.description }}</p>
            <el-input
              v-model="selectedPrompt.content"
              type="textarea"
              :rows="5"
              readonly
            />
          </div>
        </div>

        <div class="llm-section">
          <h3>大模型设置</h3>
          <el-form :model="llmSettings" label-position="top">
            <el-form-item label="选择大模型">
              <el-select
                v-model="llmSettings.model"
                placeholder="选择大模型"
                style="width: 100%"
              >
                <el-option
                  v-for="model in availableModels"
                  :key="model.id"
                  :label="model.name"
                  :value="model.id"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="温度 (Temperature)">
              <el-slider
                v-model="llmSettings.temperature"
                :min="0"
                :max="1"
                :step="0.1"
                show-input
              />
            </el-form-item>

            <el-form-item label="最大输出长度">
              <el-input-number
                v-model="llmSettings.max_tokens"
                :min="2048"
                :max="100000"
                :step="1024"
              />
            </el-form-item>
          </el-form>
        </div>

        <div class="step-actions">
          <el-button @click="prevStep">上一步</el-button>
          <el-button
            type="primary"
            @click="startExtraction"
            :loading="loading && !extractionTaskId"
            :disabled="!canExtract"
          >
            开始提取
          </el-button>
          <el-button
            v-if="extractionTaskId && loading"
            type="danger"
            @click="cancelExtraction"
          >
            取消任务
          </el-button>
        </div>
      </div>

      <!-- 步骤3：提取结果 -->
      <div v-if="activeStep === 2" class="step-content">
        <h2>提取结果</h2>

        <div v-if="extractionResult" class="result-section">
          <el-descriptions title="提取信息" :column="1" border>
            <el-descriptions-item label="Schema">
              {{ selectedSchema?.name }}
            </el-descriptions-item>
            <el-descriptions-item label="文件名" v-if="uploadType === 'file'">
              {{ extractionResult.file_name }}
            </el-descriptions-item>
            <el-descriptions-item label="提取时间">
              {{ formatDate(extractionResult.extraction_time) }}
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag
                :type="extractionResult.status === 'success' ? 'success' : 'danger'"
              >
                {{ extractionResult.status === "success" ? "成功" : "失败" }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>

          <!-- 分屏显示：左侧文档，右侧结果 - 新布局 -->
          <div class="document-result-container">
            <div class="document-panel">
              <div class="document-toolbar">
                <div class="toolbar-left">
                  <span class="toolbar-label">智能文档解析</span>
                  <span class="toolbar-info">私有化部署</span>
                  <span class="toolbar-info">使用规范</span>
                </div>
                <div class="toolbar-right">
                  <el-button size="small" type="text">
                    <el-icon><Share /></el-icon>
                    <span class="share-text">查看引导</span>
                  </el-button>
                </div>
              </div>

              <div class="document-viewer-container">
                <DocumentViewer
                  v-if="uploadType === 'file'"
                  :file="uploadFile"
                  class="document-viewer"
                />
                <div v-else-if="uploadType === 'text'" class="text-preview">
                  <pre>{{ inputText }}</pre>
                </div>
                <div v-else class="no-document">
                  <el-empty description="无文档可预览" />
                </div>
              </div>

              <div class="document-footer">
                <div class="page-controls">
                  <el-button-group>
                    <el-button size="small">
                      <el-icon><ArrowLeft /></el-icon>
                    </el-button>
                    <el-button size="small">
                      <el-icon><Search /></el-icon>
                    </el-button>
                    <el-button size="small">
                      <el-icon><ZoomOut /></el-icon>
                    </el-button>
                    <el-button size="small">
                      <el-icon><ZoomIn /></el-icon>
                    </el-button>
                    <el-button size="small">
                      <el-icon><ArrowRight /></el-icon>
                    </el-button>
                  </el-button-group>
                  <span class="page-info">1 / 1</span>
                </div>
              </div>
            </div>

            <div class="extraction-panel">
              <div class="extraction-header">
                <el-alert
                  type="info"
                  :closable="false"
                  show-icon
                >
                  <template #title>
                    利用智能文字识别技术，智能分析与抽取您需要的字段内容，提升使用效率。合同纯文本使用/所见即所得电子文档
                  </template>
                  <template #default>
                    <el-button size="small" type="primary" plain class="close-btn">关闭</el-button>
                  </template>
                </el-alert>
              </div>

              <div class="extraction-tabs">
                <el-tabs v-model="resultTab" class="extraction-result-tabs">
                  <el-tab-pane label="自动提取" name="data">
                    <div class="extraction-result-card">
                      <div class="extraction-fields">
                        <template v-if="extractionResult && extractionResult.data">
                          <div v-for="(field, fieldName) in extractionResult.data" :key="fieldName" class="extraction-field-item">
                            <div class="field-label">{{ formatFieldName(fieldName) }}</div>
                            <div class="field-value">
                              <template v-if="typeof field === 'object' && field !== null">
                                {{ formatObjectValue(field) }}
                              </template>
                              <template v-else>
                                {{ field }}
                              </template>
                            </div>
                          </div>
                        </template>
                        <div v-else class="no-extraction-data">
                          <el-empty description="暂无提取数据" />
                        </div>
                      </div>
                    </div>
                  </el-tab-pane>

                  <el-tab-pane label="配置1" name="config1">
                    <div class="config-content">配置1内容</div>
                  </el-tab-pane>

                  <el-tab-pane label="配置2" name="config2">
                    <div class="config-content">配置2内容</div>
                  </el-tab-pane>

                  <el-tab-pane label="配置3" name="config3">
                    <div class="config-content">配置3内容</div>
                  </el-tab-pane>
                </el-tabs>
              </div>

              <div class="extraction-actions">
                <div class="action-buttons">
                  <el-button size="small" @click="editExtraction">编辑数据</el-button>
                  <el-button size="small" type="primary" @click="saveExtraction">保存结果</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="no-result">
          <el-empty description="暂无提取结果" />
        </div>

        <div class="step-actions">
          <el-button @click="resetExtraction">重新提取</el-button>
          <el-button type="primary" @click="downloadResult" :disabled="!extractionResult">
            下载结果
          </el-button>
        </div>
      </div>
        </el-tab-pane>

        <el-tab-pane label="任务列表" name="tasks">
          <TaskList
            ref="taskListRef"
            @task-selected="handleTaskSelected"
            @task-deleted="handleTaskDeleted"
          />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { UploadFilled, Share, ArrowLeft, ArrowRight, Search, ZoomIn, ZoomOut } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { useSchemaStore } from "../stores/schema";
import { usePromptStore } from "../stores/prompt";
import { useExtractionStore } from "../stores/extraction";
import { systemApi } from "../services/api";
import DocumentViewer from "../components/DocumentViewer.vue";
import TaskList from "../components/TaskList.vue";

const schemaStore = useSchemaStore();
const promptStore = usePromptStore();
const extractionStore = useExtractionStore();

const activeTab = ref("new"); // 当前标签页：new, tasks
const activeStep = ref(0);
const selectedSchemaId = ref("");
const uploadType = ref("file");
const uploadFile = ref(null);
const inputText = ref("");
const selectedPromptId = ref("");
const loading = ref(false);
const extractionResult = ref(null);
const extractionTaskId = ref(null);
const pollingInterval = ref(null);
const resultTab = ref("data"); // 结果标签页：data, metadata, pages
const taskListRef = ref(null); // 任务列表组件引用

// 大模型设置
const llmSettings = ref({
  model: "gemini-2.0-flash",
  temperature: 0.3,
  max_tokens: 1000,
});

// 可用的大模型列表
const availableModels = ref([]);

// 从API获取大模型列表
const fetchAvailableModels = async () => {
  try {
    const response = await systemApi.getAvailableModels();
    if (response && Array.isArray(response)) {
      availableModels.value = response;

      // 如果有模型，默认选择第一个
      if (response.length > 0 && !llmSettings.value.model) {
        llmSettings.value.model = response[0].id;
      }
    } else {
      console.error('获取大模型列表失败: 返回格式不正确', response);
      ElMessage.warning('获取大模型列表失败，使用默认配置');
    }
  } catch (error) {
    console.error('获取大模型列表失败:', error);
    ElMessage.warning('获取大模型列表失败，使用默认配置');

    // 使用默认模型列表
    availableModels.value = [
      { id: "gemini-2.0-flash", name: "Gemini 2.0 Flash" },
      { id: "gemini-2.0-pro", name: "Gemini 2.0 Pro" },
      { id: "gpt-4o", name: "GPT-4o" },
      { id: "gpt-3.5-turbo", name: "GPT-3.5 Turbo" },
    ];
  }
};

// 获取数据
onMounted(async () => {
  try {
    // 并行获取数据
    await Promise.all([
      schemaStore.fetchSchemas(),
      promptStore.fetchPrompts(),
      fetchAvailableModels()
    ]);
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error('初始化数据失败，请刷新页面重试');
  }
});

// 计算属性
const schemas = computed(() => schemaStore.schemas);
const prompts = computed(() => promptStore.prompts);

const selectedSchema = computed(() => {
  if (!selectedSchemaId.value) return null;
  return schemas.value.find((schema) => schema.id === selectedSchemaId.value);
});

const filteredPrompts = computed(() => {
  if (!selectedSchemaId.value) return [];
  return prompts.value.filter((prompt) => prompt.schema_id === selectedSchemaId.value);
});

const selectedPrompt = computed(() => {
  if (!selectedPromptId.value) return null;
  return prompts.value.find((prompt) => prompt.id === selectedPromptId.value);
});

const canExtract = computed(() => {
  if (uploadType.value === "file") {
    return !!uploadFile.value;
  } else {
    return !!inputText.value && inputText.value.trim().length > 0;
  }
});

// 页面数据相关逻辑可以在需要时添加

// 监听Schema变化，重置提示词
watch(selectedSchemaId, () => {
  selectedPromptId.value = "";
});

// 方法
const nextStep = () => {
  activeStep.value++;
};

const prevStep = () => {
  activeStep.value--;
};

const handleFileChange = (file) => {
  uploadFile.value = file.raw;
};

const startExtraction = async () => {
  loading.value = true;

  try {
    let result;

    // 准备提取选项
    const options = {
      promptId: selectedPromptId.value,
      llm: {
        model: llmSettings.value.model,
        temperature: llmSettings.value.temperature,
        max_tokens: llmSettings.value.max_tokens,
      },
    };

    if (uploadType.value === "file") {
      result = await extractionStore.extractDocument(
        uploadFile.value,
        selectedSchemaId.value,
        options
      );
    } else {
      result = await extractionStore.extractFromText(
        inputText.value,
        selectedSchemaId.value,
        options
      );
    }

    // 处理异步任务
    if (result.task_id) {
      extractionTaskId.value = result.task_id;
      // 开始轮询任务状态
      startPollingTaskStatus(result.task_id);
      ElMessage.info(`提取任务 ${result.task_id} 已开始，正在处理中...`);
    } else {
      // 同步返回结果
      extractionResult.value = result;
      nextStep();
      loading.value = false;
    }
  } catch (error) {
    console.error("提取失败:", error);
    ElMessage.error("提取失败: " + (error.message || "未知错误"));
    loading.value = false;
  }
};

// 取消提取任务
const cancelExtraction = async () => {
  if (!extractionTaskId.value) return;

  try {
    // 调用取消任务API
    await extractionStore.cancelExtractionTask(extractionTaskId.value);

    // 显示取消成功消息
    ElMessage.success("任务已取消");

    // 清除轮询
    if (pollingInterval.value) {
      clearInterval(pollingInterval.value);
      pollingInterval.value = null;
    }

    // 重置状态
    loading.value = false;
    extractionTaskId.value = null;
  } catch (error) {
    console.error("取消任务失败:", error);
    ElMessage.error("取消任务失败: " + (error.message || "未知错误"));
  }
};

// 开始轮询任务状态
const startPollingTaskStatus = (taskId) => {
  // 清除之前的轮询
  if (pollingInterval.value) {
    clearInterval(pollingInterval.value);
  }

  // 设置轮询间隔（每2秒查询一次）
  pollingInterval.value = setInterval(async () => {
    try {
      const status = await extractionStore.checkExtractionStatus(taskId);

      // 如果任务完成、失败或取消，停止轮询
      if (
        status.status === "success" ||
        status.status === "failed" ||
        status.status === "cancelled"
      ) {
        clearInterval(pollingInterval.value);
        pollingInterval.value = null;

        if (status.status === "success") {
          extractionResult.value = status;
          nextStep();
        } else if (status.status === "cancelled") {
          ElMessage.info("任务已取消");
        }

        loading.value = false;
        extractionTaskId.value = null;
      }
    } catch (error) {
      console.error("获取任务状态失败:", error);
      clearInterval(pollingInterval.value);
      pollingInterval.value = null;
      loading.value = false;
      extractionTaskId.value = null;
    }
  }, 2000);
};

const resetExtraction = async () => {
  // 如果有正在进行的任务，先取消
  if (extractionTaskId.value && loading.value) {
    try {
      await extractionStore.cancelExtractionTask(extractionTaskId.value);
      ElMessage.info("已取消正在进行的任务");
    } catch (error) {
      console.error("取消任务失败:", error);
    }
  }

  // 清除轮询
  if (pollingInterval.value) {
    clearInterval(pollingInterval.value);
    pollingInterval.value = null;
  }

  // 重置所有状态
  activeStep.value = 0;
  selectedSchemaId.value = "";
  uploadType.value = "file";
  uploadFile.value = null;
  inputText.value = "";
  selectedPromptId.value = "";
  extractionResult.value = null;
  extractionTaskId.value = null;
  loading.value = false;

  // 重置大模型设置为默认值
  llmSettings.value = {
    model: "gemini-2.0-flash",
    temperature: 0.3,
    max_tokens: 1000,
  };
};

const downloadResult = () => {
  if (!extractionResult.value) return;

  // 使用中文编码确保中文字符正确显示
  const data = JSON.stringify(extractionResult.value, null, 2);
  // 使用UTF-8编码
  const blob = new Blob([data], { type: "application/json;charset=utf-8" });
  const url = URL.createObjectURL(blob);

  // 创建文件名（使用schema名称和时间戳）
  const schemaName = selectedSchema.value?.name || 'extraction';
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
  const fileName = `${schemaName}_提取结果_${timestamp}.json`;

  const a = document.createElement("a");
  a.href = url;
  a.download = fileName;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

// 任务列表相关方法
const handleTaskSelected = (task) => {
  // 如果任务有结果，显示结果
  if (task.result) {
    extractionResult.value = task.result;

    // 设置相关状态
    if (task.schemaId) {
      selectedSchemaId.value = task.schemaId;
    }

    if (task.textContent) {
      uploadType.value = 'text';
      inputText.value = task.textContent;
    } else {
      uploadType.value = 'file';
    }

    // 切换到提取结果步骤
    activeTab.value = 'new';
    activeStep.value = 2;
  } else {
    // 如果任务没有结果，尝试获取结果
    ElMessage.info('正在获取任务结果...');
    fetchTaskResult(task.id);
  }
};

const handleTaskDeleted = () => {
  // 刷新任务列表
  if (taskListRef.value) {
    taskListRef.value.refreshTasks();
  }
};

const fetchTaskResult = async (taskId) => {
  loading.value = true;

  try {
    const result = await extractionStore.getTaskResult(taskId);

    if (result.success) {
      extractionResult.value = result.result;

      // 切换到提取结果步骤
      activeTab.value = 'new';
      activeStep.value = 2;
    } else {
      ElMessage.error('获取任务结果失败: ' + (result.error || '未知错误'));
    }
  } catch (error) {
    console.error('获取任务结果失败:', error);
    ElMessage.error('获取任务结果失败: ' + (error.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

const formatFieldType = (type) => {
  const typeMap = {
    string: "字符串",
    number: "数字",
    integer: "整数",
    boolean: "布尔值",
    array: "数组",
    object: "对象",
  };

  return typeMap[type] || type;
};

const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleString();
};

// 这些方法可以在需要时重新添加

// 格式化字段名称
const formatFieldName = (fieldName) => {
  // 将下划线或驼峰命名转换为更友好的显示名称
  return fieldName
    .replace(/_/g, ' ')
    .replace(/([A-Z])/g, ' $1')
    .replace(/^\w/, c => c.toUpperCase());
};

// 格式化对象值
const formatObjectValue = (obj) => {
  if (Array.isArray(obj)) {
    return obj.join(', ');
  } else {
    return JSON.stringify(obj);
  }
};

// 编辑提取数据
const editExtraction = () => {
  ElMessage.info('编辑功能开发中');
};

// 保存提取结果
const saveExtraction = () => {
  ElMessage.success('提取结果已保存');
};
</script>

<style scoped>
.extraction-container {
  width: 100%;
  margin: 0 auto;
  padding: 10px;
}

h1 {
  font-size: 28px;
  color: #303133;
  margin-bottom: 20px;
}

.extraction-card {
  margin-bottom: 20px;
  height: calc(100vh - 120px); /* 使用视口高度减去头部和页面边距 */
  display: flex;
  flex-direction: column;
}

.extraction-card :deep(.el-card__body) {
  height: 100%;
  padding: 15px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.step-content {
  margin-top: 30px;
}

.step-content h2 {
  font-size: 22px;
  color: #303133;
  margin-bottom: 16px;
}

.step-content p {
  color: #606266;
  margin-bottom: 20px;
}

.schema-details,
.prompt-details {
  margin-top: 20px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.schema-details h3,
.prompt-details h4 {
  font-size: 18px;
  color: #303133;
  margin-bottom: 12px;
}

.schema-details p,
.prompt-details p {
  color: #606266;
  margin-bottom: 16px;
}

.schema-details h4 {
  font-size: 16px;
  color: #303133;
  margin: 16px 0 12px;
}

.upload-area {
  width: 100%;
  margin: 20px 0;
}

.upload-area :deep(.el-upload-dragger) {
  width: 100%;
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.upload-area :deep(.el-icon--upload) {
  font-size: 48px;
  margin-bottom: 16px;
}

.upload-area :deep(.el-upload__text) {
  font-size: 16px;
}

.text-input {
  margin: 20px 0;
}

.text-input :deep(.el-textarea__inner) {
  font-family: 'Courier New', Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
}

.prompt-section,
.llm-section {
  margin-top: 30px;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.prompt-section h3,
.llm-section h3 {
  font-size: 18px;
  color: #303133;
  margin-bottom: 16px;
}

.step-actions {
  margin-top: 30px;
  display: flex;
  justify-content: flex-end;
}

.result-section {
  margin-top: 20px;
}

.result-section h3 {
  font-size: 18px;
  color: #303133;
  margin: 24px 0 16px;
}

.data-card {
  margin-bottom: 20px;
  background-color: #f8f8f8;
}

.data-card pre {
  margin: 0;
  padding: 8px;
  white-space: pre-wrap;
  word-break: break-word;
  font-family: "Courier New", Courier, monospace;
}

.json-viewer {
  padding: 10px;
}

.json-field {
  margin-bottom: 15px;
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
}

.json-field:last-child {
  border-bottom: none;
}

.json-field-name {
  font-weight: bold;
  margin-bottom: 5px;
  color: #409EFF;
}

.json-field-value {
  padding-left: 15px;
}

.json-actions {
  display: flex;
  justify-content: flex-end;
  padding: 10px;
  border-top: 1px solid #eee;
}

.metadata-viewer {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.metadata-raw {
  margin-top: 20px;
}

.metadata-viewer :deep(.el-descriptions__label) {
  width: 120px;
  font-weight: bold;
}

.no-result {
  margin: 40px 0;
}

.split-view-container {
  margin-top: 20px;
  height: calc(100vh - 300px); /* 使用视口高度计算，减去其他元素的高度 */
  min-height: 600px; /* 设置最小高度 */
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.document-view-container,
.extraction-results-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 10px;
  overflow: hidden;
}

.document-view-container h3,
.extraction-results-container h3 {
  margin-top: 0;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.document-view-container > div:not(h3) {
  flex: 1;
  overflow: auto;
}

.text-preview {
  background-color: white;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  height: 100%;
  overflow: auto;
}

.text-preview pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  font-family: 'Courier New', Courier, monospace;
}

.no-document {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.extraction-results-container .el-tabs {
  height: calc(100% - 40px);
  display: flex;
  flex-direction: column;
}

.extraction-results-container .el-tabs__content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.full-height-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.full-height-tabs .el-tabs__content {
  flex: 1;
  overflow: hidden;
}

.tab-content-scroll {
  height: 100%;
  overflow: auto;
  padding: 10px;
}

.pages-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.page-item h4 {
  margin-top: 0;
  margin-bottom: 10px;
}

.page-card {
  margin-bottom: 10px;
}

.page-card pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  font-family: 'Courier New', Courier, monospace;
}

/* 任务列表样式 */
.extraction-tabs {
  height: 100%;
}

.extraction-tabs :deep(.el-tabs__content) {
  height: calc(100% - 40px);
  overflow: hidden;
}

.extraction-tabs :deep(.el-tab-pane) {
  height: 100%;
  overflow: auto;
}

/* 新布局样式 */
.document-result-container {
  display: flex;
  width: 100%;
  height: calc(100vh - 200px);
  min-height: 600px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
  margin-top: 20px;
}

.document-panel {
  flex: 3;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #ebeef5;
  background-color: #f5f7fa;
}

.extraction-panel {
  flex: 2;
  display: flex;
  flex-direction: column;
  background-color: white;
}

.document-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #ebeef5;
  background-color: #fff;
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.toolbar-label {
  font-weight: bold;
  margin-right: 20px;
}

.toolbar-info {
  color: #606266;
  font-size: 12px;
  margin-right: 15px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.share-text {
  margin-left: 5px;
  color: #409EFF;
}

.document-viewer-container {
  flex: 1;
  overflow: auto;
  padding: 0;
  background-color: white;
}

.document-viewer {
  width: 100%;
  height: 100%;
}

.document-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  border-top: 1px solid #ebeef5;
  background-color: #fff;
}

.page-controls {
  display: flex;
  align-items: center;
}

.page-info {
  margin-left: 15px;
  color: #606266;
}

.extraction-header {
  padding: 10px;
}

.extraction-header :deep(.el-alert) {
  margin: 0;
}

.extraction-header :deep(.el-alert__title) {
  font-size: 12px;
}

.close-btn {
  margin-top: 5px;
}

.extraction-tabs {
  flex: 1;
  overflow: hidden;
  padding: 0 10px;
}

.extraction-result-tabs {
  height: 100%;
}

.extraction-result-card {
  padding: 15px;
  height: 100%;
  overflow: auto;
}

.extraction-fields {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.extraction-field-item {
  display: flex;
  flex-direction: column;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.field-label {
  font-weight: bold;
  color: #606266;
  margin-bottom: 5px;
}

.field-value {
  color: #303133;
}

.extraction-actions {
  padding: 10px;
  border-top: 1px solid #ebeef5;
  background-color: #f5f7fa;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.config-content {
  padding: 20px;
  height: 100%;
}
</style>
