<template>
  <div class="schema-management-container">
    <div class="page-header">
      <h1>Schema管理</h1>
      <el-button type="primary" @click="createSchema">
        <el-icon><Plus /></el-icon> 创建Schema
      </el-button>
    </div>

    <el-card v-if="loading" class="loading-card">
      <el-skeleton :rows="6" animated />
    </el-card>

    <el-card v-else-if="error" class="error-card">
      <el-result
        icon="error"
        title="加载失败"
        :sub-title="error"
      >
        <template #extra>
          <el-button type="primary" @click="fetchSchemas">重试</el-button>
        </template>
      </el-result>
    </el-card>

    <template v-else>
      <el-input
        v-model="searchQuery"
        placeholder="搜索Schema"
        prefix-icon="Search"
        clearable
        class="search-input"
      />

      <el-empty v-if="filteredSchemas.length === 0" description="暂无Schema" />

      <div v-else class="schema-grid">
        <el-card
          v-for="schema in filteredSchemas"
          :key="schema.id"
          class="schema-card"
        >
          <template #header>
            <div class="card-header">
              <router-link :to="`/schemas/${schema.id}`" class="schema-link">
                <span>{{ schema.name }}</span>
              </router-link>
              <el-tag size="small">v{{ schema.version }}</el-tag>
            </div>
          </template>
          <div class="card-content">
            <router-link :to="`/schemas/${schema.id}`" class="schema-link">
              <p class="description">{{ schema.description }}</p>
              <div class="field-count">
                <el-icon><Document /></el-icon>
                <span>{{ schema.fields.length }} 个字段</span>
              </div>
              <div class="schema-metadata" v-if="schema.type">
                <el-tag size="small" type="info">{{ schema.type }}</el-tag>
              </div>
              <div class="schema-date" v-if="schema.updated_at">
                <small>更新于: {{ formatDate(schema.updated_at) }}</small>
              </div>
            </router-link>
          </div>
          <div class="card-actions">
            <el-button
              type="danger"
              size="small"
              @click.stop="confirmDelete(schema)"
            >
              删除
            </el-button>
          </div>
        </el-card>
      </div>
    </template>

    <!-- 创建/编辑Schema对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEditing ? '编辑Schema' : '创建Schema'"
      width="80%"
      destroy-on-close
    >
      <el-form
        ref="schemaForm"
        :model="schemaForm"
        :rules="schemaRules"
        label-width="120px"
      >
        <el-form-item label="ID" prop="id">
          <el-input v-model="schemaForm.id" :disabled="isEditing" />
        </el-form-item>

        <el-form-item label="名称" prop="name">
          <el-input v-model="schemaForm.name" />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="schemaForm.description"
            type="textarea"
            :rows="3"
          />
        </el-form-item>

        <el-form-item label="版本" prop="version">
          <el-input v-model="schemaForm.version" />
        </el-form-item>

        <el-divider>字段定义</el-divider>

        <div v-for="(field, index) in schemaForm.fields" :key="index" class="field-item">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item
                :label="index === 0 ? '字段代码' : ''"
                :prop="`fields.${index}.code`"
                :rules="{ required: true, message: '请输入字段代码', trigger: 'blur' }"
              >
                <el-input v-model="field.code" placeholder="字段代码" />
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item
                :label="index === 0 ? '字段名称' : ''"
                :prop="`fields.${index}.name`"
                :rules="{ required: true, message: '请输入字段名称', trigger: 'blur' }"
              >
                <el-input v-model="field.name" placeholder="字段名称" />
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-form-item
                :label="index === 0 ? '类型' : ''"
                :prop="`fields.${index}.type`"
                :rules="{ required: true, message: '请选择字段类型', trigger: 'change' }"
              >
                <el-select v-model="field.type" placeholder="类型">
                  <el-option label="字符串" value="string" />
                  <el-option label="数字" value="number" />
                  <el-option label="整数" value="integer" />
                  <el-option label="布尔值" value="boolean" />
                  <el-option label="数组" value="array" />
                  <el-option label="对象" value="object" />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-form-item :label="index === 0 ? '必填' : ''">
                <el-switch v-model="field.required" />
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-button
                type="danger"
                icon="Delete"
                circle
                @click="removeField(index)"
                :disabled="schemaForm.fields.length <= 1"
              />
            </el-col>
          </el-row>

          <el-form-item
            :label="index === 0 ? '字段描述' : ''"
            :prop="`fields.${index}.description`"
          >
            <el-input
              v-model="field.description"
              placeholder="字段描述"
              type="textarea"
              :rows="2"
            />
          </el-form-item>

          <el-divider v-if="index < schemaForm.fields.length - 1" />
        </div>

        <div class="add-field-button">
          <el-button type="primary" @click="addField" plain>
            <el-icon><Plus /></el-icon> 添加字段
          </el-button>
        </div>

        <el-divider>提取配置</el-divider>

        <el-form-item label="提取方法">
          <el-checkbox-group v-model="schemaForm.extraction.methods">
            <el-checkbox label="llm">大语言模型</el-checkbox>
            <el-checkbox label="regex">正则表达式</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item
          label="LLM提示词"
          v-if="schemaForm.extraction.methods.includes('llm')"
        >
          <el-input
            v-model="schemaForm.extraction.prompts.llm"
            type="textarea"
            :rows="5"
            placeholder="请输入提示词模板"
          />
        </el-form-item>

        <template v-if="schemaForm.extraction.methods.includes('regex')">
          <el-divider>正则表达式模式</el-divider>

          <div
            v-for="field in schemaForm.fields"
            :key="`regex-${field.code}`"
            class="regex-item"
          >
            <el-form-item :label="field.name">
              <el-input
                v-model="schemaForm.extraction.patterns[field.code]"
                placeholder="正则表达式模式"
              />
            </el-form-item>
          </div>
        </template>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveSchema" :loading="saving">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog
      v-model="deleteDialogVisible"
      title="确认删除"
      width="30%"
    >
      <p>确定要删除Schema "{{ schemaToDelete?.name }}" 吗？此操作不可恢复。</p>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteDialogVisible = false">取消</el-button>
          <el-button
            type="danger"
            @click="deleteSchema"
            :loading="deleting"
          >
            删除
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Plus, Document, Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useSchemaStore } from '../stores/schema'

const router = useRouter()
const schemaStore = useSchemaStore()

// 状态
const loading = ref(false)
const error = ref(null)
const searchQuery = ref('')
const dialogVisible = ref(false)
const isEditing = ref(false)
const saving = ref(false)
const deleteDialogVisible = ref(false)
const schemaToDelete = ref(null)
const deleting = ref(false)

// 表单
const schemaForm = ref({
  id: '',
  name: '',
  description: '',
  version: '1.0.0',
  fields: [
    {
      code: '',
      name: '',
      type: 'string',
      description: '',
      required: false
    }
  ],
  groups: [],
  extraction: {
    methods: ['llm'],
    prompts: {
      llm: ''
    },
    patterns: {}
  }
})

const schemaRules = {
  id: [
    { required: true, message: '请输入Schema ID', trigger: 'blur' },
    { pattern: /^[a-z0-9_]+$/, message: 'ID只能包含小写字母、数字和下划线', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入Schema名称', trigger: 'blur' }
  ],
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' },
    { pattern: /^\d+\.\d+\.\d+$/, message: '版本号格式应为x.y.z', trigger: 'blur' }
  ]
}

// 计算属性
const filteredSchemas = computed(() => {
  if (!searchQuery.value) {
    return schemaStore.schemas
  }

  const query = searchQuery.value.toLowerCase()
  return schemaStore.schemas.filter(schema =>
    schema.name.toLowerCase().includes(query) ||
    schema.description.toLowerCase().includes(query)
  )
})

// 生命周期钩子
onMounted(fetchSchemas)

// 方法
async function fetchSchemas() {
  loading.value = true
  error.value = null

  try {
    await schemaStore.fetchSchemas()
  } catch (err) {
    error.value = err.message || '加载Schema失败'
  } finally {
    loading.value = false
  }
}

function viewSchema(id) {
  router.push(`/schemas/${id}`)
}

function createSchema() {
  isEditing.value = false
  schemaForm.value = {
    id: '',
    name: '',
    description: '',
    version: '1.0.0',
    fields: [
      {
        code: '',
        name: '',
        type: 'string',
        description: '',
        required: false
      }
    ],
    groups: [],
    extraction: {
      methods: ['llm'],
      prompts: {
        llm: ''
      },
      patterns: {}
    }
  }
  dialogVisible.value = true
}

function addField() {
  schemaForm.value.fields.push({
    code: '',
    name: '',
    type: 'string',
    description: '',
    required: false
  })
}

function removeField(index) {
  if (schemaForm.value.fields.length > 1) {
    schemaForm.value.fields.splice(index, 1)
  }
}

async function saveSchema() {
  saving.value = true

  try {
    if (isEditing.value) {
      await schemaStore.updateSchema(schemaForm.value.id, schemaForm.value)
      ElMessage.success('Schema更新成功')
    } else {
      await schemaStore.createSchema(schemaForm.value)
      ElMessage.success('Schema创建成功')
    }

    dialogVisible.value = false
    await fetchSchemas()
  } catch (err) {
    ElMessage.error(err.message || '保存Schema失败')
  } finally {
    saving.value = false
  }
}

function confirmDelete(schema) {
  schemaToDelete.value = schema
  deleteDialogVisible.value = true
}

async function deleteSchema() {
  if (!schemaToDelete.value) return

  deleting.value = true

  try {
    await schemaStore.deleteSchema(schemaToDelete.value.id)
    ElMessage.success('Schema删除成功')
    deleteDialogVisible.value = false
    schemaToDelete.value = null
  } catch (err) {
    ElMessage.error(err.message || '删除Schema失败')
  } finally {
    deleting.value = false
  }
}

function formatDate(dateString) {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  return date.toLocaleString()
}
</script>

<style scoped>
.schema-management-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  font-size: 28px;
  color: #303133;
  margin: 0;
}

.loading-card,
.error-card {
  margin-bottom: 20px;
}

.search-input {
  margin-bottom: 20px;
}

.schema-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.schema-card {
  cursor: pointer;
  transition: all 0.3s;
}

.schema-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-weight: bold;
  font-size: 16px;
}

.card-content {
  min-height: 100px;
}

.description {
  color: #606266;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.field-count {
  display: flex;
  align-items: center;
  color: #909399;
}

.field-count .el-icon {
  margin-right: 8px;
}

.card-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

.field-item {
  margin-bottom: 16px;
}

.add-field-button {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

.regex-item {
  margin-bottom: 16px;
}

.schema-link {
  text-decoration: none;
  color: inherit;
  display: block;
}

.schema-link:hover {
  color: #409EFF;
}
</style>
