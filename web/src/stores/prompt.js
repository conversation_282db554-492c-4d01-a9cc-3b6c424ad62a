import { defineStore } from 'pinia'
import { promptApi } from '../services/api'

export const usePromptStore = defineStore('prompt', {
  state: () => ({
    prompts: [],
    currentPrompt: null,
    loading: false,
    error: null
  }),

  actions: {
    async fetchPrompts() {
      this.loading = true
      this.error = null

      try {
        // 调用API获取提示词列表
        const response = await promptApi.getPrompts()

        if (response && response.items) {
          this.prompts = response.items
        } else {
          // 如果API返回为空，使用默认提示词
          this.prompts = [
            {
              id: 'test_prompt',
              name: '测试提示词',
              description: '用于测试API的提示词',
              content: '请从以下文本中提取信息：\n\n{text}\n\n根据以下schema提取：\n\n{schema}\n\n请以JSON格式返回结果。',
              schema_id: 'test_schema',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }
          ]
        }
      } catch (error) {
        this.error = error.message || '获取提示词列表失败'
      } finally {
        this.loading = false
      }
    },

    async fetchPrompt(id) {
      this.loading = true
      this.error = null
      console.log('开始获取提示词详情，ID:', id)

      try {
        // 首先尝试从已加载的提示词列表中查找
        if (this.prompts.length === 0) {
          console.log('提示词列表为空，开始加载列表')
          await this.fetchPrompts()
          console.log('提示词列表加载完成，数量:', this.prompts.length)
        }

        const foundPrompt = this.prompts.find(p => p.id === id)
        if (foundPrompt) {
          console.log('在列表中找到提示词:', foundPrompt)
          this.currentPrompt = foundPrompt
          return
        } else {
          console.log('在列表中未找到提示词，列表中的ID:', this.prompts.map(p => p.id))
        }

        // 如果列表中没有找到，尝试调用API获取提示词详情
        try {
          console.log('开始调用API获取提示词详情')
          const response = await promptApi.getPrompt(id)
          console.log('API返回提示词详情:', response)
          if (response) {
            this.currentPrompt = response
            console.log('设置当前提示词:', this.currentPrompt)
          }
        } catch (apiError) {
          console.warn('API获取提示词详情失败:', apiError)

          // 如果API返回为空或出错，使用默认提示词
          if (id === 'extraction/extract') {
            this.currentPrompt = {
              id: 'extraction/extract',
              name: '通用提取提示词',
              description: '用于从文本中提取结构化信息的通用提示词',
              content: '你是一个提取结构化信息的机器人，你的目标是匹配输入与输出中每个属性描述相符合的信息，最终按JSON格式输出结构化提取信息。\n在提取信息时，不要添加任何没有出现在输出中显示的属性（字段的类型type无须输出）。在属性别名的定义中，匹配输入与属性别名，相符合的信息都归入属性名称的提取结果里面。除了提取的信息外，不要输出任何内容，不要添加任何澄清信息，不要添加任何不在模式中的字段。如果文本包含架构中未出现的属性，请忽略它们。\n输出:\\n{{data_schema}}\n输入：\\n```{{input_text}}```\n补充提示信息（不属于提取目标）```{{supplement_knowledge}}```\\\\n\n属性别名：\\n{{property_alias}}\\n\\n \n\n请确保输出的JSON没有SyntaxError，输出的 JSON 文本起始和结束位置，不要带 Markdown 标记。',
              schema_id: '',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }
          } else {
            this.currentPrompt = {
              id: id,
              name: '测试提示词',
              description: '用于测试API的提示词',
              content: '请从以下文本中提取信息：\n\n{text}\n\n根据以下schema提取：\n\n{schema}\n\n请以JSON格式返回结果。',
              schema_id: '',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }
          }
        }
      } catch (error) {
        this.error = error.message || '获取提示词详情失败'
      } finally {
        this.loading = false
      }
    },

    async createPrompt(data) {
      this.loading = true
      this.error = null

      try {
        // 调用API创建提示词
        const response = await promptApi.createPrompt(data)
        return response
      } catch (error) {
        this.error = error.message || '创建提示词失败'
        throw error
      } finally {
        this.loading = false
      }
    },

    async updatePrompt(id, data) {
      this.loading = true
      this.error = null

      try {
        // 调用API更新提示词
        const response = await promptApi.updatePrompt(id, data)
        return response
      } catch (error) {
        this.error = error.message || '更新提示词失败'
        throw error
      } finally {
        this.loading = false
      }
    },

    async deletePrompt(id) {
      this.loading = true
      this.error = null

      try {
        // 调用API删除提示词
        await promptApi.deletePrompt(id)

        // 从本地列表中移除
        this.prompts = this.prompts.filter(prompt => prompt.id !== id)
        return { success: true }
      } catch (error) {
        this.error = error.message || '删除提示词失败'
        throw error
      } finally {
        this.loading = false
      }
    }
  }
})
