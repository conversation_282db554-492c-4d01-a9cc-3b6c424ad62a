import { defineStore } from 'pinia'
import { schemaApi } from '../services/api'

export const useSchemaStore = defineStore('schema', {
  state: () => ({
    schemas: [],
    currentSchema: null,
    loading: false,
    error: null
  }),

  actions: {
    async fetchSchemas() {
      this.loading = true
      this.error = null

      try {
        // 调用API获取Schema列表
        const response = await schemaApi.getSchemas()

        // 处理API返回的数据
        if (response && response.items) {
          // 将API返回的字段格式转换为前端使用的格式
          this.schemas = response.items.map(schema => ({
            id: schema.id,
            name: schema.name,
            description: schema.description || '',
            version: schema.version || '1.0.0',
            type: schema.metadata?.type || '',
            created_at: schema.created_at,
            updated_at: schema.updated_at,
            fields: schema.fields.map(field => ({
              code: field.name, // 使用字段名称作为代码
              name: field.description || field.name, // 使用描述作为显示名称，如果没有则使用字段名称
              type: field.type,
              required: field.required || false,
              description: field.description || '',
              extraction_method: field.extraction_method || 'llm',
              regex_pattern: field.regex_pattern || '',
              regex_group: field.regex_group || 1
            }))
          }))
        } else {
          this.schemas = []
        }
      } catch (error) {
        this.error = error.message || '获取Schema列表失败'
        console.error('获取Schema列表失败:', error)
      } finally {
        this.loading = false
      }
    },

    async fetchSchema(id) {
      this.loading = true
      this.error = null

      try {
        // 调用API获取Schema详情
        const response = await schemaApi.getSchema(id)

        // 处理API返回的数据
        if (response) {
          // 将API返回的字段格式转换为前端使用的格式
          const schema = response

          // 提取方法
          const extractionMethods = []
          const patterns = {}
          const prompts = { llm: '' }

          // 处理字段
          const fields = schema.fields.map(field => {
            // 如果字段有正则表达式，添加到patterns中
            if (field.regex_pattern) {
              patterns[field.name] = field.regex_pattern
              if (!extractionMethods.includes('regex')) {
                extractionMethods.push('regex')
              }
            }

            // 如果字段使用LLM提取，添加到extractionMethods中
            if (field.extraction_method === 'llm' && !extractionMethods.includes('llm')) {
              extractionMethods.push('llm')
            }

            return {
              code: field.name, // 使用字段名称作为代码
              name: field.description || field.name, // 使用描述作为显示名称，如果没有则使用字段名称
              type: field.type,
              required: field.required || false,
              description: field.description || '',
              extraction_method: field.extraction_method || 'llm',
              regex_pattern: field.regex_pattern || '',
              regex_group: field.regex_group || 1
            }
          })

          // 如果metadata中有prompt_template，使用它
          if (schema.metadata && schema.metadata.prompt_template) {
            prompts.llm = schema.metadata.prompt_template
          }

          // 构建分组信息（如果metadata中有groups）
          let groups = []
          if (schema.metadata && schema.metadata.groups) {
            groups = schema.metadata.groups
          } else {
            // 默认分组：所有字段放在一个组中
            groups = [
              {
                name: '基本信息',
                fields: fields.map(field => field.code)
              }
            ]
          }

          this.currentSchema = {
            id: schema.id,
            name: schema.name,
            description: schema.description || '',
            version: schema.version || '1.0.0',
            type: schema.metadata?.type || '',
            created_at: schema.created_at,
            updated_at: schema.updated_at,
            fields: fields,
            groups: groups,
            extraction: {
              methods: extractionMethods,
              prompts: prompts,
              patterns: patterns
            }
          }
        } else {
          this.error = '未找到Schema'
        }
      } catch (error) {
        this.error = error.message || '获取Schema详情失败'
        console.error('获取Schema详情失败:', error)
      } finally {
        this.loading = false
      }
    },

    async createSchema(data) {
      this.loading = true
      this.error = null

      try {
        // 将前端格式转换为API格式
        const apiData = this._convertToApiFormat(data)

        // 调用API创建Schema
        const response = await schemaApi.createSchema(apiData)
        return response
      } catch (error) {
        this.error = error.message || '创建Schema失败'
        console.error('创建Schema失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    async updateSchema(id, data) {
      this.loading = true
      this.error = null

      try {
        // 将前端格式转换为API格式
        const apiData = this._convertToApiFormat(data)

        // 调用API更新Schema
        const response = await schemaApi.updateSchema(id, apiData)
        return response
      } catch (error) {
        this.error = error.message || '更新Schema失败'
        console.error('更新Schema失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // 辅助方法：将前端格式转换为API格式
    _convertToApiFormat(data) {
      // 创建基本结构
      const apiData = {
        id: data.id,
        name: data.name,
        description: data.description,
        version: data.version,
        fields: [],
        metadata: {
          ...data.metadata,
          type: data.type,
          groups: data.groups,
          prompt_template: data.extraction?.prompts?.llm
        }
      }

      // 转换字段
      if (data.fields && data.fields.length > 0) {
        apiData.fields = data.fields.map(field => {
          const apiField = {
            name: field.code,
            type: field.type,
            description: field.name,
            required: field.required,
            extraction_method: field.extraction_method || 'llm'
          }

          // 如果有正则表达式，添加到字段中
          if (data.extraction?.patterns && data.extraction.patterns[field.code]) {
            apiField.regex_pattern = data.extraction.patterns[field.code]
            apiField.regex_group = field.regex_group || 1
          }

          return apiField
        })
      }

      return apiData
    },

    async deleteSchema(id) {
      this.loading = true
      this.error = null

      try {
        // 调用API删除Schema
        await schemaApi.deleteSchema(id)

        // 从本地列表中移除
        this.schemas = this.schemas.filter(schema => schema.id !== id)

        return { success: true }
      } catch (error) {
        this.error = error.message || '删除Schema失败'
        console.error('删除Schema失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    }
  }
})
