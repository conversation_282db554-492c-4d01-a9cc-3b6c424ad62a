import { defineStore } from 'pinia'
import { extractionApi } from '../services/api'

export const useExtractionStore = defineStore('extraction', {
  state: () => ({
    extractionHistory: [],
    currentExtraction: null,
    loading: false,
    error: null,
    taskList: [], // 任务列表
    taskListLoaded: false // 是否已加载任务列表
  }),

  actions: {
    async extractDocument(file, schemaId, options = {}) {
      this.loading = true
      this.error = null

      try {
        // 调用API进行文档提取
        const response = await extractionApi.extractDocument(file, schemaId, options)

        // 如果是异步任务，返回任务ID
        if (response.task_id) {
          const taskId = response.task_id

          // 添加到任务列表
          this.addTask({
            id: taskId,
            schemaId: schemaId,
            schemaName: options.schemaName || schemaId,
            fileName: file.name,
            fileSize: file.size,
            fileType: file.type,
            createdAt: new Date().toISOString(),
            status: 'processing'
          })

          // 返回任务ID，前端可以使用这个ID查询任务状态
          return {
            task_id: taskId,
            status: 'processing'
          }
        }

        // 如果是同步返回结果
        this.currentExtraction = response

        // 添加到任务列表
        this.addTask({
          id: response.extraction_id || `task-${Date.now()}`,
          schemaId: schemaId,
          schemaName: options.schemaName || schemaId,
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type,
          createdAt: new Date().toISOString(),
          status: 'success',
          result: response
        })

        return response
      } catch (error) {
        this.error = error.message || '文档提取失败'
        throw error
      } finally {
        this.loading = false
      }
    },

    async extractFromText(text, schemaId, options = {}) {
      this.loading = true
      this.error = null

      try {
        // 调用API进行文本提取
        const response = await extractionApi.extractFromText(text, schemaId, options)

        // 如果是异步任务，返回任务ID
        if (response.task_id) {
          const taskId = response.task_id

          // 添加到任务列表
          this.addTask({
            id: taskId,
            schemaId: schemaId,
            schemaName: options.schemaName || schemaId,
            fileName: '文本提取',
            createdAt: new Date().toISOString(),
            status: 'processing',
            textContent: text.substring(0, 100) + (text.length > 100 ? '...' : '')
          })

          // 返回任务ID，前端可以使用这个ID查询任务状态
          return {
            task_id: taskId,
            status: 'processing'
          }
        }

        // 如果是同步返回结果
        this.currentExtraction = response

        // 添加到任务列表
        this.addTask({
          id: response.extraction_id || `task-${Date.now()}`,
          schemaId: schemaId,
          schemaName: options.schemaName || schemaId,
          fileName: '文本提取',
          createdAt: new Date().toISOString(),
          status: 'success',
          result: response,
          textContent: text.substring(0, 100) + (text.length > 100 ? '...' : '')
        })

        return response
      } catch (error) {
        this.error = error.message || '文本提取失败'
        throw error
      } finally {
        this.loading = false
      }
    },

    async fetchExtractionHistory() {
      this.loading = true
      this.error = null

      try {
        // 调用API获取提取历史
        const response = await extractionApi.getExtractionHistory()
        this.extractionHistory = response
      } catch (error) {
        this.error = error.message || '获取提取历史失败'
      } finally {
        this.loading = false
      }
    },

    async fetchExtractionResult(id) {
      this.loading = true
      this.error = null

      try {
        // 调用API获取提取结果
        const response = await extractionApi.getExtractionResult(id)
        this.currentExtraction = response
      } catch (error) {
        this.error = error.message || '获取提取结果失败'
      } finally {
        this.loading = false
      }
    },

    async checkExtractionStatus(taskId) {
      try {
        // 调用API获取提取任务状态
        const response = await extractionApi.getExtractionStatus(taskId)

        // 如果任务已完成，获取提取结果
        if (response.status === 'success') {
          this.currentExtraction = response.result

          // 更新任务状态
          this.updateTaskStatus(taskId, 'success', response.result)

          return response.result
        }

        // 如果任务失败，设置错误信息
        if (response.status === 'failed') {
          this.error = response.message || '提取任务失败'

          // 更新任务状态
          this.updateTaskStatus(taskId, 'failed', null, this.error)

          throw new Error(this.error)
        }

        // 如果任务被取消，设置状态
        if (response.status === 'cancelled') {
          // 更新任务状态
          this.updateTaskStatus(taskId, 'cancelled')

          return {
            task_id: taskId,
            status: 'cancelled',
            message: response.message || '任务已取消'
          }
        }

        // 如果任务仍在处理中，返回状态
        // 更新任务进度
        const task = this.taskList.find(t => t.id === taskId)
        if (task) {
          task.progress = response.progress || 0
          this.saveTasksToStorage()
        }

        return {
          task_id: taskId,
          status: response.status,
          progress: response.progress
        }
      } catch (error) {
        this.error = error.message || '获取提取任务状态失败'
        throw error
      }
    },

    async cancelExtractionTask(taskId) {
      this.loading = true
      this.error = null

      try {
        // 调用API取消提取任务
        const response = await extractionApi.cancelExtractionTask(taskId)

        // 更新任务状态
        this.updateTaskStatus(taskId, 'cancelled')

        // 返回取消结果
        return {
          task_id: taskId,
          status: 'cancelled',
          message: response.message || '任务已取消'
        }
      } catch (error) {
        this.error = error.message || '取消提取任务失败'
        throw error
      } finally {
        this.loading = false
      }
    },

    // 任务列表管理方法

    // 从本地存储加载任务列表
    loadTasksFromStorage() {
      if (this.taskListLoaded) return

      try {
        const storedTasks = localStorage.getItem('extraction_tasks')
        if (storedTasks) {
          this.taskList = JSON.parse(storedTasks)
        }
        this.taskListLoaded = true
      } catch (error) {
        console.error('从本地存储加载任务失败:', error)
      }
    },

    // 保存任务列表到本地存储
    saveTasksToStorage() {
      try {
        localStorage.setItem('extraction_tasks', JSON.stringify(this.taskList))
      } catch (error) {
        console.error('保存任务到本地存储失败:', error)
      }
    },

    // 获取任务列表（分页）
    async getTasks(page = 1, pageSize = 20) {
      this.loadTasksFromStorage()

      try {
        // 尝试从API获取任务列表
        try {
          const response = await extractionApi.getExtractionHistory()
          if (response && response.items) {
            // 如果API返回了任务列表，使用API返回的数据
            // 并更新本地存储
            this.taskList = response.items.map(item => ({
              id: item.id,
              schemaId: item.schema_id,
              schemaName: item.schema_name,
              fileName: item.file_name || '未知文件',
              status: item.status,
              createdAt: item.extraction_time,
              result: item.data
            }))
            this.saveTasksToStorage()
          }
        } catch (apiError) {
          console.warn('从API获取任务列表失败，使用本地存储:', apiError)
        }

        // 计算分页
        const start = (page - 1) * pageSize
        const end = start + pageSize
        const paginatedTasks = this.taskList.slice(start, end)

        return {
          success: true,
          data: {
            tasks: paginatedTasks,
            total: this.taskList.length
          }
        }
      } catch (error) {
        console.error('获取任务列表失败:', error)
        return { success: false, error: error.message || '获取任务列表失败' }
      }
    },

    // 添加任务到列表
    addTask(task) {
      this.loadTasksFromStorage()

      // 检查任务是否已存在
      const existingTaskIndex = this.taskList.findIndex(t => t.id === task.id)
      if (existingTaskIndex !== -1) {
        // 更新现有任务
        this.taskList[existingTaskIndex] = { ...this.taskList[existingTaskIndex], ...task }
      } else {
        // 添加新任务
        this.taskList.unshift(task)
      }

      this.saveTasksToStorage()
    },

    // 删除任务
    async deleteTask(taskId) {
      this.loadTasksFromStorage()

      try {
        // 从列表中删除任务
        const index = this.taskList.findIndex(t => t.id === taskId)
        if (index !== -1) {
          this.taskList.splice(index, 1)
          this.saveTasksToStorage()
        }

        return { success: true }
      } catch (error) {
        console.error('删除任务失败:', error)
        return { success: false, error: error.message || '删除任务失败' }
      }
    },

    // 更新任务状态
    updateTaskStatus(taskId, status, result = null, error = null) {
      this.loadTasksFromStorage()

      const task = this.taskList.find(t => t.id === taskId)
      if (task) {
        task.status = status
        if (result) task.result = result
        if (error) task.error = error
        task.updatedAt = new Date().toISOString()
        this.saveTasksToStorage()
      }
    }
  }
})
