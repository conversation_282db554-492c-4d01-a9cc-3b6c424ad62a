# XExtract

A powerful LLM-based information extraction service for structured data extraction from various document types.

## Features

- 🎯 Schema-driven information extraction
- 🤖 Multiple LLM support (OpenAI, Gemini, etc.)
- 📄 Support for various document types (PDF, Word, Text, etc.)
- 🔄 Prompt management and templating
- 🎨 Pre/post processing pipeline
- 💾 Result validation and normalization
- 📊 Batch processing support
- 🚀 Async processing for better performance
- 🔌 Extensible architecture

## Project Structure

```
xextract/
├── src/                  # Source code
│   ├── xextract/         # Main package
│   │   ├── engine/        # Extraction engine
│   │   ├── extractors/     # Document extractors
│   │   ├── llm/           # LLM integrations
│   │   ├── schema/        # Schema definitions
│   │   ├── storage/       # Storage backends
│   │   ├── utils/         # Utility functions
│   │   └── __init__.py    # Package initialization
│   └── __init__.py        # Package initialization
├── config/                # Configuration files
│   ├── .env.example       # Example environment variables
│   ├── config.json.example # Example JSON configuration
│   └── README.md          # Configuration documentation
├── models/                # Model definitions
│   ├── announcement/      # Announcement models
│   ├── IPO/              # IPO models
│   └── README.md          # Model documentation
├── tests/                # Tests
│   ├── unit/            # Unit tests
│   ├── integration/      # Integration tests
│   └── data/            # Test data
├── examples/              # Example code
├── docs/                  # Documentation
├── requirements.txt       # Dependencies
├── requirements-dev.txt   # Development dependencies
├── setup.py               # Package setup
└── README.md              # This file
```

## Installation

### Prerequisites

- Python 3.8 or higher
- [uv](https://github.com/astral-sh/uv) (recommended) or pip for package management

### Dependencies

- httpx >= 0.24.0 - HTTP client for API calls
- PyPDF2 >= 3.0.0 - PDF parsing library
- python-dotenv >= 1.0.0 - Environment variable management
- pydantic >= 2.0.0 - Data validation and settings management
- typing-extensions >= 4.0.0 - Additional typing support

### Installation Steps

1. Clone the repository:
```bash
git clone https://github.com/yourusername/xextract.git
cd xextract
```

2. Create a virtual environment:
```bash
# Using uv (recommended)
uv venv
source .venv/bin/activate  # Linux/Mac
# or
.venv\Scripts\activate  # Windows

# Or using standard venv
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
# or
.venv\Scripts\activate  # Windows
```

3. Install the package in development mode:
```bash
# Using uv (recommended)
uv pip install -e .

# Or using pip
pip install -e .
```

4. Install development dependencies (optional):
```bash
# Using uv
uv pip install -e ".[dev]"

# Or using pip
pip install -e ".[dev]"
```

5. Configure environment variables:
```bash
cp config/.env.example .env
# Edit .env file with your LLM API keys and settings
```

## Example Usage

### Python API

1. Define an extraction schema:
```yaml
name: financial_announcement
description: Extract key information from financial announcements
fields:
  - name: announcement_type
    type: string
    description: Type of the financial announcement
    required: true
    validation:
      enum:
        - performance_forecast
        - profit_warning
        - earnings_report

  - name: fiscal_period
    type: string
    description: The fiscal period this announcement refers to
    required: true

  - name: expected_net_profit
    type: object
    description: Expected net profit range
    fields:
      - name: min
        type: float
        required: true
      - name: max
        type: float
        required: true
      - name: unit
        type: string
        required: true
        validation:
          enum: [CNY, USD]
```

2. Extract information from a document:
```python
from xextract import Extractor

# Initialize extractor with schema
extractor = Extractor.from_schema("financial_announcement")

# Extract from text
result = await extractor.extract_from_text("""
根据公司财务部门初步测算，预计2023年前三季度实现归属于上市公司股东的净利润为
52,000万元至58,000万元，同比增长约15%至28%。
""")

# Result will be:
{
    "announcement_type": "performance_forecast",
    "fiscal_period": "2023Q3",
    "expected_net_profit": {
        "min": 520000000,
        "max": 580000000,
        "unit": "CNY"
    }
}
```

### Command Line Interface

XExtract provides a command-line interface for extracting data from documents:

```bash
# Extract data from a file
xextract from-source path/to/document.pdf --output result.json

# Extract data from text
xextract from-text "Text content to extract from" --output result.json

# Start the API server
xextract serve --host 0.0.0.0 --port 8000

# Show help
xextract --help
```

If you've installed the package in development mode, you can also run the CLI using the provided script:

```bash
./xextract_cli.py from-source path/to/document.pdf
```

### Web Interface

XExtract includes a web interface for managing schemas, prompts, and extracting data from documents. To use the web interface:

1. Install the API dependencies:
```bash
# Using uv
uv pip install -r requirements-api.txt

# Or using pip
pip install -r requirements-api.txt
```

2. Start the API server:
```bash
xextract serve
```

3. Open your browser and navigate to http://localhost:8000

## API Endpoints

### Extract from Text
```bash
curl -X POST "http://localhost:8000/extract" \
  -H "Content-Type: application/json" \
  -d '{
    "schema": "financial_announcement",
    "text": "...",
    "options": {
      "llm": "openai",
      "model": "gpt-4"
    }
  }'
```

### Extract from File
```bash
curl -X POST "http://localhost:8000/extract/file" \
  -F "schema=financial_announcement" \
  -F "file=@announcement.pdf" \
  -F "options={\"llm\":\"openai\",\"model\":\"gpt-4\"}"
```

## Configuration

XExtract uses environment variables for configuration. You can set these variables in two ways:

1. Environment variables directly
2. `.env` file in the project root directory

### Quick Start

1. Copy the example configuration file:
```bash
cp .env.example .env
```

2. Edit the `.env` file to match your environment:
```bash
# Edit configuration
vim .env
```

### Configuration Structure

The configuration files are located in the `config` directory, separate from the source code. This separation allows for better control and management of configuration settings.

```
config/
├── .env.example           # Example environment variables
├── config.json.example    # Example JSON configuration
├── schemas/               # Schema definitions
│   ├── announcement.json  # Announcement schema
│   └── ipo.json           # IPO schema
└── ...                    # Other configuration files
```

### Configuration Options

#### Application Settings
- `XEXTRACT_APP_NAME`: Application name
- `XEXTRACT_DEBUG`: Debug mode (true/false)
- `XEXTRACT_VERSION`: Application version

#### Storage Settings
- `XEXTRACT_STORAGE__TYPE`: Storage backend type (file/redis/mongodb)
- `XEXTRACT_STORAGE__PATH`: Base path for file storage
- `XEXTRACT_STORAGE__MAX_CONNECTIONS`: Maximum number of concurrent connections
- `XEXTRACT_STORAGE__TIMEOUT`: Connection timeout in seconds

##### Redis Storage
When using Redis storage (`XEXTRACT_STORAGE__TYPE=redis`):
- `XEXTRACT_STORAGE__REDIS_URL`: Redis connection URL

##### MongoDB Storage
When using MongoDB storage (`XEXTRACT_STORAGE__TYPE=mongodb`):
- `XEXTRACT_STORAGE__MONGODB_URL`: MongoDB connection URL

#### LLM Settings
- `XEXTRACT_LLM__PROVIDER`: LLM provider (openai, gemini, etc.)
- `XEXTRACT_LLM__MODEL`: Model name to use (e.g., gpt-4o, gemini-2.0-flash)
- `XEXTRACT_LLM__API_KEY`: API key for the provider
- `XEXTRACT_LLM__API_BASE_URL`: Base URL for the API
- `XEXTRACT_LLM__TIMEOUT`: Timeout in seconds
- `XEXTRACT_LLM__MAX_RETRIES`: Maximum number of retries
- `XEXTRACT_LLM__TEMPERATURE`: Temperature for generation (0.0-1.0)
- `XEXTRACT_LLM__MAX_TOKENS`: Maximum tokens to generate

##### OpenAI Settings
When using OpenAI (`XEXTRACT_LLM__PROVIDER=openai`):
- Default API base URL: `https://api.openai.com/v1`
- Recommended models: `gpt-4o`, `gpt-4-turbo`, `gpt-3.5-turbo`

##### Gemini Settings
When using Gemini (`XEXTRACT_LLM__PROVIDER=gemini`):
- Default API base URL: `https://generativelanguage.googleapis.com/v1beta/openai`
- Recommended models: `gemini-2.0-flash`, `gemini-2.0-pro`

#### Document Parser Settings
- `XEXTRACT_DOCUMENT_PARSER__API_BASE_URL`: Base URL for the document parsing API
- `XEXTRACT_DOCUMENT_PARSER__API_KEY`: API key for the document parsing service
- `XEXTRACT_DOCUMENT_PARSER__TIMEOUT`: Timeout in seconds
- `XEXTRACT_DOCUMENT_PARSER__MAX_RETRIES`: Maximum number of retries
- `XEXTRACT_DOCUMENT_PARSER__MAX_FILE_SIZE`: Maximum file size in bytes
- `XEXTRACT_DOCUMENT_PARSER__SUPPORTED_FORMATS`: List of supported file formats

#### Database Settings
- `XEXTRACT_DATABASE__TYPE`: Database type (sqlite, mysql)
- `XEXTRACT_DATABASE__PATH`: Path to SQLite database file (for SQLite)
- `XEXTRACT_DATABASE__HOST`: Database host (for MySQL)
- `XEXTRACT_DATABASE__PORT`: Database port (for MySQL)
- `XEXTRACT_DATABASE__USER`: Database username (for MySQL)
- `XEXTRACT_DATABASE__PASSWORD`: Database password (for MySQL)
- `XEXTRACT_DATABASE__NAME`: Database name (for MySQL)
- `XEXTRACT_DATABASE__POOL_SIZE`: Connection pool size
- `XEXTRACT_DATABASE__MAX_OVERFLOW`: Maximum number of connections to overflow
- `XEXTRACT_DATABASE__POOL_TIMEOUT`: Connection pool timeout in seconds
- `XEXTRACT_DATABASE__ECHO`: Whether to echo SQL statements

#### Logging Settings
- `XEXTRACT_LOGGING__LEVEL`: Log level (DEBUG/INFO/WARNING/ERROR/CRITICAL)
- `XEXTRACT_LOGGING__FORMAT`: Log message format
- `XEXTRACT_LOGGING__FILE`: Log file path (optional)
- `XEXTRACT_LOGGING__MAX_SIZE`: Maximum log file size in MB
- `XEXTRACT_LOGGING__BACKUP_COUNT`: Number of backup log files to keep

#### Performance Settings
- `XEXTRACT_MAX_SCHEMA_SIZE`: Maximum schema size in KB
- `XEXTRACT_REQUEST_TIMEOUT`: API request timeout in seconds

### JSON Configuration

Alternatively, you can use a JSON configuration file:

```bash
cp config/config.json.example config/config.json
# Edit configuration
vim config/config.json
```

Then load the configuration file when starting the application:

```bash
python -m xextract --config config/config.json
```

### Configuration Priority

Configuration settings are loaded in the following order (later sources override earlier ones):

1. Default values from Settings class
2. Config files (first match from DEFAULT_CONFIG_PATHS)
   - `./config/config.json`
   - `./config.json`
   - `~/.xextract/config.json`
3. Environment files (first match from DEFAULT_ENV_PATHS)
   - `./.env`
   - `./config/.env`
   - `~/.xextract/.env`
4. Environment variables

## Development

The project uses modern Python development tools for testing and code quality. Development dependencies are specified in the `pyproject.toml` file under `[project.optional-dependencies]`.

1. Install development dependencies:
```bash
# Using uv
uv pip install -e ".[dev]"

# Or using pip
pip install -e ".[dev]"
```

2. Run tests:
```bash
python -m pytest
```

3. Test document parser service:
```bash
# Test PDF to document structure extraction
./tests/test_docparser.sh tests/data/sample.pdf pdf2doc

# Test PDF to table extraction
./tests/test_docparser.sh tests/data/sample.pdf pdf2table

# Test DOCX to PDF conversion
./tests/test_docparser.sh tests/data/sample.docx docx2pdf

# With custom API URL
python -m tests.integration.test_docparser_service --method pdf2doc --input tests/data/sample.pdf --api-url http://custom-api.example.com --verbose
```

4. Test complete PDF extraction pipeline:
```bash
# Test PDF extraction with default schema (shareholder_meeting)
./tests/test_pdf_extraction.sh tests/data/sample.pdf

# Test PDF extraction with specific schema
./tests/test_pdf_extraction.sh tests/data/sample.pdf announcement

# With custom options
python -m tests.integration.test_pdf_extraction_pipeline --input tests/data/sample.pdf --schema shareholder_meeting --llm-provider openai --verbose
```

5. Check code style:
```bash
# Format code with black
black .

# Sort imports
isort .

# Type checking
mypy .

# Linting
ruff check .
```

## Evaluation

The project includes an evaluation tool to measure the performance of the extraction engine against ground truth data. This tool calculates various metrics such as accuracy, precision, recall, and F1 score.

```bash
# Evaluate a single file
python evaluate.py --test-file <test_file> --ground-truth-file <ground_truth_file> --schema <schema_name>

# Evaluate a test set
python evaluate.py --test-dir <test_dir> --ground-truth-dir <ground_truth_dir> --schema <schema_name>

# Save evaluation results to a file
python evaluate.py --test-dir <test_dir> --ground-truth-dir <ground_truth_dir> --schema <schema_name> --output <output_file>
```

For more options, run `python evaluate.py --help`.

## License

MIT License
