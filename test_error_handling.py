#!/usr/bin/env python3
"""
测试流水线错误处理机制
"""

import asyncio
import logging
import time
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_error_handling():
    """测试各种错误场景"""
    
    from src.xextract.engine import PipelineExtractorDriver
    
    print("🧪 测试流水线错误处理机制")
    print("=" * 50)
    
    # 创建驱动器
    driver = PipelineExtractorDriver(
        parse_workers=1,
        extract_workers=1,
        queue_size=5
    )
    
    test_cases = [
        {
            "name": "正常文件",
            "file": "tests/data/doc.json",
            "schema": "shareholder_meeting_notice",
            "expected": "success"
        },
        {
            "name": "不存在的文件",
            "file": "tests/data/nonexistent.pdf",
            "schema": "shareholder_meeting_notice", 
            "expected": "error"
        },
        {
            "name": "无效的schema",
            "file": "tests/data/doc.json",
            "schema": "invalid_schema_name",
            "expected": "error"
        }
    ]
    
    results = []
    
    try:
        with driver:
            print("✓ 流水线启动成功")
            
            for i, test_case in enumerate(test_cases, 1):
                print(f"\n📋 测试 {i}: {test_case['name']}")
                print(f"   文件: {test_case['file']}")
                print(f"   Schema: {test_case['schema']}")
                
                start_time = time.time()
                
                try:
                    # 检查文件是否存在
                    if not Path(test_case['file']).exists():
                        print(f"   ⚠️  文件不存在，跳过测试")
                        results.append({
                            "test": test_case['name'],
                            "status": "skipped",
                            "reason": "file_not_found"
                        })
                        continue
                    
                    result = driver.extract_from_file_sync(
                        test_case['file'], 
                        schema_name=test_case['schema'],
                        timeout=30
                    )
                    
                    processing_time = time.time() - start_time
                    
                    if result and result.get('status') == 'success':
                        print(f"   ✅ 成功 ({processing_time:.2f}s)")
                        print(f"   📊 提取字段数: {len(result.get('extraction_result', {}))}")
                        results.append({
                            "test": test_case['name'],
                            "status": "success",
                            "processing_time": processing_time
                        })
                    else:
                        print(f"   ❌ 失败: {result.get('error', 'Unknown error')}")
                        results.append({
                            "test": test_case['name'],
                            "status": "error",
                            "error": result.get('error', 'Unknown error'),
                            "processing_time": processing_time
                        })
                        
                except Exception as e:
                    processing_time = time.time() - start_time
                    print(f"   💥 异常: {str(e)}")
                    results.append({
                        "test": test_case['name'],
                        "status": "exception",
                        "error": str(e),
                        "processing_time": processing_time
                    })
            
            # 获取流水线统计
            stats = driver.get_stats()
            print(f"\n📈 流水线统计:")
            print(f"   已处理项目: {stats['items_processed']}")
            print(f"   失败项目: {stats['items_failed']}")
            
            # 检查死信队列
            failed_count = driver.pipeline.get_failed_items_count()
            if failed_count > 0:
                print(f"   💀 死信队列项目: {failed_count}")
                
                # 获取失败的项目
                print(f"\n🔍 检查失败项目:")
                while failed_count > 0:
                    failed_item = driver.pipeline.get_failed_item(timeout=1.0)
                    if failed_item:
                        print(f"   - 项目 {failed_item.id}: {failed_item.error}")
                        print(f"     重试次数: {failed_item.retry_count}/{failed_item.max_retries}")
                        print(f"     致命错误: {failed_item.is_fatal_error}")
                        failed_count -= 1
                    else:
                        break
            
    except Exception as e:
        print(f"💥 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 打印测试总结
    print(f"\n📋 测试总结:")
    print("=" * 30)
    
    success_count = sum(1 for r in results if r['status'] == 'success')
    error_count = sum(1 for r in results if r['status'] in ['error', 'exception'])
    skipped_count = sum(1 for r in results if r['status'] == 'skipped')
    
    print(f"✅ 成功: {success_count}")
    print(f"❌ 失败: {error_count}")
    print(f"⏭️  跳过: {skipped_count}")
    print(f"📊 总计: {len(results)}")
    
    # 详细结果
    for result in results:
        status_icon = {
            'success': '✅',
            'error': '❌', 
            'exception': '💥',
            'skipped': '⏭️'
        }.get(result['status'], '❓')
        
        print(f"{status_icon} {result['test']}: {result['status']}")
        if 'error' in result:
            print(f"   错误: {result['error']}")
        if 'processing_time' in result:
            print(f"   耗时: {result['processing_time']:.2f}s")
    
    return True

if __name__ == "__main__":
    test_error_handling()
