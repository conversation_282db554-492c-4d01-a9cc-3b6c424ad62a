"""Configuration for XExtract.

This module provides a simple interface for loading and accessing configuration settings.
It supports loading settings from environment variables and .env files.
"""

import os
import logging
import sys
from pathlib import Path
from typing import Dict, Any, Optional, List, Union

# 定义项目根目录
ROOT_DIR = Path(__file__).parent.parent.absolute()
# 将根目录添加到Python路径
sys.path.insert(0, str(ROOT_DIR))

from dotenv import load_dotenv


# 设置日志记录器
logger = logging.getLogger(__name__)

# 默认环境变量文件路径
DEFAULT_ENV_PATH = ".env"

# 默认配置值
DEFAULT_CONFIG = {
    # 应用设置
    "APP_NAME": "XExtract",
    "DEBUG": False,
    "VERSION": "202505.0.0.1",
    # 提取设置
    "EXTRACTION_TIMEOUT": 300,  # 提取超时时间，单位：秒
    # LLM 默认设置
    "LLM_DEFAULT_MODEL": "ds_14b",
    # LLM 模型列表及其配置
    "LLM_MODELS": [
        {
            "id": "ds_14b",
            "name": "ds_14b",
            "provider": "memect",
            "description": "文因私有化大模型",
            "api_key": "laozi-api-key",
            "api_base_url": "http://**************:8077/v1",
            "max_tokens": 15000,
            "temperature": 0.0,
            "timeout": 200,
            "max_retries": 3,
            "is_default": True,
        },
        {
            "id": "gemini-2.5-flash",
            "name": "Gemini 2.5 Flash",
            "provider": "gemini",
            "description": "Google Gemini 2.5 Flash - 快速响应的大模型",
            "api_key": "",
            "api_base_url": "https://generativelanguage.googleapis.com/v1beta/openai/",
            "max_tokens": 10000,
            "temperature": 0,
            "timeout": 120,
            "max_retries": 3,
            "is_default": False,
        },
    ],
    # 提示词模板设置
    "PROMPTS_TEMPLATES_DIR": "models/prompts",
    "PROMPTS_TASK_TEMPLATES": {
        "extraction": "extraction/extract.txt",
        "classification": "classification/classify.txt",
        "summarization": "summarization/summarize.txt",
    },
    # 文档解析器设置
    "DOCUMENT_PARSER_API_BASE_URL": "http://api.memect.cn:6111",
    "DOCUMENT_PARSER_API_KEY": "",
    "DOCUMENT_PARSER_TIMEOUT": 60,
    "DOCUMENT_PARSER_MAX_RETRIES": 3,
    # PDF2DOC 文档结构提取设置
    "PDF2DOC_EXTRACT_IMAGE": False,
    "PDF2DOC_OCR": False,
    "PDF2DOC_OCR_LINE": True,
    "PDF2DOC_FORMAT": 4,
    "PDF2DOC_TEXTLINES": False,
    "PDF2DOC_TABLE": "all",
    "PDF2DOC_LAYOUT": "auto",
    "PDF2DOC_ANALYZER": "auto",
    "PDF2DOC_MODE": 3,
    "PDF2DOC_MERGE_TABLE": True,
    "PDF2DOC_HTML": False,
    "PDF2DOC_HTML_SCALE": 1,
    "PDF2DOC_TABLE_SCREENSHOT": False,
    # PDF2TABLE 表格提取设置
    "PDF2TABLE_EXTRACT_IMAGE": False,
    "PDF2TABLE_OCR": False,
    "PDF2TABLE_OCR_LINE": True,
    "PDF2TABLE_FORMAT": 4,
    "PDF2TABLE_TEXTLINES": False,
    "PDF2TABLE_TABLE": "all",
    "PDF2TABLE_LAYOUT": "auto",
    "PDF2TABLE_ANALYZER": "auto",
    "PDF2TABLE_TEXT": False,
    "PDF2TABLE_EXCEL": False,
    "PDF2TABLE_HTML": False,
    "PDF2TABLE_HTML_SCALE": 1,
    "PDF2TABLE_TABLE_SCREENSHOT": False,
    # DOCX转PDF设置
    "DOCX2PDF_TABLE_BORDER": 2,
    # 缓存设置
    "CACHE_ENABLED": True,
    "CACHE_TYPE": "file",
    "CACHE_DIR": ".cache/xextract",
    "PDF_CACHE_ENABLED": True,
    "PDF_CACHE_TYPE": "file",
    "PDF_CACHE_DIR": ".cache/xextract/pdf",
    # 任务设置
    "MAX_CONCURRENT_TASKS": 5,
    # 流水线设置
    "PIPELINE_ENABLED": True,
    "PIPELINE_PARSE_WORKERS": 2,
    "PIPELINE_CLASSIFY_WORKERS": 1,
    "PIPELINE_EXTRACT_WORKERS": 2,
    "PIPELINE_QUEUE_SIZE": 100,
    "PIPELINE_TIMEOUT": 300.0,
    "PIPELINE_RETRY_COUNT": 3,
    # 流水线阶段配置
    "PIPELINE_PARSE_TIMEOUT": 120.0,
    "PIPELINE_CLASSIFY_TIMEOUT": 60.0,
    "PIPELINE_EXTRACT_TIMEOUT": 180.0,
    # 分类规则配置 (目前主要用于扩展性，实际使用中可以简化为直接使用默认schema)
    "PIPELINE_CLASSIFICATION_ENABLED": False,  # 是否启用分类功能
    "PIPELINE_DEFAULT_SCHEMA": "test_schema",  # 默认使用的schema
    "PIPELINE_CLASSIFICATION_RULES": {
        # 预留的分类规则，用于未来扩展
        "general_document": {
            "keywords": [],
            "schema": "test_schema",
            "confidence_threshold": 0.9
        }
    },
    # 日志设置
    "LOG_LEVEL": "INFO",
    "LOG_FORMAT": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "LOG_FILE": "logs/xextract.log",
}


class Config:
    """Configuration class for XExtract.

    This class provides a simple interface for accessing configuration settings.
    It loads settings from environment variables and .env files.
    """

    def __init__(self, env_file: Optional[str] = None):
        """Initialize configuration.

        Args:
            env_file: Path to .env file (default: None, will use DEFAULT_ENV_PATH)
        """
        # 加载环境变量
        self._load_env_vars(env_file)

        # 初始化配置字典
        self._config = DEFAULT_CONFIG.copy()

        # 从环境变量更新配置
        self._update_from_env()

        # 记录加载的配置
        logger.info(f"Configuration loaded: {self.get_masked_config()}")

    def _load_env_vars(self, env_file: Optional[str] = None):
        """Load environment variables from .env file.

        Args:
            env_file: Path to .env file (default: None, will use DEFAULT_ENV_PATH)
        """

        # 使用指定的环境变量文件或默认文件
        env_path = env_file or DEFAULT_ENV_PATH

        # 加载环境变量
        if os.path.exists(env_path):
            load_dotenv(env_path)
            logger.info(f"Loaded environment variables from {env_path}")

    def _update_from_env(self):
        """Update configuration from environment variables."""
        # 处理环境变量 (支持新旧两种格式)
        self._process_standard_env_vars()
        self._process_legacy_env_vars()

    def _process_standard_env_vars(self):
        """处理标准格式的环境变量 (XEXTRACT_KEY)"""
        for key in self._config:
            env_key = f"XEXTRACT_{key}"
            if env_key in os.environ:
                self._set_config_value(key, os.environ[env_key])

    def _process_legacy_env_vars(self):
        """处理旧格式的环境变量 (XEXTRACT_SECTION__KEY)"""
        for key in os.environ:
            if key.startswith("XEXTRACT_") and "__" in key:
                parts = key.split("__")
                if len(parts) == 2:
                    prefix, subkey = parts
                    prefix = prefix.replace("XEXTRACT_", "")
                    new_key = f"{prefix}_{subkey}"

                    if new_key in self._config:
                        self._set_config_value(new_key, os.environ[key])

    def _set_config_value(self, key: str, value: str):
        """根据目标类型设置配置值"""
        if isinstance(self._config[key], bool):
            self._config[key] = value.lower() in ("true", "yes", "1", "y")
        elif isinstance(self._config[key], int):
            self._config[key] = int(value)
        elif isinstance(self._config[key], float):
            self._config[key] = float(value)
        else:
            self._config[key] = value

    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value.

        Args:
            key: Configuration key
            default: Default value if key is not found

        Returns:
            Configuration value
        """
        return self._config.get(key, default)

    def __getattr__(self, name: str) -> Any:
        """Get configuration value as attribute.

        Args:
            name: Configuration key

        Returns:
            Configuration value

        Raises:
            AttributeError: If key is not found
        """
        if name in self._config:
            return self._config[name]
        raise AttributeError(f"Config has no attribute '{name}'")

    def get_masked_config(self) -> Dict[str, Any]:
        """Get configuration with sensitive values masked.

        Returns:
            Configuration dictionary with sensitive values masked
        """
        masked_config = self._config.copy()

        # 掩盖敏感信息
        sensitive_keys = ["API_KEY", "PASSWORD", "SECRET"]
        for key in masked_config:
            if (
                any(sensitive in key for sensitive in sensitive_keys)
                and masked_config[key]
            ):
                masked_config[key] = (
                    f"{masked_config[key][:5]}..."
                    if len(str(masked_config[key])) > 5
                    else "***"
                )

        return masked_config

    @property
    def llm(self) -> Dict[str, Any]:
        """获取默认LLM模型配置。

        如果找不到默认模型，则返回第一个可用模型。
        如果模型列表为空，则返回基本配置。

        Returns:
            Dict[str, Any]: 默认LLM模型的配置
        """
        # 获取默认模型ID
        default_model_id = getattr(self, "LLM_DEFAULT_MODEL", "")

        # 获取模型列表
        models = getattr(self, "LLM_MODELS", [])

        # 在模型列表中查找默认模型
        if default_model_id:
            for model in models:
                if model.get("id") == default_model_id:
                    return model

            logger.warning(f"找不到ID为'{default_model_id}'的默认模型，使用第一个可用模型")

        # 如果没有找到默认模型或未指定默认模型ID，返回第一个模型
        if models:
            return models[0]

        # 如果模型列表为空，返回基本配置
        logger.warning("LLM模型列表为空，返回基本配置")
        return {
            "id": default_model_id or "default",
            "name": default_model_id or "Default Model",
            "provider": "openai",
            "description": "默认模型配置",
            "api_key": "",
            "api_base_url": "https://api.openai.com/v1",
            "max_tokens": 4096,
            "temperature": 0.0,
            "timeout": 120,
            "max_retries": 3,
            "is_default": True,
        }

    @property
    def llm_models(self) -> List[Dict[str, Any]]:
        """获取可用的LLM模型列表，并确保默认模型标记正确。

        Returns:
            List[Dict[str, Any]]: 可用的LLM模型列表
        """
        # 获取配置中的模型列表
        models = getattr(self, "LLM_MODELS", []).copy()

        # 获取默认模型ID
        default_model_id = getattr(self, "LLM_DEFAULT_MODEL", "")

        # 如果模型列表为空，返回空列表
        if not models:
            return []

        # 确保默认模型标记正确
        default_found = False
        for model in models:
            if model.get("id") == default_model_id:
                model["is_default"] = True
                default_found = True
            else:
                model["is_default"] = False

        # 如果没有找到默认模型，将第一个模型标记为默认
        if not default_found and models:
            models[0]["is_default"] = True
            logger.info(f"未找到默认模型'{default_model_id}'，将'{models[0].get('id')}'设为默认模型")

        return models

    def get_model_by_id(self, model_id: str) -> Dict[str, Any]:
        """根据模型ID获取模型配置。

        Args:
            model_id: 模型ID

        Returns:
            Dict[str, Any]: 模型配置，如果找不到则返回默认模型配置
        """
        # 获取模型列表
        models = self.llm_models

        # 在模型列表中查找指定模型
        for model in models:
            if model.get("id") == model_id:
                return model

        # 如果找不到指定模型，返回默认模型
        logger.warning(f"找不到ID为'{model_id}'的模型，使用默认模型")
        return self.llm

    @property
    def document_parser(self) -> Dict[str, Any]:
        """Get document parser configuration.

        Returns:
            Document parser configuration dictionary
        """
        return {
            "api_base_url": self.DOCUMENT_PARSER_API_BASE_URL,
            "api_key": self.DOCUMENT_PARSER_API_KEY,
            "timeout": self.DOCUMENT_PARSER_TIMEOUT,
            "max_retries": self.DOCUMENT_PARSER_MAX_RETRIES,
            "use_cache": self.PDF_CACHE_ENABLED,
            "cache_type": self.PDF_CACHE_TYPE,
            "cache_dir": self.PDF_CACHE_DIR,
        }

    @property
    def pdf2doc(self) -> Dict[str, Any]:
        """Get PDF to document structure extraction configuration.

        Returns:
            PDF2DOC configuration dictionary
        """
        return {
            "extract_image": self.PDF2DOC_EXTRACT_IMAGE,
            "ocr": self.PDF2DOC_OCR,
            "ocr_line": self.PDF2DOC_OCR_LINE,
            "format": self.PDF2DOC_FORMAT,
            "textlines": self.PDF2DOC_TEXTLINES,
            "table": self.PDF2DOC_TABLE,
            "layout": self.PDF2DOC_LAYOUT,
            "analyzer": self.PDF2DOC_ANALYZER,
            "mode": self.PDF2DOC_MODE,
            "merge_table": self.PDF2DOC_MERGE_TABLE,
            "html": self.PDF2DOC_HTML,
            "html_scale": self.PDF2DOC_HTML_SCALE,
            "table_screenshot": self.PDF2DOC_TABLE_SCREENSHOT,
        }

    @property
    def pdf2table(self) -> Dict[str, Any]:
        """Get PDF to table extraction configuration.

        Returns:
            PDF2TABLE configuration dictionary
        """
        return {
            "extract_image": self.PDF2TABLE_EXTRACT_IMAGE,
            "ocr": self.PDF2TABLE_OCR,
            "ocr_line": self.PDF2TABLE_OCR_LINE,
            "format": self.PDF2TABLE_FORMAT,
            "textlines": self.PDF2TABLE_TEXTLINES,
            "table": self.PDF2TABLE_TABLE,
            "layout": self.PDF2TABLE_LAYOUT,
            "analyzer": self.PDF2TABLE_ANALYZER,
            "text": self.PDF2TABLE_TEXT,
            "excel": self.PDF2TABLE_EXCEL,
            "html": self.PDF2TABLE_HTML,
            "html_scale": self.PDF2TABLE_HTML_SCALE,
            "table_screenshot": self.PDF2TABLE_TABLE_SCREENSHOT,
        }

    @property
    def docx2pdf(self) -> Dict[str, Any]:
        """Get DOCX to PDF conversion configuration.

        Returns:
            DOCX2PDF configuration dictionary
        """
        return {
            "table_border": self.DOCX2PDF_TABLE_BORDER,
        }

    @property
    def pipeline(self) -> Dict[str, Any]:
        """Get pipeline configuration.

        Returns:
            Pipeline configuration dictionary
        """
        return {
            "enabled": self.PIPELINE_ENABLED,
            "parse_workers": self.PIPELINE_PARSE_WORKERS,
            "classify_workers": self.PIPELINE_CLASSIFY_WORKERS,
            "extract_workers": self.PIPELINE_EXTRACT_WORKERS,
            "queue_size": self.PIPELINE_QUEUE_SIZE,
            "timeout": self.PIPELINE_TIMEOUT,
            "retry_count": self.PIPELINE_RETRY_COUNT,
            "parse_timeout": self.PIPELINE_PARSE_TIMEOUT,
            "classify_timeout": self.PIPELINE_CLASSIFY_TIMEOUT,
            "extract_timeout": self.PIPELINE_EXTRACT_TIMEOUT,
            "classification_enabled": self.PIPELINE_CLASSIFICATION_ENABLED,
            "default_schema": self.PIPELINE_DEFAULT_SCHEMA,
            "classification_rules": self.PIPELINE_CLASSIFICATION_RULES,
        }

    @property
    def pipeline_stage_configs(self) -> Dict[str, Dict[str, Any]]:
        """Get pipeline stage configurations.

        Returns:
            Dictionary with stage configurations
        """
        return {
            "parse": {
                "worker_count": self.PIPELINE_PARSE_WORKERS,
                "queue_size": self.PIPELINE_QUEUE_SIZE,
                "timeout": self.PIPELINE_PARSE_TIMEOUT,
                "retry_count": self.PIPELINE_RETRY_COUNT,
            },
            "classify": {
                "worker_count": self.PIPELINE_CLASSIFY_WORKERS,
                "queue_size": self.PIPELINE_QUEUE_SIZE,
                "timeout": self.PIPELINE_CLASSIFY_TIMEOUT,
                "retry_count": self.PIPELINE_RETRY_COUNT,
            },
            "extract": {
                "worker_count": self.PIPELINE_EXTRACT_WORKERS,
                "queue_size": self.PIPELINE_QUEUE_SIZE,
                "timeout": self.PIPELINE_EXTRACT_TIMEOUT,
                "retry_count": self.PIPELINE_RETRY_COUNT,
            },
        }


# 创建全局配置实例
config = Config()
