# XExtract 配置

本目录包含 XExtract 的配置文件和设置。

## 配置文件

- `config.json.example`: JSON 配置文件示例
- `.env.example`: 环境变量配置文件示例

## 配置结构

配置文件包含以下主要部分：

### 应用设置

```json
{
  "app_name": "XExtract",
  "debug": false,
  "version": "0.1.0"
}
```

### LLM 设置

```json
{
  "llm": {
    "provider": "gemini",
    "model": "gemini-2.0-flash",
    "api_key": "your-gemini-api-key",
    "api_base_url": "https://generativelanguage.googleapis.com/v1beta/openai/",
    "timeout": 120,
    "max_retries": 3,
    "temperature": 0.0,
    "max_tokens": 4096
  }
}
```

### 存储设置

```json
{
  "storage": {
    "type": "file",
    "path": "./data",
    "max_connections": 10,
    "timeout": 5.0
  }
}
```

### 文档解析器设置

```json
{
  "document_parser": {
    "api_base_url": "http://localhost:8080",
    "api_key": "your-document-parser-api-key",
    "timeout": 60,
    "max_retries": 3,
    "max_file_size": 10485760,
    "supported_formats": ["pdf", "docx", "doc", "txt", "rtf", "pptx"]
  }
}
```

### 日志设置

```json
{
  "logging": {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": "logs/xextract.log",
    "max_size": 10,
    "backup_count": 30
  }
}
```

## 使用方法

1. 复制示例配置文件：

```bash
cp config/config.json.example config/config.json
# 或者
cp config/.env.example .env
```

2. 编辑配置文件，设置您的 API 密钥和其他设置。

3. 在代码中使用配置：

```python
from config import settings

# 访问 LLM 设置
llm_provider = settings.llm.provider
llm_api_key = settings.llm.api_key

# 访问存储设置
storage_path = settings.storage.path

# 访问日志设置
log_level = settings.logging.level
```

## 配置优先级

配置按以下优先级加载（后面的会覆盖前面的）：

1. 默认值（在 Settings 类中定义）
2. 配置文件（从 DEFAULT_CONFIG_PATHS 中的第一个匹配项）
   - `./config/config.json`
   - `./config.json`
   - `~/.xextract/config.json`
3. 环境变量文件（从 DEFAULT_ENV_PATHS 中的第一个匹配项）
   - `./.env`
   - `./config/.env`
   - `~/.xextract/.env`
4. 环境变量
