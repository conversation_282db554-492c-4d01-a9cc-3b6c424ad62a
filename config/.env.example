# Application settings
XEXTRACT_APP_NAME=XExtract
XEXTRACT_DEBUG=false
XEXTRACT_API_PREFIX=/api/v1

# Server settings
XEXTRACT_HOST=0.0.0.0
XEXTRACT_PORT=8000
XEXTRACT_WORKERS=1
XEXTRACT_RELOAD=false

# Storage settings
XEXTRACT_STORAGE_TYPE=file
XEXTRACT_STORAGE_PATH=./data

# Cache settings
XEXTRACT_CACHE_TYPE=memory
XEXTRACT_CACHE_TTL=3600
XEXTRACT_REDIS_URL=redis://localhost:6379/0

# Database settings
XEXTRACT_DB_URL=sqlite:///./data/xextract.db
XEXTRACT_DB_POOL_SIZE=5
XEXTRACT_DB_MAX_OVERFLOW=10
XEXTRACT_DB_POOL_TIMEOUT=30

# Task settings
XEXTRACT_MAX_WORKERS=5
XEXTRACT_TASK_TIMEOUT=300
XEXTRACT_STORE_TASK_RESULTS=true
XEXTRACT_MAX_STORED_TASKS=1000

# LLM settings
XEXTRACT_LLM__PROVIDER=gemini
XEXTRACT_LLM__MODEL=gemini-2.0-flash
XEXTRACT_LLM__API_KEY=your-gemini-api-key
XEXTRACT_LLM__API_BASE_URL=https://generativelanguage.googleapis.com/v1beta/openai/
XEXTRACT_LLM__TIMEOUT=120
XEXTRACT_LLM__MAX_RETRIES=3
XEXTRACT_LLM__TEMPERATURE=0.0
XEXTRACT_LLM__MAX_TOKENS=8192

# Document Parser settings
XEXTRACT_DOCUMENT_PARSER__API_BASE_URL=http://api.memect.cn:6111
XEXTRACT_DOCUMENT_PARSER__API_KEY=your-document-parser-api-key
XEXTRACT_DOCUMENT_PARSER__TIMEOUT=60
XEXTRACT_DOCUMENT_PARSER__MAX_RETRIES=3
XEXTRACT_DOCUMENT_PARSER__MAX_FILE_SIZE=10485760
XEXTRACT_DOCUMENT_PARSER__POLL_INTERVAL=1
XEXTRACT_DOCUMENT_PARSER__ASYNC_MODE=false

# DOCX to PDF conversion settings
XEXTRACT_DOCX2PDF__TABLE_BORDER=2

# PDF to Table extraction settings
XEXTRACT_PDF2TABLE__EXTRACT_IMAGE=false
XEXTRACT_PDF2TABLE__OCR=false
XEXTRACT_PDF2TABLE__OCR_LINE=true
XEXTRACT_PDF2TABLE__FORMAT=4
XEXTRACT_PDF2TABLE__TEXTLINES=false
XEXTRACT_PDF2TABLE__TABLE=all
XEXTRACT_PDF2TABLE__LAYOUT=auto
XEXTRACT_PDF2TABLE__ANALYZER=auto
XEXTRACT_PDF2TABLE__TEXT=false
XEXTRACT_PDF2TABLE__EXCEL=false
XEXTRACT_PDF2TABLE__HTML=false
XEXTRACT_PDF2TABLE__HTML_SCALE=1
XEXTRACT_PDF2TABLE__TABLE_SCREENSHOT=false

# pdf2doc structure extraction settings
XEXTRACT_PDF2DOC__EXTRACT_IMAGE=false
XEXTRACT_PDF2DOC__OCR=false
XEXTRACT_PDF2DOC__OCR_LINE=true
XEXTRACT_PDF2DOC__FORMAT=4
XEXTRACT_PDF2DOC__TEXTLINES=false
XEXTRACT_PDF2DOC__TABLE=all
XEXTRACT_PDF2DOC__LAYOUT=auto
XEXTRACT_PDF2DOC__ANALYZER=auto
XEXTRACT_PDF2DOC__MODE=3
XEXTRACT_PDF2DOC__MERGE_TABLE=true
XEXTRACT_PDF2DOC__HTML=false
XEXTRACT_PDF2DOC__HTML_SCALE=1
XEXTRACT_PDF2DOC__TABLE_SCREENSHOT=false

# PDF settings
XEXTRACT_MAX_PDF_SIZE=10485760
XEXTRACT_PDF_TIMEOUT=60

# Schema settings
XEXTRACT_SCHEMAS_DIR=./schemas
XEXTRACT_DEFAULT_SCHEMA=

# Logging settings
XEXTRACT_LOG_LEVEL=INFO
XEXTRACT_LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
XEXTRACT_LOG_FILE=
