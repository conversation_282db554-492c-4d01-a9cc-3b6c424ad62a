.PHONY: test test-unit test-integration test-all clean

# Default target
all: test

# Install dependencies
install:
	pip install -r requirements.txt
	pip install -r requirements-dev.txt

# Run unit tests
test-unit:
	pytest -xvs tests/unit

# Run integration tests
test-integration:
	pytest -xvs tests/integration

# Run all tests
test-all:
	pytest -xvs

# Run tests with coverage
test-cov:
	pytest --cov=src --cov-report=term --cov-report=html

# Clean up
clean:
	rm -rf .pytest_cache
	rm -rf .coverage
	rm -rf htmlcov
	rm -rf tests/output/*
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

# Default test target
test: test-unit
