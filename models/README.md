# XExtract 模型定义

本目录包含 XExtract 的模型定义文件，用于定义不同类型文档的提取模式。

## 目录结构

```
models/
├── announcement/             # 公告类文档模型
│   └── performance_forecast.json  # 业绩预告提取模式
├── IPO/                      # IPO类文档模型
│   └── company_profile.json  # 公司基本信息提取模式
└── test_schema.json          # 测试用提取模式
```

## 模型格式

每个模型文件都是一个 JSON 文件，包含以下主要部分：

1. **基本信息**：模型的名称、版本和描述
2. **字段定义**：需要提取的字段列表，每个字段包含名称、类型、描述等信息
3. **元数据**：模型的元数据，如来源类型、行业等

示例：

```json
{
  "name": "test_schema",
  "version": "1.0",
  "description": "测试提取模式",
  "fields": [
    {
      "name": "title",
      "type": "string",
      "description": "文档标题",
      "required": true,
      "extraction_method": "llm",
      "prompt_hint": "这个文档的标题是什么？"
    },
    {
      "name": "author",
      "type": "string",
      "description": "文档作者",
      "required": false,
      "extraction_method": "regex",
      "regex_pattern": "作者[：:](.*?)(?:\\n|$)"
    }
  ],
  "metadata": {
    "source_type": "general",
    "version_history": [
      {
        "version": "1.0",
        "date": "2024-04-18",
        "changes": "初始版本"
      }
    ]
  }
}
```

## 字段类型

支持的字段类型包括：

- `string`：字符串
- `integer`：整数
- `float`：浮点数
- `boolean`：布尔值
- `date`：日期
- `datetime`：日期时间
- `object`：对象（包含嵌套字段）
- `array`：数组

## 提取方法

支持的提取方法包括：

- `regex`：使用正则表达式提取
- `llm`：使用大型语言模型提取
- `hybrid`：先尝试正则表达式，如果失败则使用大型语言模型

## 使用方法

在代码中使用模型：

```python
from src.xextract.schema import Schema
from src.xextract.engine import ExtractEngine

# 创建提取引擎
engine = ExtractEngine(
    llm_provider="gemini",
    llm_api_key="your-api-key"
)

# 从文件中提取数据
result = await engine.extract(
    file_path="path/to/document.pdf",
    schema_name="test_schema"
)

# 从文本中提取数据
result = await engine.extract_from_text(
    text="文档内容...",
    schema_name="test_schema"
)
```

## 添加新模型

要添加新的模型，请按照以下步骤操作：

1. 确定模型类型，并在相应的子目录中创建新的 JSON 文件
2. 定义模型的基本信息、字段和元数据
3. 测试模型的提取效果
4. 优化模型的字段定义和提取方法

## 最佳实践

- 为每个字段提供清晰的描述
- 对于正则表达式提取，尽量使用精确的模式
- 对于大型语言模型提取，提供有用的提示词
- 对于复杂字段，考虑使用嵌套对象
- 使用后处理器对提取结果进行处理
