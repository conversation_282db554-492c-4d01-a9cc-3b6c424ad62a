{"name": "company_profile", "version": "1.0", "description": "招股书中的公司基本信息提取模式", "fields": [{"name": "company_name", "type": "string", "description": "公司全称", "required": true, "extraction_method": "regex", "regex_pattern": "([\\u4e00-\\u9fa5]{2,}(?:股份)?(?:有限)?(?:公司))", "regex_group": 1}, {"name": "english_name", "type": "string", "description": "公司英文名称", "required": false, "extraction_method": "regex", "regex_pattern": "([A-Za-z\\s]+(?:Co\\.?|Ltd\\.?|Inc\\.?|Corporation|Company|Limited))", "regex_group": 1}, {"name": "stock_code", "type": "string", "description": "股票代码", "required": false, "extraction_method": "regex", "regex_pattern": "(?:股票代码：|证券代码：)?(\\d{6})", "regex_group": 1}, {"name": "legal_representative", "type": "string", "description": "法定代表人", "required": true, "extraction_method": "hybrid", "regex_pattern": "法定代表人：?([\\u4e00-\\u9fa5]{2,5})", "prompt_hint": "谁是公司的法定代表人？"}, {"name": "registered_capital", "type": "object", "description": "注册资本", "required": true, "extraction_method": "llm", "fields": [{"name": "amount", "type": "float", "description": "金额", "required": true, "extraction_method": "regex", "regex_pattern": "注册资本(?:金)?(?:为)?(?:人民币)?([\\d,]+(?:\\.\\d+)?)(?:万元|亿元|千元|元)", "regex_group": 1, "post_processors": ["remove_commas", "normalize_currency"]}, {"name": "unit", "type": "string", "description": "单位", "required": true, "default": "CNY", "extraction_method": "llm"}]}, {"name": "establishment_date", "type": "date", "description": "成立日期", "required": true, "extraction_method": "regex", "regex_pattern": "(?:成立日期|成立时间|设立日期)(?:为)?(?:：)?\\s*(\\d{4})(?:年|\\-)(\\d{1,2})(?:月|\\-)(\\d{1,2})(?:日)?", "format": "YYYY-MM-DD"}, {"name": "company_type", "type": "string", "description": "公司类型", "required": true, "extraction_method": "llm", "prompt_hint": "公司的类型是什么？例如：股份有限公司、有限责任公司等"}, {"name": "business_scope", "type": "string", "description": "经营范围", "required": true, "extraction_method": "hybrid", "regex_pattern": "经营范围：?(.*?)(?=\\n|。|；|$)", "prompt_hint": "公司的经营范围是什么？"}, {"name": "industry", "type": "string", "description": "所属行业", "required": true, "extraction_method": "llm", "prompt_hint": "公司所属的行业是什么？"}, {"name": "registered_address", "type": "address", "description": "注册地址", "required": true, "extraction_method": "hybrid", "regex_pattern": "(?:注册地址|住所)(?:为)?(?:：)?\\s*([^\\n]{5,100})", "prompt_hint": "公司的注册地址是什么？"}, {"name": "office_address", "type": "address", "description": "办公地址", "required": false, "extraction_method": "hybrid", "regex_pattern": "(?:办公地址|主要经营场所)(?:为)?(?:：)?\\s*([^\\n]{5,100})", "prompt_hint": "公司的办公地址是什么？"}, {"name": "contact_info", "type": "object", "description": "联系方式", "required": false, "extraction_method": "llm", "fields": [{"name": "phone", "type": "phone", "description": "电话号码", "required": false, "extraction_method": "regex", "regex_pattern": "(?:电话|联系电话|咨询电话)(?:：|:)\\s*(\\d{2,4}-?\\d{7,8}(?:-\\d{1,4})?)", "regex_group": 1}, {"name": "fax", "type": "phone", "description": "传真号码", "required": false, "extraction_method": "regex", "regex_pattern": "(?:传真|传真号码)(?:：|:)\\s*(\\d{2,4}-?\\d{7,8}(?:-\\d{1,4})?)", "regex_group": 1}, {"name": "email", "type": "email", "description": "电子邮箱", "required": false, "extraction_method": "regex", "regex_pattern": "(?:邮箱|电子邮箱|电子信箱|E-?mail)(?:：|:)\\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})", "regex_group": 1}, {"name": "website", "type": "url", "description": "公司网站", "required": false, "extraction_method": "regex", "regex_pattern": "(?:网址|公司网址|网站|官网)(?:：|:)\\s*((?:http|https)://[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}(?:/[^\\s]*)?)", "regex_group": 1}]}, {"name": "main_business", "type": "string", "description": "主营业务简介", "required": true, "extraction_method": "llm", "prompt_hint": "请简要描述公司的主营业务，不超过200字"}], "metadata": {"source_type": "IPO", "industry": "general", "version_history": [{"version": "1.0", "date": "2024-04-18", "changes": "初始版本"}]}}