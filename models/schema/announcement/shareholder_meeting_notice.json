{"name": "shareholder_meeting_notice", "version": "1.0", "description": "股东大会通知公告提取模式", "fields": [{"name": "obOrgid0109", "type": "string", "description": "证券代码", "required": true, "extraction_method": "hybrid", "regex_pattern": "(?:证券代码|股票代码)[:：]\\s*(\\d{6})", "regex_group": 1, "prompt_hint": "请提取公告中的证券代码（6位数字）举例：证券代码：600536"}, {"name": "obRectime0109", "type": "string", "description": "公告日期", "required": true, "extraction_method": "hybrid", "regex_pattern": "(?:公告日期|披露日期)[:：]\\s*(\\d{4}[-年]\\d{1,2}[-月]\\d{1,2}[日]?)", "regex_group": 1, "prompt_hint": "请提取公告的发布日期"}, {"name": "obOrgname0109", "type": "string", "description": "机构名称", "required": true, "extraction_method": "hybrid", "regex_pattern": "([\\u4e00-\\u9fa5]{2,}(?:股份)?(?:有限)?(?:公司))", "regex_group": 1, "prompt_hint": "请提取公告中的公司名称"}, {"name": "f002v0109", "type": "string", "description": "股东大会类别编码", "required": false, "extraction_method": "llm", "prompt_hint": "请提取股东大会的类别编码，如果是年度股东大会为'01'，临时股东大会为'02'，如果无法确定请返回空"}, {"name": "f003v0109", "type": "string", "description": "股东大会类别", "required": true, "extraction_method": "hybrid", "regex_pattern": "(年度股东大会|临时股东大会|股东大会)", "regex_group": 1, "prompt_hint": "这是什么类型的股东大会？年度股东大会还是临时股东大会？"}, {"name": "f021v0109", "type": "string", "description": "股东大会名称", "required": true, "extraction_method": "hybrid", "regex_pattern": "(?:关于召开|召开)([\\u4e00-\\u9fa5]+(?:年度|临时)?股东(?:大)?会)", "regex_group": 1, "prompt_hint": "请提取完整的股东大会名称"}, {"name": "f027v0109", "type": "string", "description": "股东大会名称（英文）", "required": false, "extraction_method": "llm", "prompt_hint": "请提取公告中股东大会的英文名称，如果没有则返回空"}, {"name": "f006v0109", "type": "string", "description": "会议召开地点", "required": true, "extraction_method": "hybrid", "regex_pattern": "(?:会议地点|召开地点|现场会议地点)[:：]\\s*([^\\n]{5,100})", "regex_group": 1, "prompt_hint": "请提取股东大会的召开地点"}, {"name": "f001d0109", "type": "string", "description": "会议召开日期", "required": true, "extraction_method": "hybrid", "regex_pattern": "(?:会议召开时间|召开日期|召开时间)[:：]\\s*(\\d{4}[-年]\\d{1,2}[-月]\\d{1,2}[日]?)", "regex_group": 1, "prompt_hint": "请提取股东大会的召开日期"}, {"name": "f004d0109", "type": "string", "description": "A股股东资格登记日期", "required": false, "extraction_method": "hybrid", "regex_pattern": "(?:A股股东资格登记日|股权登记日)[:：]\\s*(\\d{4}[-年]\\d{1,2}[-月]\\d{1,2}[日]?)", "regex_group": 1, "prompt_hint": "请提取A股股东资格登记日期"}, {"name": "f032d0109", "type": "string", "description": "B股股东资格登记日期", "required": false, "extraction_method": "hybrid", "regex_pattern": "(?:B股股东资格登记日|B股最后交易日)[:：]\\s*(\\d{4}[-年]\\d{1,2}[-月]\\d{1,2}[日]?)", "regex_group": 1, "prompt_hint": "请提取B股股东资格登记日期，如果没有则返回空"}, {"name": "f014d0109", "type": "string", "description": "网络投票起始日", "required": false, "extraction_method": "hybrid", "regex_pattern": "(?:网络投票起始日期|网络投票起始时间|网络投票开始日期)[:：]\\s*(\\d{4}[-年]\\d{1,2}[-月]\\d{1,2}[日]?)", "regex_group": 1, "prompt_hint": "请提取网络投票的起始日期"}, {"name": "f016d0109", "type": "string", "description": "网络投票终止日", "required": false, "extraction_method": "hybrid", "regex_pattern": "(?:网络投票终止日期|网络投票结束日期|网络投票结束时间)[:：]\\s*(\\d{4}[-年]\\d{1,2}[-月]\\d{1,2}[日]?)", "regex_group": 1, "prompt_hint": "请提取网络投票的终止日期"}, {"name": "f025d0109", "type": "string", "description": "交易系统投票日期", "required": false, "extraction_method": "hybrid", "regex_pattern": "(?:交易系统投票时间|交易系统投票日期)[:：]\\s*(\\d{4}[-年]\\d{1,2}[-月]\\d{1,2}[日]?)", "regex_group": 1, "prompt_hint": "请提取交易系统投票日期"}, {"name": "f023d0109", "type": "string", "description": "参会登记日期截止日期", "required": false, "extraction_method": "hybrid", "regex_pattern": "(?:参会登记截止日期|登记截止日期|登记截止时间)[:：]\\s*(\\d{4}[-年]\\d{1,2}[-月]\\d{1,2}[日]?)", "regex_group": 1, "prompt_hint": "请提取参会登记的截止日期"}, {"name": "f033d0109", "type": "string", "description": "参会登记起始日", "required": false, "extraction_method": "hybrid", "regex_pattern": "(?:参会登记起始日期|登记起始日期|登记开始时间)[:：]\\s*(\\d{4}[-年]\\d{1,2}[-月]\\d{1,2}[日]?)", "regex_group": 1, "prompt_hint": "请提取参会登记的起始日期"}, {"name": "f037d0109", "type": "string", "description": "异地股东传真截止日", "required": false, "extraction_method": "hybrid", "regex_pattern": "(?:异地股东传真截止日|传真截止日期)[:：]\\s*(\\d{4}[-年]\\d{1,2}[-月]\\d{1,2}[日]?)", "regex_group": 1, "prompt_hint": "请提取异地股东传真的截止日期"}, {"name": "proposals", "type": "array", "description": "议案表", "required": true, "extraction_method": "llm", "prompt_hint": "请提取以下字段，返回List[dict]", "array": true, "fields": [{"name": "f002n0489", "type": "string", "description": "议案序号", "required": false, "extraction_method": "llm", "prompt_hint": "议案的序号是多少？"}, {"name": "f012n0489", "type": "string", "description": "投票序号", "required": false, "extraction_method": "llm", "prompt_hint": "议案的投票序号是多少？"}, {"name": "f011v0489", "type": "string", "description": "累积投票制是否适用", "required": false, "extraction_method": "llm", "prompt_hint": "该议案是否适用累积投票制？"}, {"name": "f008v0489", "type": "string", "description": "应选人数", "required": false, "extraction_method": "llm", "prompt_hint": "该议案应选人数是多少？如果不适用则返回空"}, {"name": "f003v0489", "type": "string", "description": "议案名称", "required": true, "extraction_method": "llm", "prompt_hint": "议案的名称是什么？"}]}], "metadata": {"source_type": "announcement", "category": "shareholder_meeting", "prompt_template": "extraction/extract.txt", "version_history": [{"version": "1.0", "date": "2024-04-18", "changes": "初始版本"}, {"version": "1.1", "date": "2024-05-18", "changes": "添加提示词模板信息"}]}}