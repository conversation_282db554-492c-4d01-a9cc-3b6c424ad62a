[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "xextract"
version = "0.0.1"
description = "A Python package for data extraction"
readme = "README.md"
requires-python = ">=3.8"
license = {text = "MIT"}
authors = [
    {name = "Memect", email = "lih<PERSON><PERSON>@memect.com"},
]
dependencies = [
    "httpx>=0.24.0",
    "PyPDF2>=3.0.0",
    "python-dotenv>=1.0.0",
    "pydantic>=2.0.0",
    "typing-extensions>=4.0.0",
    "deepdiff>=8.4.2",
]

[project.scripts]
xextract = "src.xextract.cli:main"

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
    "ruff>=0.0.1",
]

[tool.black]
line-length = 88
target-version = ["py38", "py39", "py310", "py311"]

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
