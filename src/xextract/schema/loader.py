"""Schema loader for xextract.

This module provides functionality for loading extraction schemas from various sources,
including files and predefined schemas.
"""

import json
import logging
import os
from pathlib import Path
from typing import Dict, List, Any, Optional, Union

logger = logging.getLogger(__name__)


class SchemaLoader:
    """Loader for extraction schemas.
    
    This class provides functionality for loading extraction schemas from various sources,
    including files and predefined schemas.
    """
    
    def __init__(self, schema_dir: Optional[Union[str, Path]] = None):
        """Initialize the schema loader.
        
        Args:
            schema_dir: Directory containing schema files (if None, uses default)
        """
        if schema_dir is None:
            # Use default schema directory
            project_root = Path(__file__).parent.parent.parent.parent
            self.schema_dir = project_root / "models"
        else:
            self.schema_dir = Path(schema_dir)
        
        # Predefined schemas
        self._predefined_schemas = {
            "shareholder_meeting": {
                "name": "shareholder_meeting",
                "description": "Extract key information from shareholder meeting announcements",
                "fields": [
                    {
                        "name": "company_name",
                        "type": "string",
                        "description": "Name of the company holding the meeting",
                        "required": True
                    },
                    {
                        "name": "meeting_type",
                        "type": "string",
                        "description": "Type of the meeting (annual, extraordinary, etc.)",
                        "required": True
                    },
                    {
                        "name": "meeting_date",
                        "type": "string",
                        "description": "Date of the meeting",
                        "required": True
                    },
                    {
                        "name": "meeting_time",
                        "type": "string",
                        "description": "Time of the meeting",
                        "required": True
                    },
                    {
                        "name": "meeting_location",
                        "type": "string",
                        "description": "Location of the meeting",
                        "required": True
                    },
                    {
                        "name": "record_date",
                        "type": "string",
                        "description": "Record date for determining shareholders eligible to attend",
                        "required": False
                    },
                    {
                        "name": "agenda_items",
                        "type": "array",
                        "description": "List of agenda items to be discussed",
                        "required": True,
                        "items": {
                            "type": "string"
                        }
                    }
                ]
            }
        }
    
    def load_schema(self, schema_name: str) -> Optional[Dict[str, Any]]:
        """Load a schema by name.
        
        Args:
            schema_name: Name of the schema to load
            
        Returns:
            Schema dictionary or None if not found
        """
        # Check predefined schemas first
        if schema_name in self._predefined_schemas:
            logger.debug(f"Loading predefined schema: {schema_name}")
            return self._predefined_schemas[schema_name]
        
        # Check schema directory
        schema_path = self._find_schema_file(schema_name)
        if schema_path:
            logger.debug(f"Loading schema from file: {schema_path}")
            return self._load_schema_from_file(schema_path)
        
        logger.warning(f"Schema not found: {schema_name}")
        return None
    
    def _find_schema_file(self, schema_name: str) -> Optional[Path]:
        """Find a schema file by name.
        
        Args:
            schema_name: Name of the schema to find
            
        Returns:
            Path to the schema file or None if not found
        """
        # Check direct path
        schema_path = self.schema_dir / f"{schema_name}.json"
        if schema_path.exists():
            return schema_path
        
        # Check in subdirectories
        for subdir in self.schema_dir.glob("*"):
            if subdir.is_dir():
                schema_path = subdir / f"{schema_name}.json"
                if schema_path.exists():
                    return schema_path
        
        return None
    
    def _load_schema_from_file(self, schema_path: Path) -> Dict[str, Any]:
        """Load a schema from a file.
        
        Args:
            schema_path: Path to the schema file
            
        Returns:
            Schema dictionary
            
        Raises:
            ValueError: If the schema file is invalid
        """
        try:
            with open(schema_path, 'r', encoding='utf-8') as f:
                schema = json.load(f)
            
            # Validate schema
            if not isinstance(schema, dict):
                raise ValueError(f"Invalid schema format: {schema_path}")
            
            if "name" not in schema:
                schema["name"] = schema_path.stem
            
            return schema
        
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing schema file: {schema_path} - {e}")
            raise ValueError(f"Invalid JSON in schema file: {schema_path}")
        
        except Exception as e:
            logger.error(f"Error loading schema file: {schema_path} - {e}")
            raise
