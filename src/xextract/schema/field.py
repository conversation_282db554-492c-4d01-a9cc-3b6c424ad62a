"""Schema field definitions."""
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field as PydanticField


class FieldType(str, Enum):
    """Field types supported by the schema."""
    STRING = "string"  # Text data
    INTEGER = "integer"  # Whole numbers
    FLOAT = "float"  # Decimal numbers
    BOOLEAN = "boolean"  # True/False values
    DATE = "date"  # Date without time
    DATETIME = "datetime"  # Date with time
    OBJECT = "object"  # Nested object structure
    ARRAY = "array"  # List/array of values
    CURRENCY = "currency"  # Monetary values with currency
    PERCENTAGE = "percentage"  # Percentage values
    EMAIL = "email"  # Email addresses
    PHONE = "phone"  # Phone numbers
    URL = "url"  # Web URLs
    ADDRESS = "address"  # Physical addresses
    ID_NUMBER = "id_number"  # Identification numbers


class ExtractionMethod(str, Enum):
    """Extraction methods supported by the schema."""
    REGEX = "regex"  # Extract using regular expressions
    LLM = "llm"  # Extract using large language models
    HYBRID = "hybrid"  # Use both regex and LLM
    RULE = "rule"  # Use rule-based extraction
    OCR = "ocr"  # Use OCR for extraction from images


class SchemaField(BaseModel):
    """Field definition in a schema.
    
    Attributes:
        name: The field name
        type: The field type from FieldType enum
        description: Optional description of the field
        required: Whether the field is required
        array: Whether the field is an array of the specified type
        default: Default value if field is missing
        validation: Dictionary of validation rules
        fields: List of nested fields for object type
        examples: Example values for documentation
        extraction_method: Method to use for extraction (regex, llm, hybrid)
        regex_pattern: Regular expression pattern for extraction (if method is regex or hybrid)
        regex_group: Capture group to use from regex match (default: 0 - entire match)
        prompt_hint: Hint to provide to LLM for extraction (if method is llm or hybrid)
        format: Format specification for the field (e.g., date format)
        pre_processors: List of processors to apply before extraction
        post_processors: List of processors to apply after extraction
    """
    
    name: str
    type: FieldType
    description: Optional[str] = None
    required: bool = False
    array: bool = False
    default: Optional[Any] = None
    validation: Optional[Dict[str, Any]] = None
    fields: Optional[List['SchemaField']] = None
    examples: Optional[List[Any]] = None
    extraction_method: ExtractionMethod = ExtractionMethod.LLM
    regex_pattern: Optional[str] = None
    regex_group: int = 0
    prompt_hint: Optional[str] = None
    format: Optional[str] = None
    pre_processors: Optional[List[str]] = None
    post_processors: Optional[List[str]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert field to dictionary."""
        result = {
            "name": self.name,
            "type": self.type.value,
            "array": self.array,
            "extraction_method": self.extraction_method.value
        }
        
        if self.description:
            result["description"] = self.description
        if self.required:
            result["required"] = self.required
        if self.default is not None:
            result["default"] = self.default
        if self.validation:
            result["validation"] = self.validation
        if self.fields:
            result["fields"] = [f.to_dict() for f in self.fields]
        if self.examples:
            result["examples"] = self.examples
        if self.regex_pattern:
            result["regex_pattern"] = self.regex_pattern
        if self.regex_group != 0:
            result["regex_group"] = self.regex_group
        if self.prompt_hint:
            result["prompt_hint"] = self.prompt_hint
        if self.format:
            result["format"] = self.format
        if self.pre_processors:
            result["pre_processors"] = self.pre_processors
        if self.post_processors:
            result["post_processors"] = self.post_processors
            
        return result
