"""Schema definition for XExtract."""
import re
import os
import glob
import json
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import yaml
from pydantic import BaseModel, Field as PydanticField

from .field import FieldType, ExtractionMethod, SchemaField


class Schema(BaseModel):
    """Schema definition for extraction.

    Attributes:
        name: Schema name
        description: Schema description
        version: Schema version
        fields: Dictionary of fields by name
        metadata: Additional metadata
    """

    name: str
    description: Optional[str] = None
    version: str = "1.0"
    fields: Dict[str, SchemaField]
    metadata: Dict[str, Any] = PydanticField(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert schema to dictionary."""
        return {
            "name": self.name,
            "description": self.description,
            "version": self.version,
            "fields": [field.to_dict() for field in self.fields.values()],
            "metadata": self.metadata
        }

    def to_json(self) -> str:
        """Convert schema to JSON string."""
        return json.dumps(self.to_dict(), indent=2)

    def to_yaml(self) -> str:
        """Convert schema to YAML string."""
        return yaml.dump(self.to_dict(), sort_keys=False)

    def save_json(self, file_path: str) -> None:
        """Save schema to JSON file."""
        with open(file_path, "w") as f:
            f.write(self.to_json())

    def save_yaml(self, file_path: str) -> None:
        """Save schema to YAML file."""
        with open(file_path, "w") as f:
            f.write(self.to_yaml())

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Schema':
        """Create schema from dictionary.

        Args:
            data: Dictionary containing schema definition

        Returns:
            Schema instance

        Raises:
            ValueError: If the dictionary is invalid
        """
        if not isinstance(data, dict):
            raise ValueError("Schema data must be a dictionary")

        # Extract basic schema properties
        name = data.get("name")
        if not name:
            raise ValueError("Schema must have a name")

        description = data.get("description")
        version = data.get("version", "1.0")
        metadata = data.get("metadata", {})

        # Extract fields
        field_list = data.get("fields", [])
        if not isinstance(field_list, list):
            raise ValueError("Schema fields must be a list")

        fields = {}
        for field_data in field_list:
            if not isinstance(field_data, dict):
                continue

            field_name = field_data.get("name")
            if not field_name:
                continue

            # Get field type
            field_type_str = field_data.get("type", "string")
            try:
                field_type = FieldType(field_type_str)
            except ValueError:
                field_type = FieldType.STRING

            # Get extraction method
            extraction_method_str = field_data.get("extraction_method", "llm")
            try:
                extraction_method = ExtractionMethod(extraction_method_str)
            except ValueError:
                extraction_method = ExtractionMethod.LLM

            # Create field
            field = SchemaField(
                name=field_name,
                type=field_type,
                description=field_data.get("description"),
                required=field_data.get("required", False),
                array=field_data.get("array", False),
                default=field_data.get("default"),
                validation=field_data.get("validation"),
                examples=field_data.get("examples"),
                extraction_method=extraction_method,
                regex_pattern=field_data.get("regex_pattern"),
                regex_group=field_data.get("regex_group", 0),
                prompt_hint=field_data.get("prompt_hint"),
                format=field_data.get("format"),
                pre_processors=field_data.get("pre_processors"),
                post_processors=field_data.get("post_processors"),
            )

            # Handle nested fields for object type or array type
            if ("fields" in field_data) and (field_type == FieldType.OBJECT or field.array):
                nested_fields = []
                for nested_field_data in field_data.get("fields", []):
                    if not isinstance(nested_field_data, dict):
                        continue

                    nested_name = nested_field_data.get("name")
                    if not nested_name:
                        continue

                    # Get nested field type
                    nested_type_str = nested_field_data.get("type", "string")
                    try:
                        nested_type = FieldType(nested_type_str)
                    except ValueError:
                        nested_type = FieldType.STRING

                    # Get nested extraction method
                    nested_extraction_method_str = nested_field_data.get("extraction_method", "llm")
                    try:
                        nested_extraction_method = ExtractionMethod(nested_extraction_method_str)
                    except ValueError:
                        nested_extraction_method = ExtractionMethod.LLM

                    # Create nested field
                    nested_field = SchemaField(
                        name=nested_name,
                        type=nested_type,
                        description=nested_field_data.get("description"),
                        required=nested_field_data.get("required", False),
                        array=nested_field_data.get("array", False),
                        default=nested_field_data.get("default"),
                        validation=nested_field_data.get("validation"),
                        examples=nested_field_data.get("examples"),
                        extraction_method=nested_extraction_method,
                        regex_pattern=nested_field_data.get("regex_pattern"),
                        regex_group=nested_field_data.get("regex_group", 0),
                        prompt_hint=nested_field_data.get("prompt_hint"),
                        format=nested_field_data.get("format"),
                        pre_processors=nested_field_data.get("pre_processors"),
                        post_processors=nested_field_data.get("post_processors"),
                    )
                    nested_fields.append(nested_field)

                field.fields = nested_fields

            fields[field_name] = field

        return cls(
            name=name,
            description=description,
            version=version,
            fields=fields,
            metadata=metadata
        )

    @classmethod
    def from_json(cls, json_str: str) -> 'Schema':
        """Create schema from JSON string.

        Args:
            json_str: JSON string containing schema definition

        Returns:
            Schema instance
        """
        data = json.loads(json_str)
        return cls.from_dict(data)

    @classmethod
    def from_yaml(cls, yaml_str: str) -> 'Schema':
        """Create schema from YAML string.

        Args:
            yaml_str: YAML string containing schema definition

        Returns:
            Schema instance
        """
        data = yaml.safe_load(yaml_str)
        return cls.from_dict(data)

    @classmethod
    def from_json_file(cls, file_path: str) -> 'Schema':
        """Create schema from JSON file.

        Args:
            file_path: Path to JSON file

        Returns:
            Schema instance
        """
        with open(file_path, 'r') as f:
            return cls.from_json(f.read())

    @classmethod
    def from_yaml_file(cls, file_path: str) -> 'Schema':
        """Create schema from YAML file.

        Args:
            file_path: Path to YAML file

        Returns:
            Schema instance
        """
        with open(file_path, 'r') as f:
            return cls.from_yaml(f.read())

    @classmethod
    def get_by_name(cls, name: str) -> Optional['Schema']:
        """Get a schema by name.

        This method searches for schema files in the following locations:
        1. models/{project_type} directories (e.g., models/announcement, models/IPO)
        2. models/schema/{project_type} directories (e.g., models/schema/announcement)
        3. src/xextract/models/schemas directory
        4. examples/schemas directory
        5. schemas directory

        Args:
            name: Name of the schema to retrieve

        Returns:
            Schema if found, None otherwise
        """
        # 导入日志模块
        import logging
        logger = logging.getLogger("xextract.schema")
        logger.info(f"Looking for schema: {name}")

        # 检查是否有缓存的 schema
        if hasattr(cls, "_schemas") and name in cls._schemas:
            logger.info(f"Found schema in cache: {name}")
            return cls._schemas[name]

        # 检查是否是文件路径
        path = Path(name)
        if path.exists() and path.is_file():
            logger.info(f"Found schema file at path: {path}")
            ext = path.suffix.lower()
            if ext == ".json":
                return cls.from_json_file(str(path))
            elif ext in [".yaml", ".yml"]:
                return cls.from_yaml_file(str(path))

        # Define possible schema locations
        schema_locations = [
            # Project-specific schema locations in root models directory
            Path("models"),
            # New schema location
            Path("models/schema"),
            # Legacy locations
            Path("models/schemas"),
            Path("src/xextract/models/schemas"),
            Path("examples/schemas"),
            Path("schemas"),
        ]

        # Try to find schema file
        for location in schema_locations:
            if not location.exists():
                logger.debug(f"Schema location does not exist: {location}")
                continue

            logger.info(f"Searching for schema in location: {location}")

            # For the root models directory or models/schema directory, search in all subdirectories
            if (location.name == "models" and location.parent == Path(".")) or (location.name == "schema" and location.parent == Path("models")):
                logger.info(f"Searching in subdirectories of {location}")
                for project_dir in location.iterdir():
                    if not project_dir.is_dir():
                        continue

                    logger.info(f"Searching in project directory: {project_dir}")

                    # Look for JSON and YAML files with matching name
                    for ext in [".json", ".yaml", ".yml"]:
                        # Try exact name match
                        file_path = project_dir / f"{name}{ext}"
                        logger.info(f"Checking for file: {file_path}")
                        if file_path.exists():
                            logger.info(f"Found schema file: {file_path}")
                            if ext == ".json":
                                return cls.from_json_file(str(file_path))
                            else:
                                return cls.from_yaml_file(str(file_path))

                        # Try case-insensitive match
                        for schema_file in project_dir.glob(f"*{ext}"):
                            try:
                                if ext == ".json":
                                    schema = cls.from_json_file(str(schema_file))
                                else:
                                    schema = cls.from_yaml_file(str(schema_file))

                                if schema.name.lower() == name.lower():
                                    return schema
                            except Exception:
                                continue
            else:
                # Look for JSON and YAML files with matching name
                for ext in [".json", ".yaml", ".yml"]:
                    # Try exact name match
                    file_path = location / f"{name}{ext}"
                    if file_path.exists():
                        if ext == ".json":
                            return cls.from_json_file(str(file_path))
                        else:
                            return cls.from_yaml_file(str(file_path))

                    # Try case-insensitive match
                    for schema_file in location.glob(f"*{ext}"):
                        try:
                            if ext == ".json":
                                schema = cls.from_json_file(str(schema_file))
                            else:
                                schema = cls.from_yaml_file(str(schema_file))

                            if schema.name.lower() == name.lower():
                                return schema
                        except Exception as e:
                            logger.debug(f"Error loading schema file: {e}")
                            continue

        logger.warning(f"Schema not found: {name}")
        return None
