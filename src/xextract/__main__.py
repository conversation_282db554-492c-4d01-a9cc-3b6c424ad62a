"""Main entry point for XExtract."""
import argparse
import sys

from .cli import document_tasks_main, task_manager_main, docparser_main


def main() -> None:
    """Main entry point for the command-line interface."""
    parser = argparse.ArgumentParser(description="XExtract - Document Extraction Engine")
    subparsers = parser.add_subparsers(dest="command", help="Command to run")

    # Document processing command
    doc_parser = subparsers.add_parser("document", help="Document processing")
    doc_parser.set_defaults(func=document_tasks_main)

    # Task management command
    task_parser = subparsers.add_parser("task", help="Task management")
    task_parser.set_defaults(func=task_manager_main)

    # Document parser service command
    docparser_parser = subparsers.add_parser("docparser", help="Document parser service")
    docparser_parser.set_defaults(func=docparser_main)

    # Parse arguments
    args = parser.parse_args()

    # Run the appropriate command
    if hasattr(args, "func"):
        sys.argv = [sys.argv[0]] + sys.argv[2:]
        args.func()
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
