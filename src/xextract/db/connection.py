"""Database connection management."""
import logging
from typing import Optional, Dict, Any, Union
from contextlib import contextmanager
from pathlib import Path

from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.engine import Engine
from sqlalchemy.exc import SQLAlchemyError

from ...config import settings
from ...config.models import DatabaseType
from .models import Base

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Database connection manager."""

    def __init__(self):
        """Initialize database manager."""
        self.engine: Optional[Engine] = None
        self.SessionLocal: Optional[sessionmaker] = None

    def init_db(self, db_url: Optional[str] = None, **engine_kwargs: Any):
        """Initialize database connection.

        Args:
            db_url: Database URL. If not provided, uses configuration.
            **engine_kwargs: Additional arguments to pass to create_engine.
        """
        if not db_url:
            db_url = self._get_database_url()

        try:
            # Create engine with appropriate settings
            if db_url.startswith('sqlite'):
                self.engine = create_engine(
                    db_url,
                    connect_args={"check_same_thread": False},
                    echo=settings.database.echo,
                    **engine_kwargs
                )

                # Enable foreign key support for SQLite
                @event.listens_for(self.engine, "connect")
                def set_sqlite_pragma(dbapi_connection, connection_record):
                    cursor = dbapi_connection.cursor()
                    cursor.execute("PRAGMA foreign_keys=ON")
                    cursor.close()

            elif db_url.startswith('mysql'):
                self.engine = create_engine(
                    db_url,
                    pool_size=settings.database.pool_size,
                    max_overflow=settings.database.max_overflow,
                    pool_timeout=settings.database.pool_timeout,
                    echo=settings.database.echo,
                    **engine_kwargs
                )
            else:
                raise ValueError(f"Unsupported database type in URL: {db_url}")

            # Create session factory
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )

            # Create tables
            Base.metadata.create_all(bind=self.engine)
            logger.info(f"Database initialized with {db_url}")

        except SQLAlchemyError as e:
            logger.error(f"Failed to initialize database: {str(e)}")
            raise

    def _get_database_url(self) -> str:
        """Get database URL from configuration."""
        db_config = settings.database

        if db_config.type == DatabaseType.SQLITE:
            # Ensure the directory exists
            db_path = db_config.path
            db_path.parent.mkdir(parents=True, exist_ok=True)
            return f"sqlite:///{db_path}"

        elif db_config.type == DatabaseType.MYSQL:
            # Format: mysql+pymysql://username:password@host:port/database
            password_str = f":{db_config.password}" if db_config.password else ""
            return (
                f"mysql+pymysql://{db_config.user}{password_str}@"
                f"{db_config.host}:{db_config.port}/{db_config.name}"
            )

        else:
            raise ValueError(f"Unsupported database type: {db_config.type}")

    @contextmanager
    def get_session(self) -> Session:
        """Get database session.

        Yields:
            Session: Database session

        Example:
            ```python
            with db.get_session() as session:
                prompt = session.query(Prompt).filter_by(id=1).first()
            ```
        """
        if not self.SessionLocal:
            raise RuntimeError("Database not initialized")

        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()

# Global database manager instance
db = DatabaseManager()
