"""Time utilities for XExtract.

This module provides utility functions for time-related operations.
"""

import datetime
from typing import Union, <PERSON><PERSON>


def format_uptime(seconds: float) -> str:
    """Format uptime in a human-readable format.
    
    Args:
        seconds: The number of seconds to format.
        
    Returns:
        A human-readable string representing the uptime.
        Format depends on the duration:
        - Less than a minute: "X seconds"
        - Less than an hour: "X minutes, Y seconds"
        - Less than a day: "X hours, Y minutes"
        - Days or more: "X days, Y hours"
    """
    if seconds < 60:
        # Less than a minute
        return f"{int(seconds)} seconds"
    elif seconds < 3600:
        # Less than an hour
        minutes = int(seconds / 60)
        seconds_remainder = int(seconds % 60)
        return f"{minutes} minutes, {seconds_remainder} seconds"
    elif seconds < 86400:
        # Less than a day
        hours = int(seconds / 3600)
        minutes = int((seconds % 3600) / 60)
        return f"{hours} hours, {minutes} minutes"
    else:
        # Days or more
        days = int(seconds / 86400)
        hours = int((seconds % 86400) / 3600)
        return f"{days} days, {hours} hours"


def get_uptime_info(start_time: Union[str, datetime.datetime]) -> Tuple[float, str]:
    """Calculate uptime in seconds and format it as a human-readable string.
    
    Args:
        start_time: The start time as an ISO-formatted string or datetime object.
        
    Returns:
        A tuple containing:
        - uptime_seconds: The number of seconds since start_time.
        - uptime_display: A human-readable string representing the uptime.
    """
    # Convert string to datetime if needed
    if isinstance(start_time, str):
        start_time = datetime.datetime.fromisoformat(start_time)
    
    # Calculate uptime in seconds
    uptime_seconds = (datetime.datetime.now() - start_time).total_seconds()
    
    # Format uptime
    uptime_display = format_uptime(uptime_seconds)
    
    return uptime_seconds, uptime_display
