"""JSON extraction utilities.

This module provides utilities for extracting JSON from text, including
handling various formats and edge cases.
"""

import json
import re
import logging
from typing import Any, Dict, List, Optional, Union, Tuple

logger = logging.getLogger(__name__)


class JSONExtractor:
    """Utility class for extracting JSO<PERSON> from text."""

    @staticmethod
    def extract_json(text: str) -> Optional[Dict[str, Any]]:
        """Extract JSON from text.

        This method tries various strategies to extract JSON from text:
        1. Parse the entire text as JSON
        2. Extract JSON from markdown code blocks
        3. Extract JSON from backticks
        4. Extract JSON from the text using regex

        Args:
            text: Text that may contain JSON

        Returns:
            Extracted JSON as a dictionary, or None if no JSON could be extracted
        """
        # Strategy 1: Parse the entire text as JSON
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            pass

        # Strategy 2: Extract JSON from markdown code blocks
        json_data = JSONExtractor._extract_from_markdown(text)
        if json_data:
            return json_data

        # Strategy 3: Extract JSON from backticks
        json_data = JSONExtractor._extract_from_backticks(text)
        if json_data:
            return json_data

        # Strategy 4: Extract JSON using regex
        json_data = JSONExtractor._extract_using_regex(text)
        if json_data:
            return json_data

        # Failed to extract JSON
        return None

    @staticmethod
    def _extract_from_markdown(text: str) -> Optional[Dict[str, Any]]:
        """Extract JSON from markdown code blocks.

        Args:
            text: Text that may contain markdown code blocks

        Returns:
            Extracted JSON as a dictionary, or None if no JSON could be extracted
        """
        # Try to extract from ```json blocks
        if "```json" in text and "```" in text.split("```json")[1]:
            try:
                json_text = text.split("```json")[1].split("```")[0].strip()
                return json.loads(json_text)
            except (IndexError, json.JSONDecodeError):
                pass

        # Try to extract from ``` blocks (without language specifier)
        if "```" in text:
            try:
                blocks = text.split("```")
                for i in range(1, len(blocks), 2):
                    try:
                        return json.loads(blocks[i].strip())
                    except json.JSONDecodeError:
                        continue
            except (IndexError, json.JSONDecodeError):
                pass

        return None

    @staticmethod
    def _extract_from_backticks(text: str) -> Optional[Dict[str, Any]]:
        """Extract JSON from backticks.

        Args:
            text: Text that may contain backticks

        Returns:
            Extracted JSON as a dictionary, or None if no JSON could be extracted
        """
        if "`" in text:
            try:
                parts = text.split("`")
                for i in range(1, len(parts), 2):
                    try:
                        return json.loads(parts[i].strip())
                    except json.JSONDecodeError:
                        continue
            except (IndexError, json.JSONDecodeError):
                pass

        return None

    @staticmethod
    def _extract_using_regex(text: str) -> Optional[Dict[str, Any]]:
        """Extract JSON using regex.

        Args:
            text: Text that may contain JSON

        Returns:
            Extracted JSON as a dictionary, or None if no JSON could be extracted
        """
        # Try to find JSON objects using regex
        try:
            # Look for JSON objects
            json_pattern = r'(\{(?:[^{}]|(?:\{(?:[^{}]|(?:\{[^{}]*\}))*\}))*\})'
            matches = re.findall(json_pattern, text)

            for match in matches:
                try:
                    # Try to handle escaped quotes and special characters
                    # First, try direct loading
                    try:
                        return json.loads(match)
                    except json.JSONDecodeError:
                        # If that fails, try to handle escaped quotes
                        # Replace escaped quotes with a temporary marker
                        temp_match = match.replace("\\\"", "__ESCAPED_QUOTE__")
                        # Load the JSON
                        result = json.loads(temp_match)
                        # Restore the escaped quotes in the result
                        if isinstance(result, dict):
                            for key, value in result.items():
                                if isinstance(value, str):
                                    result[key] = value.replace("__ESCAPED_QUOTE__", "\"")
                        return result
                except json.JSONDecodeError:
                    continue

            # Look for JSON arrays
            array_pattern = r'(\[(?:[^\[\]]|(?:\[(?:[^\[\]]|(?:\[[^\[\]]*\]))*\]))*\])'
            matches = re.findall(array_pattern, text)

            for match in matches:
                try:
                    return json.loads(match)
                except json.JSONDecodeError:
                    continue

            # Try to extract JSON from the text (look for { and })
            start = text.find("{")
            end = text.rfind("}")
            if start >= 0 and end > start:
                json_text = text[start:end+1]
                try:
                    return json.loads(json_text)
                except json.JSONDecodeError:
                    # Try with raw string to handle escape sequences
                    try:
                        return json.loads(json_text.encode('utf-8').decode('unicode_escape'))
                    except (json.JSONDecodeError, UnicodeDecodeError):
                        pass
        except Exception as e:
            logger.debug(f"Error extracting JSON using regex: {e}")

        return None

    @staticmethod
    def extract_json_array(text: str) -> Optional[List[Any]]:
        """Extract JSON array from text.

        Args:
            text: Text that may contain a JSON array

        Returns:
            Extracted JSON array, or None if no JSON array could be extracted
        """
        # Strategy 1: Parse the entire text as JSON array
        try:
            result = json.loads(text)
            if isinstance(result, list):
                return result
        except json.JSONDecodeError:
            pass

        # Strategy 2: Extract JSON array from markdown code blocks
        if "```json" in text and "```" in text.split("```json")[1]:
            try:
                json_text = text.split("```json")[1].split("```")[0].strip()
                result = json.loads(json_text)
                if isinstance(result, list):
                    return result
            except (IndexError, json.JSONDecodeError):
                pass

        # Strategy 3: Extract JSON array from backticks
        if "`" in text:
            try:
                parts = text.split("`")
                for i in range(1, len(parts), 2):
                    try:
                        result = json.loads(parts[i].strip())
                        if isinstance(result, list):
                            return result
                    except json.JSONDecodeError:
                        continue
            except (IndexError, json.JSONDecodeError):
                pass

        # Strategy 4: Extract JSON array using regex
        try:
            # Look for JSON arrays
            array_pattern = r'(\[(?:[^\[\]]|(?:\[(?:[^\[\]]|(?:\[[^\[\]]*\]))*\]))*\])'
            matches = re.findall(array_pattern, text)

            for match in matches:
                try:
                    result = json.loads(match)
                    if isinstance(result, list):
                        return result
                except json.JSONDecodeError:
                    continue

            # Try to extract JSON array from the text (look for [ and ])
            start = text.find("[")
            end = text.rfind("]")
            if start >= 0 and end > start:
                json_text = text[start:end+1]
                result = json.loads(json_text)
                if isinstance(result, list):
                    return result
        except Exception as e:
            logger.debug(f"Error extracting JSON array using regex: {e}")

        return None

    @staticmethod
    def extract_with_fallback(text: str) -> Tuple[Any, bool]:
        """Extract JSON with fallback to text.

        Args:
            text: Text that may contain JSON

        Returns:
            Tuple of (extracted_data, is_json) where:
            - extracted_data is the extracted JSON or the original text
            - is_json is True if JSON was successfully extracted, False otherwise
        """
        # Try to extract JSON
        json_data = JSONExtractor.extract_json(text)
        if json_data is not None:
            return json_data, True

        # Try to extract JSON array
        json_array = JSONExtractor.extract_json_array(text)
        if json_array is not None:
            return json_array, True

        # Fallback to original text
        return text, False
