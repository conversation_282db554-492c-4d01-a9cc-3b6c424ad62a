"""File utility functions for XExtract."""

import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Union


def is_url(source: str) -> bool:
    """
    Check if the given source is a URL.

    Args:
        source: Source string to check

    Returns:
        True if the source is a URL, False otherwise
    """
    return source.startswith(("http://", "https://"))


def read_file(file_path: str) -> str:
    """
    Read the contents of a file.

    Args:
        file_path: Path to the file

    Returns:
        Contents of the file as a string

    Raises:
        FileNotFoundError: If the file does not exist
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"File not found: {file_path}")

    with open(file_path, "r", encoding="utf-8") as f:
        return f.read()


def load_json(*, path=None, data=None):
    if path:
        path = Path(path)
        data = path.read_text(encoding="utf-8")
    elif isinstance(data, (bytes, bytearray)):
        data = data.decode("utf-8")
    elif isinstance(data, str):
        # str
        pass
    else:
        raise ValueError("path和data必须设置")

    try:
        import json

        return json.loads(data)
    except ImportError:
        return json.loads(data)
