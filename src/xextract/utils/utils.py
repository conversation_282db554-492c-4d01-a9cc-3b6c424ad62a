#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Author: lixiaobing
Date: 2021/02/09
Desc:
"""
import json
import time
from collections import namedtuple
from functools import wraps
from inspect import signature
from pathlib import Path


def load_json(*,path=None, data=None):
    if path:
        path = Path(path)
        data = path.read_text(encoding='utf-8')
    elif isinstance(data,(bytes,bytearray)):
        data = data.decode('utf-8')
    elif isinstance(data,str):
        #str
        pass
    else:
        raise ValueError('path和data必须设置')

    try:
        import json
        return json.loads(data)
    except ImportError:
        return json.loads(data)

def read_json(json_filename):
    """
    读取json
    """

    with open(json_filename, 'r', encoding='utf-8') as f:
        content = json.loads(f.read())
    return content


def timer(func):
    """
    记录执行用时装饰器
    """

    @wraps(func)
    def wrapper(*args, **kwargs):

        t1 = time.time()
        result = func(*args, **kwargs)
        print(
            '{}:{}() cast: {}'.format(
                func.__module__,
                func.__name__,
                time.time() - t1
            )
        )
        return result

    return wrapper


def str_join(separator, iterable):
    """
    拼接字符串
    """

    sequence = list(iter(iterable))
    if not sequence:
        return ''

    ret = sequence[0]
    for s in sequence[1:]:
        ret += separator + s

    true_ret = separator.join([str(s) for s in sequence])
    if ret != true_ret:
        raise Exception(f'{ret} and {true_ret} is different')

    return ret


def load_package(name: str, filename: str, obj_name: str = ''):
    """
    加载自定义代码
    """
    import importlib.util
    import sys
    spec = importlib.util.spec_from_file_location(name, filename)
    if spec:
        module = importlib.util.module_from_spec(spec)
        sys.modules[spec.name] = module
        spec.loader.exec_module(module)
    else:
        raise Exception('加载package {} 路径 {} 无效'.format(name, filename))
    if obj_name:
        return object.__getattribute__(module, obj_name)
    else:
        return module


def serialize(func):
    """
    pkl 序列化
    """

    @wraps(func)
    def wrapper(*args, **kwargs):

        bind_args = signature(func).bind(*args, **kwargs)
        bind_args.apply_defaults()
        parameters = dict(bind_args.arguments)

        assert 'self' in parameters, f'{func}函数没有self参数'
        self = parameters['self']

        result = func(*args, **kwargs)
        result.update({
            '__class__': self.__class__.__name__,
            '__module__': self.__module__,
        })
        return result

    return wrapper


class Digitizer:
    """
    数字转换
    """

    CHN_NUM_CHAR_COUNT = 10
    CHN_CHAR_LENGTH = 1
    chnNumchar = ('零', '一', '二', '三', '四', '五', '六', '七', '八', '九')
    chnUnitsection = ('', '万', '亿', '万亿')
    chnUnitechar = ('', '十', '百', '千')
    CHN_NAME_VALUE = namedtuple(
        'CHN_NAME_VALUE', [
            'name',    # 中文权位名称
            'value',   # 10的倍数值
            'secUnit'  # 是否是节权位
        ]
    )
    chnValuePair = [
        CHN_NAME_VALUE('十', 10, False),
        CHN_NAME_VALUE('百', 100, False),
        CHN_NAME_VALUE('千', 1000, False),
        CHN_NAME_VALUE('万', 10000, True),
        CHN_NAME_VALUE('亿', 100000000, True)
    ]

    @classmethod
    def number2chinese(cls, num):
        unitpos = 0
        strint = ''
        chnstr = ''
        needzero = False

        if num == 0:
            return cls.chnNumchar[0]

        while num > 0:
            section = int(num % 10000)
            if needzero:
                chnstr = cls.chnNumchar[0] + chnstr
            strint = cls.section2chinese(section)
            # 是否需要节权位
            if section != 0:
                strint += cls.chnUnitsection[unitpos]
                # print(unitpos)
                # print(strint + ' ' + str(unitpos))
            else:
                strint += cls.chnUnitsection[0]
            chnstr = strint + chnstr
            # 千位是零，下一个section补零
            needzero = (section < 1000) and (section > 0)
            num = int(num / 10000)
            unitpos += 1
        return chnstr

    @classmethod
    def section2chinese(cls, section):
        strint = ''
        chnstr = ''
        unitpos = 0
        zero = True
        while section > 0:
            v = int(section % 10)
            if v == 0:
                if section == 0 or not zero:
                    zero = True
                    chnstr = cls.chnNumchar[v] + chnstr
            else:
                zero = False
                # 15 输出十五
                if v == 1 and chnstr:
                    strint = ''
                else:
                    strint = cls.chnNumchar[v]
                strint += cls.chnUnitechar[unitpos]
                chnstr = strint + chnstr
            unitpos += 1
            section = int(section / 10)
        return chnstr

    @classmethod
    def chinese2value(cls, chnstr):
        for val in range(len(cls.chnNumchar)):
            if chnstr.find(cls.chnNumchar[val]) == 0:
                return val
        return -1

    @classmethod
    def chinese2unit(cls, chnstr):
        for unit in range(len(cls.chnValuePair)):
            if chnstr.find(cls.chnValuePair[unit].name) == 0:
                secUint = cls.chnValuePair[unit].secUnit
                return cls.chnValuePair[unit].value, secUint
        return -1

    @classmethod
    def chinese2number(cls, chnstring):
        rtn = 0
        section = 0
        number = 1
        secUnit = False
        pos = 0

        while pos < len(chnstring):
            num = cls.chinese2value(chnstring[pos: pos + cls.CHN_CHAR_LENGTH])
            if num >= 0:
                number = num
                pos += cls.CHN_CHAR_LENGTH
                if pos >= len(chnstring):
                    section += number
                    rtn += section
                    break
            else:
                unit, secUnit = cls.chinese2unit(chnstring[pos: pos + cls.CHN_CHAR_LENGTH])
                if secUnit:
                    section = (section + number) * unit
                    rtn += section
                    section = 0
                else:
                    section += (number * unit)
                number = 0
                pos += cls.CHN_CHAR_LENGTH
                if pos >= len(chnstring):
                    rtn += section
                    break
        return rtn
