"""Logging configuration for the XExtract application."""
import os
import logging
import logging.handlers
from pathlib import Path
from datetime import datetime

# 创建日志目录
LOG_DIR = Path(__file__).parent.parent.parent.parent / "logs"
LOG_DIR.mkdir(exist_ok=True)

# 日志文件名格式：xextract_YYYY-MM-DD.log
log_filename = f"xextract_{datetime.now().strftime('%Y-%m-%d')}.log"
log_filepath = LOG_DIR / log_filename

# 创建日志格式
DETAILED_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s"
SIMPLE_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

def configure_logging(level=logging.INFO):
    """Configure logging for the application.
    
    Args:
        level: The logging level to use
    """
    # 创建根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_formatter = logging.Formatter(SIMPLE_FORMAT)
    console_handler.setFormatter(console_formatter)
    
    # 创建文件处理器
    file_handler = logging.handlers.RotatingFileHandler(
        log_filepath, 
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(level)
    file_formatter = logging.Formatter(DETAILED_FORMAT)
    file_handler.setFormatter(file_formatter)
    
    # 添加处理器到根日志记录器
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    
    # 设置第三方库的日志级别
    logging.getLogger("uvicorn").setLevel(logging.WARNING)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    
    # 返回根日志记录器
    return root_logger

def get_logger(name):
    """Get a logger with the given name.
    
    Args:
        name: The name of the logger
        
    Returns:
        A logger instance
    """
    return logging.getLogger(name)
