"""Logging decorators for XExtract.

This module provides decorators for method and function logging, making it easy
to add logging to any function or method with minimal code changes.
"""

import time
import functools
import inspect
from typing import Optional, Callable, Any, Dict, Type, Union
from contextlib import contextmanager

from .core import get_logger

def log_method(
    level: str = "INFO",
    entry_msg: Optional[str] = None,
    exit_msg: Optional[str] = None,
    log_args: bool = True,
    log_result: bool = True,
    exclude_args: Optional[list] = None,
    logger_name: Optional[str] = None
):
    """Decorator for logging method entry and exit with parameters and return value.
    
    Args:
        level: Logging level (INFO, DEBUG, etc.)
        entry_msg: Custom message for method entry
        exit_msg: Custom message for method exit
        log_args: Whether to log method arguments
        log_result: Whether to log return value
        exclude_args: List of argument names to exclude from logging
        logger_name: Custom logger name
    
    Returns:
        Callable: Decorated function
    """
    def decorator(func: Callable) -> Callable:
        # Get the function's module logger if no custom logger specified
        logger = get_logger(logger_name or func.__module__)
        log_level = getattr(logger, level.lower())
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Get function signature
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
            
            # Prepare context for logging
            ctx = {
                'function': func.__name__,
                'module': func.__module__
            }
            
            # Add arguments to context if requested
            if log_args:
                arg_dict = dict(bound_args.arguments)
                if exclude_args:
                    for arg in exclude_args:
                        arg_dict.pop(arg, None)
                ctx['args'] = arg_dict
            
            # Log method entry
            entry_message = entry_msg or f"Entering {func.__name__}"
            log_level(entry_message, extra={'extra_fields': ctx})
            
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                
                # Add execution time and result to context
                ctx['duration'] = time.time() - start_time
                if log_result:
                    ctx['result'] = result
                
                # Log method exit
                exit_message = exit_msg or f"Exiting {func.__name__}"
                log_level(exit_message, extra={'extra_fields': ctx})
                
                return result
            except Exception as e:
                # Log error with context
                ctx['duration'] = time.time() - start_time
                ctx['error'] = {
                    'type': type(e).__name__,
                    'message': str(e)
                }
                logger.error(f"Error in {func.__name__}", extra={'extra_fields': ctx}, exc_info=True)
                raise
        
        return wrapper
    return decorator

def log_error(
    error_msg: Optional[str] = None,
    log_args: bool = True,
    exclude_args: Optional[list] = None,
    logger_name: Optional[str] = None,
    reraise: bool = True
):
    """Decorator for logging exceptions.
    
    Args:
        error_msg: Custom error message
        log_args: Whether to log method arguments
        exclude_args: List of argument names to exclude from logging
        logger_name: Custom logger name
        reraise: Whether to re-raise the caught exception
    
    Returns:
        Callable: Decorated function
    """
    def decorator(func: Callable) -> Callable:
        logger = get_logger(logger_name or func.__module__)
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Prepare context for error logging
                ctx = {
                    'function': func.__name__,
                    'module': func.__module__,
                    'error': {
                        'type': type(e).__name__,
                        'message': str(e)
                    }
                }
                
                # Add arguments to context if requested
                if log_args:
                    sig = inspect.signature(func)
                    bound_args = sig.bind(*args, **kwargs)
                    bound_args.apply_defaults()
                    arg_dict = dict(bound_args.arguments)
                    if exclude_args:
                        for arg in exclude_args:
                            arg_dict.pop(arg, None)
                    ctx['args'] = arg_dict
                
                # Log the error
                message = error_msg or f"Error in {func.__name__}: {str(e)}"
                logger.error(message, extra={'extra_fields': ctx}, exc_info=True)
                
                if reraise:
                    raise
        
        return wrapper
    return decorator

@contextmanager
def log_operation(
    operation: str,
    logger_name: Optional[str] = None,
    level: str = "INFO",
    **context
):
    """Context manager for logging operations with timing and context.
    
    Args:
        operation: Name of the operation being performed
        logger_name: Custom logger name
        level: Logging level
        **context: Additional context to include in logs
    
    Example:
        with log_operation("data_processing", user_id="123"):
            process_data()
    """
    logger = get_logger(logger_name)
    log_level = getattr(logger, level.lower())
    
    ctx = {
        'operation': operation,
        **context
    }
    
    start_time = time.time()
    log_level(f"Starting {operation}", extra={'extra_fields': ctx})
    
    try:
        yield
        ctx['duration'] = time.time() - start_time
        log_level(f"Completed {operation}", extra={'extra_fields': ctx})
    except Exception as e:
        ctx.update({
            'duration': time.time() - start_time,
            'error': {
                'type': type(e).__name__,
                'message': str(e)
            }
        })
        logger.error(f"Error in {operation}", extra={'extra_fields': ctx}, exc_info=True)
        raise 