"""Core logging functionality for XExtract.

This module provides the core logging functionality, including logger setup
and configuration management.
"""

import logging
import logging.handlers
import json
import sys
from pathlib import Path
from typing import Optional, Dict, Any, Union
from functools import lru_cache

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入配置
try:
    # 确保项目根目录在Python路径中
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent.parent.parent.parent.absolute()
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))

    from config.config import config
except ImportError:
    # 如果无法导入config，使用默认值
    config = type('Config', (), {
        'LOG_LEVEL': 'INFO',
        'LOG_FORMAT': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'LOG_FILE': 'logs/xextract.log'
    })

class JsonFormatter(logging.Formatter):
    """JSON formatter for structured logging."""

    def __init__(self, **kwargs):
        """Initialize formatter with optional fields to include in all records."""
        super().__init__()
        self.additional_fields = kwargs

    def format(self, record: logging.LogRecord) -> str:
        """Format the log record as a JSON string."""
        data = {
            'timestamp': self.formatTime(record),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
        }

        # Add error information if present
        if record.exc_info:
            data['error'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': self.formatException(record.exc_info)
            }

        # Add extra fields from record
        if hasattr(record, 'extra_fields'):
            data.update(record.extra_fields)

        # Add configured additional fields
        data.update(self.additional_fields)

        return json.dumps(data)

class ContextualLogger(logging.Logger):
    """Logger subclass that supports context and structured logging."""

    def _log_with_context(self, level: int, msg: str, extra_fields: Optional[Dict[str, Any]] = None, **kwargs):
        """Log a message with additional context fields."""
        if extra_fields:
            kwargs.setdefault('extra', {}).setdefault('extra_fields', {}).update(extra_fields)
        super().log(level, msg, **kwargs)

    def info_with_context(self, msg: str, **context):
        """Log an info message with context."""
        self._log_with_context(logging.INFO, msg, context)

    def error_with_context(self, msg: str, **context):
        """Log an error message with context."""
        self._log_with_context(logging.ERROR, msg, context)

    def debug_with_context(self, msg: str, **context):
        """Log a debug message with context."""
        self._log_with_context(logging.DEBUG, msg, context)

    def warning_with_context(self, msg: str, **context):
        """Log a warning message with context."""
        self._log_with_context(logging.WARNING, msg, context)

def setup_logger(name: str) -> ContextualLogger:
    """Set up a new logger instance with the configured settings.

    Args:
        name: The name of the logger

    Returns:
        ContextualLogger: Configured logger instance
    """
    # Register our custom logger class
    logging.setLoggerClass(ContextualLogger)

    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(settings.logging.level)

    # Remove existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Create handlers
    handlers = []

    # Console handler
    console_handler = logging.StreamHandler()
    # 使用默认格式
    log_format = config.LOG_FORMAT
    if log_format == "json":
        formatter = JsonFormatter(
            app_name=config.APP_NAME,
            version=config.VERSION
        )
    else:
        formatter = logging.Formatter(log_format)
    console_handler.setFormatter(formatter)
    handlers.append(console_handler)

    # File handler if configured
    log_file = config.LOG_FILE
    if log_file:
        file_path = Path(log_file)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        file_handler = logging.handlers.RotatingFileHandler(
            file_path,
            maxBytes=10 * 1024 * 1024,  # 10MB default
            backupCount=5  # 5 backups default
        )
        file_handler.setFormatter(formatter)
        handlers.append(file_handler)

    # Add all handlers to logger
    for handler in handlers:
        logger.addHandler(handler)

    return logger

@lru_cache()
def get_logger(name: Optional[str] = None) -> ContextualLogger:
    """Get a logger instance with caching.

    Args:
        name: Logger name, defaults to the module name

    Returns:
        ContextualLogger: Configured logger instance
    """
    if name is None:
        import inspect
        frame = inspect.currentframe()
        if frame is None:
            name = 'xextract'
        else:
            # Get the module name of the caller
            caller_frame = frame.f_back
            if caller_frame is not None:
                name = caller_frame.f_globals.get('__name__', 'xextract')
            else:
                name = 'xextract'

    return setup_logger(name)