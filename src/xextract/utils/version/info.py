"""Version information core functionality.

This module provides functions to retrieve version and build information
from various sources including git, package metadata, and configuration.
"""

import os
import subprocess
import sys
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入配置
try:
    # 确保项目根目录在Python路径中
    import sys
    from pathlib import Path
    project_root = Path(__file__).parent.parent.parent.parent.parent.absolute()
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))

    from config.config import config
except ImportError:
    # 如果无法导入config，使用默认值
    config = type('Config', (), {'VERSION': '0.1.0'})

def get_git_info() -> Dict[str, str]:
    """Get git repository information.

    Returns:
        Dict with git information including:
        - commit_hash: Latest commit hash
        - commit_date: Date of the latest commit
        - branch: Current branch name
        - dirty: Whether there are uncommitted changes
    """
    try:
        # Get the project root directory (where .git is located)
        root_dir = subprocess.check_output(
            ['git', 'rev-parse', '--show-toplevel'],
            stderr=subprocess.DEVNULL,
            text=True
        ).strip()

        # Change to root directory temporarily
        current_dir = os.getcwd()
        os.chdir(root_dir)

        try:
            # Get commit hash
            commit_hash = subprocess.check_output(
                ['git', 'rev-parse', 'HEAD'],
                stderr=subprocess.DEVNULL,
                text=True
            ).strip()

            # Get commit date
            commit_date = subprocess.check_output(
                ['git', 'log', '-1', '--format=%cd', '--date=iso'],
                stderr=subprocess.DEVNULL,
                text=True
            ).strip()

            # Get branch name
            branch = subprocess.check_output(
                ['git', 'rev-parse', '--abbrev-ref', 'HEAD'],
                stderr=subprocess.DEVNULL,
                text=True
            ).strip()

            # Check if there are uncommitted changes
            status = subprocess.check_output(
                ['git', 'status', '--porcelain'],
                stderr=subprocess.DEVNULL,
                text=True
            ).strip()
            dirty = bool(status)

            return {
                'commit_hash': commit_hash,
                'commit_date': commit_date,
                'branch': branch,
                'dirty': dirty
            }

        finally:
            # Restore original directory
            os.chdir(current_dir)

    except (subprocess.SubprocessError, FileNotFoundError):
        return {
            'commit_hash': 'unknown',
            'commit_date': 'unknown',
            'branch': 'unknown',
            'dirty': False
        }

def get_package_info() -> Dict[str, str]:
    """Get package information from setup.py or pyproject.toml.

    Returns:
        Dict with package information including:
        - version: Package version
        - name: Package name
        - author: Package author
        - license: Package license
    """
    try:
        import importlib.metadata

        # Try to get metadata from package
        metadata = importlib.metadata.metadata('xextract')
        return {
            'version': metadata.get('Version', 'unknown'),
            'name': metadata.get('Name', 'xextract'),
            'author': metadata.get('Author', 'unknown'),
            'license': metadata.get('License', 'unknown')
        }
    except (ImportError, importlib.metadata.PackageNotFoundError):
        # Fallback to configuration version
        return {
            'version': config.VERSION,
            'name': config.APP_NAME,
            'author': 'unknown',
            'license': 'unknown'
        }

def get_build_info() -> Dict[str, Any]:
    """Get build-related information.

    Returns:
        Dict with build information including:
        - build_date: Date when the package was built
        - python_version: Python version used
        - platform: Operating system platform
    """
    import sys
    import platform

    # Try to get build date from package metadata
    try:
        build_date = datetime.fromtimestamp(
            Path(__file__).parent.parent.parent.stat().st_mtime
        ).isoformat()
    except (OSError, ValueError):
        build_date = 'unknown'

    return {
        'build_date': build_date,
        'python_version': sys.version,
        'platform': platform.platform()
    }

def get_version_info() -> Dict[str, Any]:
    """Get comprehensive version information.

    Returns:
        Dict containing all version-related information including:
        - git information
        - package information
        - build information
        - runtime information
    """
    return {
        'git': get_git_info(),
        'package': get_package_info(),
        'build': get_build_info(),
        'runtime': {
            'debug_mode': config.DEBUG,
            'environment': os.getenv('XEXTRACT_ENV', 'production')
        }
    }