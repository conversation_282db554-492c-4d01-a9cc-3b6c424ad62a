"""Hybrid extractor combining rule-based and LLM-based extraction."""

import logging
from typing import Any, Dict, List, Optional, Union

from .base import BaseExtractor
from .rule_extractor import RuleExtractor, RuleSet, Rule
from .llm_extractor import LLMExtractor
from ..llm import LLM

logger = logging.getLogger(__name__)


class HybridExtractor(BaseExtractor):
    """Hybrid extractor combining rule-based and LLM-based extraction.

    This extractor uses both rule-based and LLM-based extraction methods
    and combines their results based on confidence scores.
    """

    def __init__(
        self,
        llm: Optional[LLM] = None,
        rules: Optional[Union[RuleSet, List[Rule], Dict[str, Any], str]] = None,
        llm_weight: float = 0.7,
        rule_weight: float = 0.3,
        fallback_to_llm: bool = True
    ):
        """Initialize hybrid extractor.

        Args:
            llm: LLM instance to use for extraction
            rules: Rules for extraction
            llm_weight: Weight for LLM extraction results (0.0-1.0)
            rule_weight: Weight for rule-based extraction results (0.0-1.0)
            fallback_to_llm: Whether to fallback to LLM if rule extraction fails
        """
        self.llm_extractor = LLMExtractor(llm=llm) if llm else None
        self.rule_extractor = RuleExtractor(rules=rules) if rules else None

        # Normalize weights
        total_weight = llm_weight + rule_weight
        self.llm_weight = llm_weight / total_weight if total_weight > 0 else 0.5
        self.rule_weight = rule_weight / total_weight if total_weight > 0 else 0.5

        self.fallback_to_llm = fallback_to_llm

    def extract(
        self,
        source: str,
        schema: Optional[Dict[str, Any]] = None,
        rules: Optional[Union[RuleSet, List[Rule], Dict[str, Any], str]] = None,
        llm_options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Extract data using hybrid approach.

        Args:
            source: Text to extract from
            schema: Schema for LLM extraction
            rules: Rules for rule-based extraction
            llm_options: Options for LLM extraction

        Returns:
            Extracted data with confidence scores

        Raises:
            ValueError: If neither LLM nor rules are available
        """
        if not self.llm_extractor and not self.rule_extractor:
            raise ValueError("Neither LLM nor rules are available for extraction")

        llm_result = None
        rule_result = None

        # Extract using LLM if available
        if self.llm_extractor and schema:
            try:
                llm_result = self.llm_extractor.extract(
                    source=source,
                    schema=schema,
                    **(llm_options or {})
                )
            except Exception as e:
                logger.error(f"Error in LLM extraction: {e}")

        # Extract using rules if available
        if self.rule_extractor:
            try:
                rule_result = self.rule_extractor.extract(
                    source=source,
                    rules=rules
                )
            except Exception as e:
                logger.error(f"Error in rule-based extraction: {e}")

                # Fallback to LLM if rule extraction fails
                if self.fallback_to_llm and self.llm_extractor and schema and not llm_result:
                    try:
                        logger.info("Falling back to LLM extraction")
                        llm_result = self.llm_extractor.extract(
                            source=source,
                            schema=schema,
                            **(llm_options or {})
                        )
                    except Exception as e2:
                        logger.error(f"Error in fallback LLM extraction: {e2}")

        # Combine results
        combined_result = self._combine_results(llm_result, rule_result)

        return combined_result

    def _combine_results(
        self,
        llm_result: Optional[Dict[str, Any]],
        rule_result: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Combine results from LLM and rule-based extraction.

        Args:
            llm_result: Result from LLM extraction
            rule_result: Result from rule-based extraction

        Returns:
            Combined result with confidence scores
        """
        if llm_result and not rule_result:
            return {
                "extracted_data": llm_result,
                "source": "llm",
                "confidence": 1.0,
                "method": "llm_only"
            }

        if rule_result and not llm_result:
            return {
                "extracted_data": rule_result.get("extracted_data", {}),
                "source": "rule",
                "confidence": 1.0,
                "method": "rule_only"
            }

        if llm_result and rule_result:
            # Combine results based on weights
            llm_data = llm_result
            rule_data = rule_result.get("extracted_data", {})

            combined_data = {}
            confidence_scores = {}

            # Get all field names from both results
            all_fields = set(llm_data.keys()) | set(rule_data.keys())

            for field in all_fields:
                llm_value = llm_data.get(field)
                rule_value = rule_data.get(field)

                if llm_value is not None and rule_value is not None:
                    # Both extractors found a value
                    if llm_value == rule_value:
                        # Values agree, high confidence
                        combined_data[field] = llm_value
                        confidence_scores[field] = 1.0
                    else:
                        # Values disagree, use weighted value
                        # For simplicity, we'll use the LLM value if weight is higher
                        if self.llm_weight >= self.rule_weight:
                            combined_data[field] = llm_value
                            confidence_scores[field] = self.llm_weight
                        else:
                            combined_data[field] = rule_value
                            confidence_scores[field] = self.rule_weight
                elif llm_value is not None:
                    # Only LLM found a value
                    combined_data[field] = llm_value
                    confidence_scores[field] = self.llm_weight
                elif rule_value is not None:
                    # Only rule found a value
                    combined_data[field] = rule_value
                    confidence_scores[field] = self.rule_weight

            # Calculate overall confidence
            overall_confidence = sum(confidence_scores.values()) / len(confidence_scores) if confidence_scores else 0.0

            return {
                "extracted_data": combined_data,
                "confidence_scores": confidence_scores,
                "overall_confidence": overall_confidence,
                "method": "hybrid",
                "llm_weight": self.llm_weight,
                "rule_weight": self.rule_weight
            }

        # No results from either method
        return {
            "extracted_data": {},
            "confidence": 0.0,
            "method": "none"
        }
