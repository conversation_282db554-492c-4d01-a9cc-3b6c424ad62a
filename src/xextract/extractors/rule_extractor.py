"""Rule-based extractor for structured data extraction."""

import re
import json
import logging
from typing import Any, Dict, List, Optional, Union, Pattern, Callable

from .base import BaseExtractor

logger = logging.getLogger(__name__)


class Rule:
    """Rule for extracting data from text."""

    def __init__(
        self,
        field_name: str,
        pattern: Union[str, Pattern],
        transform: Optional[Callable[[str], Any]] = None,
        required: bool = False,
        default: Any = None,
        description: Optional[str] = None
    ):
        """Initialize rule.

        Args:
            field_name: Name of the field to extract
            pattern: Regex pattern to match
            transform: Function to transform the matched value
            required: Whether the field is required
            default: Default value if not found
            description: Description of the rule
        """
        self.field_name = field_name

        # Compile pattern if it's a string
        if isinstance(pattern, str):
            self.pattern = re.compile(pattern, re.DOTALL | re.MULTILINE)
        else:
            self.pattern = pattern

        self.transform = transform
        self.required = required
        self.default = default
        self.description = description

    def extract(self, text: str) -> Any:
        """Extract value from text.

        Args:
            text: Text to extract from

        Returns:
            Extracted value
        """
        match = self.pattern.search(text)

        if not match:
            if self.required:
                logger.warning(f"Required field '{self.field_name}' not found")
            return self.default

        # Get the first group if there are groups, otherwise the whole match
        if match.groups():
            value = match.group(1)
        else:
            value = match.group(0)

        # Apply transformation if provided
        if self.transform and value is not None:
            try:
                return self.transform(value)
            except Exception as e:
                logger.error(f"Error transforming value for field '{self.field_name}': {e}")
                return self.default

        return value


class RuleSet:
    """Set of rules for extracting data from text."""

    def __init__(
        self,
        name: str,
        rules: List[Rule],
        description: Optional[str] = None
    ):
        """Initialize rule set.

        Args:
            name: Name of the rule set
            rules: List of rules
            description: Description of the rule set
        """
        self.name = name
        self.rules = rules
        self.description = description

    def extract(self, text: str) -> Dict[str, Any]:
        """Extract data from text using rules.

        Args:
            text: Text to extract from

        Returns:
            Extracted data
        """
        result = {}

        for rule in self.rules:
            result[rule.field_name] = rule.extract(text)

        return result

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RuleSet':
        """Create rule set from dictionary.

        Args:
            data: Dictionary with rule set definition

        Returns:
            Rule set instance
        """
        name = data.get("name", "unnamed")
        description = data.get("description")
        rules_data = data.get("rules", [])

        rules = []
        for rule_data in rules_data:
            field_name = rule_data.get("field_name")
            pattern = rule_data.get("pattern")
            required = rule_data.get("required", False)
            default = rule_data.get("default")
            description = rule_data.get("description")

            # Create transform function if specified
            transform = None
            transform_type = rule_data.get("transform")
            if transform_type:
                if transform_type == "int":
                    transform = lambda x: int(x.strip())
                elif transform_type == "float":
                    transform = lambda x: float(x.strip())
                elif transform_type == "boolean":
                    transform = lambda x: x.lower().strip() in ("true", "yes", "1")
                elif transform_type == "strip":
                    transform = lambda x: x.strip()

            rules.append(Rule(
                field_name=field_name,
                pattern=pattern,
                transform=transform,
                required=required,
                default=default,
                description=description
            ))

        return cls(name=name, rules=rules, description=description)

    @classmethod
    def from_json(cls, json_str: str) -> 'RuleSet':
        """Create rule set from JSON string.

        Args:
            json_str: JSON string with rule set definition

        Returns:
            Rule set instance
        """
        data = json.loads(json_str)
        return cls.from_dict(data)

    # 移除 from_file 方法，因为我们不再需要它


class RuleExtractor(BaseExtractor):
    """Rule-based extractor for structured data extraction."""

    def __init__(
        self,
        rules: Optional[Union[RuleSet, List[Rule], Dict[str, Any], str]] = None
    ):
        """Initialize rule extractor.

        Args:
            rules: Rules for extraction (RuleSet, list of Rules, dict, or JSON string)
        """
        self.rule_set = None

        if rules:
            if isinstance(rules, RuleSet):
                self.rule_set = rules
            elif isinstance(rules, list):
                self.rule_set = RuleSet(name="default", rules=rules)
            elif isinstance(rules, dict):
                self.rule_set = RuleSet.from_dict(rules)
            elif isinstance(rules, str) and (rules.startswith("{") or rules.startswith("[")):
                self.rule_set = RuleSet.from_json(rules)

    def extract(
        self,
        source: str,
        rules: Optional[Union[RuleSet, List[Rule], Dict[str, Any], str]] = None
    ) -> Dict[str, Any]:
        """Extract data using rules.

        Args:
            source: Text to extract from
            rules: Rules for extraction (overrides rules provided in constructor)

        Returns:
            Extracted data

        Raises:
            ValueError: If no rules are provided
        """
        # Use provided rules or fall back to instance rules
        rule_set = None
        if rules:
            if isinstance(rules, RuleSet):
                rule_set = rules
            elif isinstance(rules, list):
                rule_set = RuleSet(name="default", rules=rules)
            elif isinstance(rules, dict):
                rule_set = RuleSet.from_dict(rules)
            elif isinstance(rules, str) and (rules.startswith("{") or rules.startswith("[")):
                rule_set = RuleSet.from_json(rules)
        else:
            rule_set = self.rule_set

        if not rule_set:
            raise ValueError("No rules provided for extraction")

        # Extract data using rules
        result = rule_set.extract(source)

        return {
            "extracted_data": result,
            "rule_set_name": rule_set.name,
            "confidence": 1.0  # Rule-based extraction has fixed confidence
        }
