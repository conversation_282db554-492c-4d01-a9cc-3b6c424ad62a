"""Base extractor interface."""
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
from pathlib import Path


class BaseExtractor(ABC):
    """Base class for all extractors."""

    @abstractmethod
    def extract(
        self,
        source: Union[str, Path, bytes],
        **kwargs
    ) -> Dict[str, Any]:
        """Extract data from source.

        Args:
            source: Source to extract from (file path, Path object, or bytes)
            **kwargs: Additional extraction options

        Returns:
            Extracted data
        """
        pass

    # extract_sync method is no longer needed since extract is now synchronous
