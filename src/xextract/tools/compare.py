"""比对工具，用于比较两次提取结果的差异。

此模块提供了比较两次提取结果差异的功能，用于评估模型升级前后的效果变化。
同时提供了计算准确率、召回率和F1值的功能，用于评估提取结果的质量。
"""

import json
import logging
import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Union
from collections import defaultdict

try:
    from deepdiff import DeepDiff
    DEEPDIFF_AVAILABLE = True
except ImportError:
    DEEPDIFF_AVAILABLE = False
    logging.warning("deepdiff库未安装，将使用基本比较方法。安装方法: pip install deepdiff")

logger = logging.getLogger(__name__)


class ResultComparer:
    """提取结果比对器，用于比较两次提取结果的差异。"""

    def __init__(self, ignore_fields: Optional[List[str]] = None):
        """初始化比对器。

        Args:
            ignore_fields: 比对时忽略的字段列表
        """
        self.ignore_fields = set(ignore_fields or [])

    def compare_files(
        self,
        baseline_file: Union[str, Path],
        current_file: Union[str, Path],
        output_file: Optional[Union[str, Path]] = None,
        output_format: str = "json",
    ) -> Dict[str, Any]:
        """比较两个提取结果文件的差异。

        Args:
            baseline_file: 基准文件路径（旧模型结果）
            current_file: 当前文件路径（新模型结果）
            output_file: 差异输出文件路径
            output_format: 输出格式（json, csv, markdown）

        Returns:
            差异结果字典
        """
        # 加载文件
        baseline_data = self._load_file(baseline_file)
        current_data = self._load_file(current_file)

        # 比较结果
        diff_result = self.compare_results(baseline_data, current_data)

        # 保存差异结果
        if output_file:
            self._save_diff(diff_result, output_file, output_format)

        return diff_result

    def compare_results(
        self, baseline: Dict[str, Any], current: Dict[str, Any]
    ) -> Dict[str, Any]:
        """比较两个提取结果的差异。

        Args:
            baseline: 基准结果（旧模型结果）
            current: 当前结果（新模型结果）

        Returns:
            差异结果字典
        """
        # 检查是否为结构化结果
        is_structured = self._is_structured_result(baseline) and self._is_structured_result(current)

        if is_structured:
            return self._compare_structured_results(baseline, current)
        else:
            return self._compare_simple_results(baseline, current)

    def _compare_structured_results(
        self, baseline: Dict[str, Any], current: Dict[str, Any]
    ) -> Dict[str, Any]:
        """比较两个结构化提取结果的差异。

        Args:
            baseline: 基准结果（旧模型结果）
            current: 当前结果（新模型结果）

        Returns:
            差异结果字典
        """
        # 获取字段列表
        baseline_fields = baseline.get("fields", {})
        current_fields = current.get("fields", {})

        # 所有字段的并集
        all_fields = set(baseline_fields.keys()) | set(current_fields.keys())
        all_fields = all_fields - self.ignore_fields

        # 计算差异
        field_diffs = {}
        for field in all_fields:
            baseline_value = baseline_fields.get(field, {}).get("value")
            current_value = current_fields.get(field, {}).get("value")

            # 如果两个值都是None或空字符串，认为相同
            if (baseline_value is None or baseline_value == "") and (
                current_value is None or current_value == ""
            ):
                continue

            # 如果值不同，记录差异
            if baseline_value != current_value:
                field_diffs[field] = {
                    "baseline": baseline_value,
                    "current": current_value,
                    "baseline_confidence": baseline_fields.get(field, {}).get("confidence", 0),
                    "current_confidence": current_fields.get(field, {}).get("confidence", 0),
                }

        # 计算整体指标
        total_fields = len(all_fields)
        changed_fields = len(field_diffs)
        unchanged_fields = total_fields - changed_fields
        change_rate = changed_fields / total_fields if total_fields > 0 else 0

        # 构建差异结果
        diff_result = {
            "document_id": current.get("document_id", "unknown"),
            "schema_id": current.get("schema_id", "unknown"),
            "baseline_model": baseline.get("metadata", {}).get("extraction", {}).get("model", "unknown"),
            "current_model": current.get("metadata", {}).get("extraction", {}).get("model", "unknown"),
            "metrics": {
                "total_fields": total_fields,
                "changed_fields": changed_fields,
                "unchanged_fields": unchanged_fields,
                "change_rate": change_rate,
                "baseline_confidence": baseline.get("overall_confidence", 0),
                "current_confidence": current.get("overall_confidence", 0),
                "confidence_change": current.get("overall_confidence", 0) - baseline.get("overall_confidence", 0),
            },
            "field_diffs": field_diffs,
        }

        return diff_result

    def _compare_simple_results(
        self, baseline: Dict[str, Any], current: Dict[str, Any]
    ) -> Dict[str, Any]:
        """比较两个简单提取结果的差异。

        Args:
            baseline: 基准结果（旧模型结果）
            current: 当前结果（新模型结果）

        Returns:
            差异结果字典
        """
        # 获取数据部分
        baseline_data = baseline.get("data", baseline)
        current_data = current.get("data", current)

        # 所有字段的并集
        all_fields = set(baseline_data.keys()) | set(current_data.keys())
        all_fields = all_fields - self.ignore_fields

        # 计算差异
        field_diffs = {}
        for field in all_fields:
            baseline_value = baseline_data.get(field)
            current_value = current_data.get(field)

            # 如果两个值都是None或空字符串，认为相同
            if (baseline_value is None or baseline_value == "") and (
                current_value is None or current_value == ""
            ):
                continue

            # 如果值不同，记录差异
            if baseline_value != current_value:
                field_diffs[field] = {
                    "baseline": baseline_value,
                    "current": current_value,
                }

        # 计算整体指标
        total_fields = len(all_fields)
        changed_fields = len(field_diffs)
        unchanged_fields = total_fields - changed_fields
        change_rate = changed_fields / total_fields if total_fields > 0 else 0

        # 获取元数据
        baseline_metadata = baseline.get("metadata", {})
        current_metadata = current.get("metadata", {})

        # 构建差异结果
        diff_result = {
            "document_id": os.path.basename(str(baseline.get("file", "unknown"))),
            "schema_id": baseline.get("schema", "unknown"),
            "baseline_model": baseline_metadata.get("extraction", {}).get("model", "unknown"),
            "current_model": current_metadata.get("extraction", {}).get("model", "unknown"),
            "metrics": {
                "total_fields": total_fields,
                "changed_fields": changed_fields,
                "unchanged_fields": unchanged_fields,
                "change_rate": change_rate,
                "baseline_confidence": baseline_metadata.get("confidence", {}).get("overall", 0),
                "current_confidence": current_metadata.get("confidence", {}).get("overall", 0),
                "confidence_change": current_metadata.get("confidence", {}).get("overall", 0) -
                                    baseline_metadata.get("confidence", {}).get("overall", 0),
            },
            "field_diffs": field_diffs,
        }

        return diff_result

    def compare_directories(
        self,
        baseline_dir: Union[str, Path],
        current_dir: Union[str, Path],
        output_dir: Optional[Union[str, Path]] = None,
        file_pattern: str = "*.json",
        summary_file: Optional[Union[str, Path]] = None,
        output_format: str = "json",
    ) -> List[Dict[str, Any]]:
        """比较两个目录下的提取结果文件。

        Args:
            baseline_dir: 基准目录路径（旧模型结果）
            current_dir: 当前目录路径（新模型结果）
            output_dir: 差异输出目录路径
            file_pattern: 文件匹配模式
            summary_file: 汇总报告文件路径
            output_format: 输出格式（json, csv, markdown）

        Returns:
            差异结果列表
        """
        # 确保目录存在
        baseline_dir = Path(baseline_dir)
        current_dir = Path(current_dir)

        if not baseline_dir.exists() or not baseline_dir.is_dir():
            raise ValueError(f"基准目录不存在或不是目录: {baseline_dir}")
        if not current_dir.exists() or not current_dir.is_dir():
            raise ValueError(f"当前目录不存在或不是目录: {current_dir}")

        # 创建输出目录
        if output_dir:
            output_dir = Path(output_dir)
            os.makedirs(output_dir, exist_ok=True)

        # 获取基准目录下的所有文件
        baseline_files = list(baseline_dir.glob(file_pattern))
        logger.info(f"在基准目录 {baseline_dir} 中找到 {len(baseline_files)} 个文件")

        # 比较每个文件
        diff_results = []
        for baseline_file in baseline_files:
            # 构建当前文件路径
            current_file = current_dir / baseline_file.name
            if not current_file.exists():
                logger.warning(f"当前目录中找不到对应文件: {current_file}")
                continue

            # 构建输出文件路径
            output_file = None
            if output_dir:
                output_file = output_dir / f"{baseline_file.stem}_diff.{output_format}"

            # 比较文件
            try:
                diff_result = self.compare_files(
                    baseline_file, current_file, output_file, output_format
                )
                diff_results.append(diff_result)
                logger.info(f"成功比较文件: {baseline_file.name}")
            except Exception as e:
                logger.error(f"比较文件 {baseline_file.name} 时出错: {e}")

        # 生成汇总报告
        if summary_file and diff_results:
            self._generate_summary(diff_results, summary_file, output_format)

        return diff_results

    def _generate_summary(
        self, diff_results: List[Dict[str, Any]], summary_file: Union[str, Path], format: str = "json"
    ) -> None:
        """生成汇总报告。

        Args:
            diff_results: 差异结果列表
            summary_file: 汇总报告文件路径
            format: 输出格式（json, csv, markdown）
        """
        # 提取汇总指标
        summary_data = []
        for diff in diff_results:
            summary_data.append({
                "document_id": diff.get("document_id", "unknown"),
                "schema_id": diff.get("schema_id", "unknown"),
                "baseline_model": diff.get("baseline_model", "unknown"),
                "current_model": diff.get("current_model", "unknown"),
                "total_fields": diff.get("metrics", {}).get("total_fields", 0),
                "changed_fields": diff.get("metrics", {}).get("changed_fields", 0),
                "unchanged_fields": diff.get("metrics", {}).get("unchanged_fields", 0),
                "change_rate": diff.get("metrics", {}).get("change_rate", 0),
                "baseline_confidence": diff.get("metrics", {}).get("baseline_confidence", 0),
                "current_confidence": diff.get("metrics", {}).get("current_confidence", 0),
                "confidence_change": diff.get("metrics", {}).get("confidence_change", 0),
            })

        # 计算整体指标
        total_documents = len(diff_results)
        total_fields = sum(item["total_fields"] for item in summary_data)
        total_changed_fields = sum(item["changed_fields"] for item in summary_data)
        overall_change_rate = total_changed_fields / total_fields if total_fields > 0 else 0
        avg_baseline_confidence = sum(item["baseline_confidence"] for item in summary_data) / total_documents if total_documents > 0 else 0
        avg_current_confidence = sum(item["current_confidence"] for item in summary_data) / total_documents if total_documents > 0 else 0
        avg_confidence_change = avg_current_confidence - avg_baseline_confidence

        # 构建汇总数据
        summary = {
            "overall_metrics": {
                "total_documents": total_documents,
                "total_fields": total_fields,
                "total_changed_fields": total_changed_fields,
                "overall_change_rate": overall_change_rate,
                "avg_baseline_confidence": avg_baseline_confidence,
                "avg_current_confidence": avg_current_confidence,
                "avg_confidence_change": avg_confidence_change,
            },
            "document_metrics": summary_data,
        }

        # 保存汇总报告
        self._save_diff(summary, summary_file, format)

    def _load_file(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """加载提取结果文件。

        Args:
            file_path: 文件路径

        Returns:
            提取结果字典
        """
        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")

        try:
            with open(file_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            raise ValueError(f"加载文件 {file_path} 时出错: {e}")

    def _save_diff(
        self, diff_result: Dict[str, Any], output_file: Union[str, Path], format: str = "json"
    ) -> None:
        """保存差异结果。

        Args:
            diff_result: 差异结果字典
            output_file: 输出文件路径
            format: 输出格式（json, csv, markdown）
        """
        output_file = Path(output_file)
        os.makedirs(output_file.parent, exist_ok=True)

        if format == "json":
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(diff_result, f, ensure_ascii=False, indent=2)
        elif format == "csv":
            # 简单的CSV输出
            with open(output_file, "w", encoding="utf-8") as f:
                if "document_metrics" in diff_result:
                    # 汇总报告
                    # 写入表头
                    headers = ["document_id", "schema_id", "baseline_model", "current_model",
                              "total_fields", "changed_fields", "unchanged_fields", "change_rate",
                              "baseline_confidence", "current_confidence", "confidence_change"]
                    f.write(",".join(headers) + "\n")

                    # 写入数据
                    for item in diff_result["document_metrics"]:
                        values = [
                            str(item.get("document_id", "")),
                            str(item.get("schema_id", "")),
                            str(item.get("baseline_model", "")),
                            str(item.get("current_model", "")),
                            str(item.get("total_fields", 0)),
                            str(item.get("changed_fields", 0)),
                            str(item.get("unchanged_fields", 0)),
                            f"{item.get('change_rate', 0):.2%}",
                            f"{item.get('baseline_confidence', 0):.4f}",
                            f"{item.get('current_confidence', 0):.4f}",
                            f"{item.get('confidence_change', 0):.4f}"
                        ]
                        f.write(",".join(values) + "\n")
                else:
                    # 单个文件的差异
                    # 写入表头
                    f.write("field,baseline,current,baseline_confidence,current_confidence\n")

                    # 写入数据
                    for field, values in diff_result.get("field_diffs", {}).items():
                        row = [
                            field,
                            str(values.get("baseline", "")),
                            str(values.get("current", "")),
                            f"{values.get('baseline_confidence', 0):.4f}",
                            f"{values.get('current_confidence', 0):.4f}"
                        ]
                        f.write(",".join(row) + "\n")
        elif format == "markdown":
            with open(output_file, "w", encoding="utf-8") as f:
                if "document_metrics" in diff_result:
                    # 汇总报告
                    f.write("# 模型升级效果比对汇总报告\n\n")
                    f.write("## 整体指标\n\n")
                    overall = diff_result["overall_metrics"]
                    f.write(f"- 总文档数: {overall['total_documents']}\n")
                    f.write(f"- 总字段数: {overall['total_fields']}\n")
                    f.write(f"- 变化字段数: {overall['total_changed_fields']}\n")
                    f.write(f"- 整体变化率: {overall['overall_change_rate']:.2%}\n")
                    f.write(f"- 基准模型平均置信度: {overall['avg_baseline_confidence']:.4f}\n")
                    f.write(f"- 当前模型平均置信度: {overall['avg_current_confidence']:.4f}\n")
                    f.write(f"- 置信度变化: {overall['avg_confidence_change']:.4f}\n\n")

                    f.write("## 文档指标\n\n")
                    # 手动创建Markdown表格
                    headers = ["文档ID", "模式", "基准模型", "当前模型", "总字段数", "变化字段数", "未变化字段数", "变化率", "基准置信度", "当前置信度", "置信度变化"]
                    f.write("| " + " | ".join(headers) + " |\n")
                    f.write("| " + " | ".join(["---"] * len(headers)) + " |\n")

                    for item in diff_result["document_metrics"]:
                        values = [
                            str(item.get("document_id", "")),
                            str(item.get("schema_id", "")),
                            str(item.get("baseline_model", "")),
                            str(item.get("current_model", "")),
                            str(item.get("total_fields", 0)),
                            str(item.get("changed_fields", 0)),
                            str(item.get("unchanged_fields", 0)),
                            f"{item.get('change_rate', 0):.2%}",
                            f"{item.get('baseline_confidence', 0):.4f}",
                            f"{item.get('current_confidence', 0):.4f}",
                            f"{item.get('confidence_change', 0):.4f}"
                        ]
                        f.write("| " + " | ".join(values) + " |\n")
                else:
                    # 单个文件的差异
                    f.write(f"# 文档 {diff_result['document_id']} 的模型升级效果比对\n\n")
                    f.write(f"- 模式: {diff_result['schema_id']}\n")
                    f.write(f"- 基准模型: {diff_result['baseline_model']}\n")
                    f.write(f"- 当前模型: {diff_result['current_model']}\n\n")

                    metrics = diff_result["metrics"]
                    f.write("## 指标\n\n")
                    f.write(f"- 总字段数: {metrics['total_fields']}\n")
                    f.write(f"- 变化字段数: {metrics['changed_fields']}\n")
                    f.write(f"- 未变化字段数: {metrics['unchanged_fields']}\n")
                    f.write(f"- 变化率: {metrics['change_rate']:.2%}\n")
                    f.write(f"- 基准模型置信度: {metrics['baseline_confidence']:.4f}\n")
                    f.write(f"- 当前模型置信度: {metrics['current_confidence']:.4f}\n")
                    f.write(f"- 置信度变化: {metrics['confidence_change']:.4f}\n\n")

                    f.write("## 字段差异\n\n")

                    # 检查是否有字段差异
                    field_diffs = diff_result.get("field_diffs", {})
                    if field_diffs:
                        # 手动创建Markdown表格
                        headers = ["字段", "基准值", "当前值", "基准置信度", "当前置信度"]
                        f.write("| " + " | ".join(headers) + " |\n")
                        f.write("| " + " | ".join(["---"] * len(headers)) + " |\n")

                        for field, values in field_diffs.items():
                            row = [
                                field,
                                str(values.get("baseline", "")),
                                str(values.get("current", "")),
                                f"{values.get('baseline_confidence', 0):.4f}",
                                f"{values.get('current_confidence', 0):.4f}"
                            ]
                            f.write("| " + " | ".join(row) + " |\n")
                    else:
                        f.write("无字段差异\n")
        else:
            raise ValueError(f"不支持的输出格式: {format}")

    def _is_structured_result(self, result: Dict[str, Any]) -> bool:
        """检查是否为结构化提取结果。

        Args:
            result: 提取结果字典

        Returns:
            是否为结构化结果
        """
        return "fields" in result and "document_id" in result and "schema_id" in result
