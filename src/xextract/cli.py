"""Command-line interface for XExtract."""

import argparse
import json
import sys
import importlib.metadata
from typing import List, Optional

# Handle both direct script execution and module import
try:
    # When imported as a module
    from . import __version__
    from .engine import ExtractEngine
    from .schema import Schema
except ImportError:
    # When run directly as a script
    try:
        __version__ = importlib.metadata.version("xextract")
    except importlib.metadata.PackageNotFoundError:
        __version__ = "0.1.0"  # Default version if package not installed

    # Adjust import paths for direct execution
    import os
    import sys
    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))
    from src.xextract.engine import ExtractEngine
    from src.xextract.schema import Schema


def main(args: Optional[List[str]] = None) -> int:
    """
    Run the XExtract CLI.

    Args:
        args: Command-line arguments (defaults to sys.argv[1:])

    Returns:
        Exit code
    """
    if args is None:
        args = sys.argv[1:]

    parser = argparse.ArgumentParser(
        prog="xextract",
        description="Extract data from various sources",
    )
    parser.add_argument(
        "--version", action="version", version=f"%(prog)s {__version__}"
    )

    subparsers = parser.add_subparsers(dest="command", help="Command to run")

    # from-source command
    source_parser = subparsers.add_parser(
        "from-source", help="Extract data from a source file or URL"
    )
    source_parser.add_argument("source", help="Source file or URL")
    source_parser.add_argument(
        "--output", "-o", help="Output file (defaults to stdout)"
    )

    # from-text command
    text_parser = subparsers.add_parser(
        "from-text", help="Extract data from text"
    )
    text_parser.add_argument("text", help="Text to extract data from")
    text_parser.add_argument(
        "--output", "-o", help="Output file (defaults to stdout)"
    )

    parsed_args = parser.parse_args(args)

    if parsed_args.command is None:
        parser.print_help()
        return 1

    result = None

    # Create engine
    engine = ExtractEngine()

    if parsed_args.command == "from-source":
        # Extract data from source
        result = engine.extract_sync(parsed_args.source, "test_schema")
    elif parsed_args.command == "from-text":
        # Extract data from text
        result = engine.extract_from_text_sync(parsed_args.text, "test_schema")

    if result is not None:
        output = json.dumps(result, indent=2)
        if parsed_args.output:
            with open(parsed_args.output, "w", encoding="utf-8") as f:
                f.write(output)
        else:
            print(output)

    return 0


if __name__ == "__main__":
    sys.exit(main())
