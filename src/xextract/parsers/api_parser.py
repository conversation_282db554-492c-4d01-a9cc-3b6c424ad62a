"""API-based document parser.

This module provides a document parser that uses an external API service.
It includes a built-in API client to avoid dependencies on other layers.
"""

import json
import logging
import os
import random
import sys
import time
import concurrent.futures
from pathlib import Path
from typing import Any, BinaryIO, Dict, List, Optional, Union

import httpx

from .base import BasePDFParser

# 不再需要导入DocJsonAnalyzer
# from .document.analyzer.doc_json import DocJsonAnalyzer

# 设置日志记录器
logger = logging.getLogger(__name__)

# Import config
try:
    # 确保项目根目录在Python路径中
    import sys
    from pathlib import Path

    project_root = Path(__file__).parent.parent.parent.parent.absolute()
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))

    from config.config import ROOT_DIR, config

    logger.debug(f"Using config from {ROOT_DIR}/config/config.py")
except ImportError as e:
    # 如果无法导入config，使用默认值
    logger.warning(f"Failed to import config: {e}, using default values")
    config = type(
        "Config",
        (),
        {
            "document_parser": {
                "api_base_url": "http://api.memect.cn:6111",
                "api_key": "",
                "timeout": 60,
                "max_retries": 3,
                "use_ocr": False,
                "extract_tables": True,
                "extract_layout": True,
            }
        },
    )


class DocumentAPIClient:
    """Client for document parsing API.

    This client provides methods to interact with the document parsing API
    that supports various document formats.
    """

    def __init__(
        self,
        api_base_url: Optional[str] = None,
        api_key: Optional[str] = None,
        timeout: Optional[int] = None,
        max_retries: Optional[int] = None,
        poll_interval: int = 1,
        **kwargs,
    ):
        """Initialize the document API client.

        Args:
            api_base_url: Base URL for the API
            api_key: API key for authentication
            timeout: Request timeout in seconds
            max_retries: Maximum number of retries for failed requests
            poll_interval: Interval in seconds between polling for async requests
            **kwargs: Additional keyword arguments
        """
        self.api_base_url = api_base_url or config.document_parser.get(
            "api_base_url", "http://api.memect.cn:6111"
        )
        self.api_key = api_key or config.document_parser.get("api_key", "")
        self.timeout = timeout or config.document_parser.get("timeout", 60)
        self.max_retries = max_retries or config.document_parser.get("max_retries", 3)
        self.poll_interval = poll_interval
        self.max_file_size = kwargs.get(
            "max_file_size", 10 * 1024 * 1024
        )  # 10MB default
        self.supported_formats = kwargs.get(
            "supported_formats", ["pdf", "docx", "doc", "txt", "rtf", "pptx"]
        )

        # Initialize HTTP client
        headers = {}
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"

        self.client = httpx.Client(
            timeout=self.timeout, base_url=self.api_base_url, headers=headers
        )

    def close(self):
        """Close the HTTP client."""
        self.client.close()

    def __enter__(self):
        """Enter context manager."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit context manager."""
        self.close()

    def _request(
        self,
        method: str,
        url: str,
        *,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Any] = None,
        files: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        json: Optional[Dict[str, Any]] = None,
        **kwargs,
    ) -> httpx.Response:
        """Send a request to the API with retry logic.

        Args:
            method: HTTP method (GET, POST, etc.)
            url: URL path (will be appended to base URL)
            params: Query parameters
            data: Request body data
            files: Files to upload
            headers: Additional headers
            json: JSON data to send
            **kwargs: Additional keyword arguments for httpx

        Returns:
            Response object

        Raises:
            ValueError: If the request fails after all retries
        """
        # Initialize retry counter
        retries = 0
        last_error = None

        # Retry loop
        while retries <= self.max_retries:
            try:
                # Send request
                response = self.client.request(
                    method,
                    url,
                    params=params,
                    data=data,
                    files=files,
                    headers=headers,
                    json=json,
                    **kwargs,
                )

                # Check for rate limiting or server errors that should be retried
                if (
                    response.status_code in [429, 503, 504]
                    and retries < self.max_retries
                ):
                    retries += 1
                    wait_time = (
                        2**retries + random.random()
                    )  # Exponential backoff with jitter
                    logger.warning(
                        f"Request rate limited or server error. Retrying in {wait_time:.2f} seconds... (attempt {retries}/{self.max_retries})"
                    )
                    time.sleep(wait_time)
                    continue

                # Return successful response or raise for other status codes
                response.raise_for_status()
                return response

            except httpx.HTTPStatusError as e:
                last_error = e
                logger.error(
                    f"HTTP error: {e.response.status_code} - {e.response.text}"
                )

                # Check for task status in error response
                response_text = e.response.text.lower()
                if "running" in response_text or "waiting" in response_text:
                    # This is a task status error, not a real error
                    raise ValueError(f"Task status: {e.response.text}")

                # Don't retry client errors except rate limiting
                if e.response.status_code < 500 and e.response.status_code != 429:
                    raise ValueError(f"API request failed: {e}")

                # Retry server errors
                if retries < self.max_retries:
                    retries += 1
                    wait_time = (
                        2**retries + random.random()
                    )  # Exponential backoff with jitter
                    logger.warning(
                        f"Request failed with server error. Retrying in {wait_time:.2f} seconds... (attempt {retries}/{self.max_retries})"
                    )
                    time.sleep(wait_time)
                else:
                    raise ValueError(
                        f"API request failed after {self.max_retries} retries: {e}"
                    )

            except httpx.RequestError as e:
                last_error = e
                logger.error(f"Request error: {e}")

                # Retry network errors
                if retries < self.max_retries:
                    retries += 1
                    wait_time = (
                        2**retries + random.random()
                    )  # Exponential backoff with jitter
                    logger.warning(
                        f"Network error. Retrying in {wait_time:.2f} seconds... (attempt {retries}/{self.max_retries})"
                    )
                    time.sleep(wait_time)
                else:
                    raise ValueError(
                        f"API request failed after {self.max_retries} retries: {e}"
                    )

        # If we get here, all retries failed
        raise ValueError(
            f"API request failed after {self.max_retries} retries: {last_error}"
        )

    def _poll_task(
        self, url: str, task_id: str, params: Optional[Dict[str, Any]] = None
    ) -> httpx.Response:
        """Poll for the result of an asynchronous task.

        Args:
            url: URL to poll
            task_id: Task ID to poll for
            params: Additional query parameters

        Returns:
            Response object with the task result

        Raises:
            ValueError: If polling fails or the task fails
        """
        if params is None:
            params = {}

        params["task_id"] = task_id

        while True:
            try:
                response = self._request("GET", url, params=params)

                # If we get a 200 response, the task is complete
                return response

            except ValueError as e:
                # Check if this is a task status error
                if "running" in str(e).lower() or "waiting" in str(e).lower():
                    # Task is still running, wait and retry
                    time.sleep(self.poll_interval)
                    continue

                # Other errors should be propagated
                raise

    def pdf2doc(
        self,
        file_path: Union[str, Path],
        params: Optional[Dict[str, Any]] = None,
        output_path: Optional[Union[str, Path]] = None,
        async_mode: bool = False,
        use_thread: bool = False,
    ) -> Dict[str, Any]:
        """Extract document structure from a PDF.

        Args:
            file_path: Path to the PDF file
            params: Additional parameters for the API
            output_path: Path to save the output
            async_mode: Whether to use asynchronous mode for API requests
            use_thread: Whether to run in a separate thread

        Returns:
            Document structure data

        Raises:
            FileNotFoundError: If the file doesn't exist
            ValueError: If the file is not a PDF or other API errors
        """
        if use_thread:
            # Run in a separate thread using ThreadPoolExecutor
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(
                    self._pdf2doc_sync, file_path, params, output_path, async_mode
                )
                return future.result()
        else:
            # Run synchronously
            return self._pdf2doc_sync(file_path, params, output_path, async_mode)

    def _pdf2doc_sync(
        self,
        file_path: Union[str, Path],
        params: Optional[Dict[str, Any]] = None,
        output_path: Optional[Union[str, Path]] = None,
        async_mode: bool = False,
    ) -> Dict[str, Any]:
        """Synchronous implementation of pdf2doc.

        Args:
            file_path: Path to the PDF file
            params: Additional parameters for the API
            output_path: Path to save the output
            async_mode: Whether to use asynchronous mode

        Returns:
            Document structure data

        Raises:
            FileNotFoundError: If the file doesn't exist
            ValueError: If the file is not a PDF or other API errors
        """
        # Validate file path
        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        # Validate file format
        file_ext = file_path.suffix.lower().lstrip(".")
        if file_ext != "pdf":
            raise ValueError(f"Unsupported file format: {file_ext} (supported: pdf)")

        # Prepare default parameters
        default_params = {
            "mode": "3",
            "extract-image": "false",
            "ocr": "auto",
            "ocr-line": "true",
            "format": "4",
            "textlines": "false",
            "table": "all",
            "layout": "auto",
            "analyzer": "auto",
            "merge-table": "false",
            "html": "false",
            "html-scale": "1",
            "table-screenshot": "false",
        }

        # Update with user-provided parameters
        if params:
            for key, value in params.items():
                # Convert boolean values to strings
                if isinstance(value, bool):
                    default_params[key.replace("_", "-")] = str(value).lower()
                else:
                    default_params[key.replace("_", "-")] = str(value)

        # Set output format
        default_params["output-format"] = "json"
        default_params["async"] = "true" if async_mode else "false"

        # Send request
        try:
            with open(file_path, "rb") as f:
                file_data = f.read()

            # Prepare headers
            headers = {
                "Content-Type": "application/octet-stream",
                "Content-Disposition": f"attachment; filename={file_path.name}",
            }

            if async_mode:
                # Send initial request to start the task
                response = self._request(
                    "POST",
                    "/api/pdf2doc",
                    params=default_params,
                    data=file_data,
                    headers=headers,
                )

                # Get task ID from response
                response_data = response.json()
                task_id = response_data.get("task", {}).get("id")

                if not task_id:
                    raise ValueError(f"Invalid response from API: {response_data}")

                # Poll for task completion
                response = self._poll_task(
                    "/api/pdf2doc", task_id, params={"task_id": task_id}
                )
            else:
                # Send synchronous request
                response = self._request(
                    "POST",
                    "/api/pdf2doc",
                    params=default_params,
                    data=file_data,
                    headers=headers,
                )

            # Process response
            if output_path:
                output_path = Path(output_path)
                os.makedirs(output_path.parent, exist_ok=True)

                with open(output_path, "w", encoding="utf-8") as f:
                    if isinstance(response.content, bytes):
                        f.write(response.content.decode("utf-8"))
                    else:
                        json.dump(response.json(), f, indent=2, ensure_ascii=False)

                # Return empty dict if output is saved to file
                return {}

            # Return JSON response
            return response.json()

        except Exception as e:
            logger.error(f"Error in pdf2doc: {e}")
            raise ValueError(f"Failed to extract document structure: {e}")


class APIDocumentParser(BasePDFParser):
    """Document parser that uses an external API service.

    This parser uses the document parser API service to parse PDF files.
    """

    def __init__(
        self,
        api_base_url: Optional[str] = None,
        api_key: Optional[str] = None,
        timeout: Optional[int] = None,
        max_retries: Optional[int] = None,
        **kwargs,
    ):
        """Initialize the API document parser.

        Args:
            api_base_url: API base URL (default: from settings)
            api_key: API key (default: from settings)
            timeout: Request timeout in seconds (default: from settings)
            max_retries: Maximum number of retries (default: from settings)
            **kwargs: Additional options for the API client including:
                - extract_image: Whether to extract images (default: False)
                - ocr: OCR mode (default: "auto")
                - poll_interval: Interval in seconds between polling for async requests
                - supported_formats: List of supported file formats
                - max_file_size: Maximum file size in bytes
        """
        # Store extraction options
        self.extract_image = kwargs.get("extract_image", False)
        self.ocr = kwargs.get("ocr", "auto")
        self.ocr_line = kwargs.get("ocr_line", True)
        self.format = kwargs.get("format", 4)
        self.textlines = kwargs.get("textlines", False)
        self.table = kwargs.get("table", "all")
        self.layout = kwargs.get("layout", "auto")
        self.analyzer = kwargs.get("analyzer", "auto")
        self.mode = kwargs.get("mode", 3)
        self.merge_table = kwargs.get("merge_table", False)
        self.html = kwargs.get("html", False)
        self.html_scale = kwargs.get("html_scale", 1)
        self.table_screenshot = kwargs.get("table_screenshot", False)

        # Initialize API client
        self.client = DocumentAPIClient(
            api_base_url=api_base_url,
            api_key=api_key,
            timeout=timeout,
            max_retries=max_retries,
            **kwargs,
        )

        logger.info(
            f"Initialized API document parser with URL: {self.client.api_base_url}"
        )

    async def parse_pdf(
        self,
        pdf_path: Union[str, Path],
        output_path: Optional[Union[str, Path]] = None,
        **kwargs,
    ) -> Dict[str, Any]:
        """Parse a PDF file using the API service.

        Args:
            pdf_path: Path to the PDF file
            output_path: Path to save the parsed result (optional)
            **kwargs: Additional options for the API request
                - extract_image: Whether to extract images (default: False)
                - ocr: OCR mode (default: "auto")
                - ocr_line: Whether to recognize lines with OCR (default: True)
                - format: Output format version (default: 4)
                - textlines: Whether to output textlines instead of spans (default: False)
                - table: Table extraction mode (default: "all")
                - layout: Layout mode (default: "auto")
                - analyzer: Document type analyzer (default: "auto")
                - mode: Document structure extraction mode (default: 3)
                - merge_table: Whether to merge tables (default: False)
                - html: Whether to output HTML (default: False)
                - html_scale: Scale for HTML output (default: 1)
                - table_screenshot: Whether to take screenshots of tables (default: False)
                - async_mode: Whether to use asynchronous mode (default: False)
                - use_thread: Whether to run in a separate thread (default: False)

        Returns:
            Parsed document content as a dictionary

        Raises:
            FileNotFoundError: If the PDF file doesn't exist
            ValueError: If the PDF file is invalid or parsing fails
        """
        pdf_path = Path(pdf_path)
        logger.info(f"Parsing PDF file: {pdf_path}")

        if not pdf_path.exists():
            logger.error(f"PDF file not found: {pdf_path}")
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")

        # Prepare API parameters - use instance defaults but allow overrides from kwargs
        params = {
            "extract_image": kwargs.get("extract_image", self.extract_image),
            "ocr": kwargs.get("ocr", self.ocr),
            "ocr_line": kwargs.get("ocr_line", self.ocr_line),
            "format": kwargs.get("format", self.format),
            "textlines": kwargs.get("textlines", self.textlines),
            "table": kwargs.get("table", self.table),
            "layout": kwargs.get("layout", self.layout),
            "analyzer": kwargs.get("analyzer", self.analyzer),
            "mode": kwargs.get("mode", self.mode),
            "merge_table": kwargs.get("merge_table", self.merge_table),
            "html": kwargs.get("html", self.html),
            "html_scale": kwargs.get("html_scale", self.html_scale),
            "table_screenshot": kwargs.get("table_screenshot", self.table_screenshot),
        }

        # Get async mode and threading options from kwargs or default to False
        async_mode = kwargs.get("async_mode", False)
        use_thread = kwargs.get("use_thread", False)

        logger.debug(f"API parameters: {params}")
        logger.debug(f"Async mode: {async_mode}, Use thread: {use_thread}")

        # Call API
        try:
            if output_path:
                logger.info(f"Saving output to: {output_path}")
                # Call pdf2doc and save result to file
                self.client.pdf2doc(
                    file_path=pdf_path,
                    params=params,
                    output_path=output_path,
                    async_mode=async_mode,
                    use_thread=use_thread,
                )

                # Load saved file
                with open(output_path, "r", encoding="utf-8") as f:
                    result = json.load(f)
                    logger.info(f"Successfully loaded result from {output_path}")
                    return result
            else:
                # Return result directly
                result = self.client.pdf2doc(
                    file_path=pdf_path,
                    params=params,
                    async_mode=async_mode,
                    use_thread=use_thread,
                )
                logger.info(
                    f"Successfully parsed PDF with {len(result.get('pages', []))} pages"
                )
                return result
        except Exception as e:
            logger.error(f"Error parsing PDF with API: {e}")
            raise ValueError(f"Failed to parse PDF with API: {e}")

    def to_markdown(self, doc_content: Dict[str, Any]) -> str:
        """Convert parsed document content to Markdown.

        Args:
            doc_content: Parsed document content

        Returns:
            Markdown representation of the document
        """
        # 直接从JSON内容中提取文本
        text = ""
        if "pages" in doc_content:
            for page in doc_content["pages"]:
                if "body" in page and "block" in page["body"]:
                    for block in page["body"]["block"]:
                        if "text" in block:
                            text += block["text"] + "\n\n"

        # 如果没有提取到文本，返回原始JSON的字符串表示
        if not text:
            import json
            text = json.dumps(doc_content, ensure_ascii=False, indent=2)

        return text

    def to_text(self, doc_content: Dict[str, Any]) -> str:
        """Convert parsed document content to plain text.

        Args:
            doc_content: Parsed document content

        Returns:
            Plain text representation of the document
        """
        # 直接从JSON内容中提取文本
        text = ""
        if "pages" in doc_content:
            for page in doc_content["pages"]:
                if "body" in page and "block" in page["body"]:
                    for block in page["body"]["block"]:
                        if "text" in block:
                            text += block["text"] + "\n\n"

        return text
