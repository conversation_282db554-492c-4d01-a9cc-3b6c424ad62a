"""Base classes for document parsers.

This module provides abstract base classes for document parsers.
"""

import abc
from pathlib import Path
from typing import Any, Dict, Optional, Union


class BasePDFParser(abc.ABC):
    """Abstract base class for PDF parsers.
    
    This class defines the interface that all PDF parsers must implement.
    """
    
    @abc.abstractmethod
    async def parse_pdf(
        self,
        pdf_path: Union[str, Path],
        output_path: Optional[Union[str, Path]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Parse a PDF file and return its content.
        
        Args:
            pdf_path: Path to the PDF file
            output_path: Path to save the parsed result (optional)
            **kwargs: Additional parser-specific options
            
        Returns:
            Parsed document content as a dictionary
            
        Raises:
            FileNotFoundError: If the PDF file doesn't exist
            ValueError: If the PDF file is invalid or parsing fails
        """
        pass
    
    @abc.abstractmethod
    def to_markdown(self, doc_content: Dict[str, Any]) -> str:
        """Convert parsed document content to Markdown.
        
        Args:
            doc_content: Parsed document content
            
        Returns:
            Markdown representation of the document
        """
        pass
    
    @abc.abstractmethod
    def to_text(self, doc_content: Dict[str, Any]) -> str:
        """Convert parsed document content to plain text.
        
        Args:
            doc_content: Parsed document content
            
        Returns:
            Plain text representation of the document
        """
        pass
