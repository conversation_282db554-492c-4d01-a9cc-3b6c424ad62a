"""Factory for creating PDF parsers.

This module provides a factory for creating PDF parsers based on the parser type.
"""

import logging
from typing import Dict, Optional, Type

from .base import BasePDFParser
from .api_parser import APIDocumentParser

# 检查是否可以使用PDFMiner
PDFMINER_AVAILABLE = False
try:
    from pdfminer.high_level import extract_text
    from .pdfminer_parser import PDFMinerParser
    PDFMINER_AVAILABLE = True
except ImportError:
    pass

# 导入配置
try:
    from config.config import config as settings
except ImportError:
    # 如果无法导入config，使用默认值
    settings = type('Config', (), {
        'pdf_parser': type('PDFParserConfig', (), {
            'default_parser': 'api'
        })
    })

logger = logging.getLogger(__name__)


class PDFParserFactory:
    """Factory for creating PDF parsers.

    This class provides methods for creating PDF parsers based on the parser type.
    """

    # Registry of available parser types
    _parsers: Dict[str, Type[BasePDFParser]] = {
        "api": APIDocumentParser,
    }

    # Register PDFMinerParser if available
    if PDFMINER_AVAILABLE:
        _parsers["pdfminer"] = PDFMinerParser

    @classmethod
    def create_parser(
        cls,
        parser_type: Optional[str] = None,
        **kwargs
    ) -> BasePDFParser:
        """Create a PDF parser.

        Args:
            parser_type: Type of parser to create (default: from settings)
            **kwargs: Additional options for the parser

        Returns:
            PDF parser instance

        Raises:
            ValueError: If the parser type is not supported
        """
        # Use default parser type from settings if not specified
        if parser_type is None:
            parser_type = settings.pdf_parser.default_parser

        # Check if parser type is supported
        if parser_type not in cls._parsers:
            supported_parsers = ", ".join(cls._parsers.keys())
            raise ValueError(
                f"Unsupported parser type: {parser_type}. "
                f"Supported types: {supported_parsers}"
            )

        # Create parser instance
        parser_class = cls._parsers[parser_type]

        # Pass API settings for API parser
        if parser_type == "api":
            # Extract API-specific parameters
            api_kwargs = {}
            for key in ["api_base_url", "api_key", "timeout", "max_retries"]:
                if key in kwargs:
                    api_kwargs[key] = kwargs[key]

            # Add remaining kwargs
            for key, value in kwargs.items():
                if key not in api_kwargs:
                    api_kwargs[key] = value

            return parser_class(**api_kwargs)

        # Pass general settings for other parsers
        return parser_class(**kwargs)

    @classmethod
    def register_parser(cls, parser_type: str, parser_class: Type[BasePDFParser]) -> None:
        """Register a new parser type.

        Args:
            parser_type: Type name for the parser
            parser_class: Parser class

        Raises:
            ValueError: If the parser type is already registered
        """
        if parser_type in cls._parsers:
            raise ValueError(f"Parser type already registered: {parser_type}")

        cls._parsers[parser_type] = parser_class

    @classmethod
    def get_available_parsers(cls) -> Dict[str, Type[BasePDFParser]]:
        """Get available parser types.

        Returns:
            Dictionary of available parser types and their classes
        """
        return cls._parsers.copy()
