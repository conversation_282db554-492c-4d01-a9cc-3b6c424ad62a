#!/usr/bin/env python
# encoding: utf-8
"""
Author: liuwenmin
Date: 2020/2/3
Desc:
"""


class LocationTextPart:
    """
    span抽象类，保存文本和对应的坐标信息
    param: chars: 文本内容
    param: bbox: 坐标
    return:
    """

    def __init__(self, chars, start=None, end=None, bbox=None, page_no=-1, page_size=None):
        self.text = chars
        self.__page_no = page_no
        self.__page_size = (page_size, [])[page_size is None]
        self.__start = (start, 0)[start is None]
        self.__end = (end, len(chars))[end is None]
        self.__bbox = (bbox, [])[bbox is None]

    def page_no(self):
        return self.__page_no

    def page_size(self):
        return self.__page_size

    def __str__(self):
        return self.text[self.__start:self.__end]

    def __radd__(self, other):

        if not isinstance(other, str):
            raise ValueError(f'not support type {type(other)} for LocationTextPart __radd__')

        if self.text[:self.__start].endswith(other):
            start = self.__start - len(other)
            return LocationTextPart(self.text, start, self.__end, self.__bbox, self.__page_no,  self.__page_size)

        return None

    def __add__(self, other):

        if not isinstance(other, str):
            raise ValueError(f'not support type {type(other)} for LocationTextPart __add__')

        if self.text[self.__end:].startswith(other):
            end = self.__end + len(other)
            return LocationTextPart(self.text, self.__start, end, self.__bbox, self.__page_no, self.__page_size)

        return None

    def __copy__(self):
        return LocationTextPart(self.text, self.__start, self.__end, self.__bbox, self.__page_no, self.__page_size)

    def __iter__(self):

        for i in range(self.__start, self.__end):
            yield LocationTextPart(self.text, i, i + 1, self.__bbox, self.__page_no, self.__page_size)

    def __len__(self):
        return self.__end - self.__start

    def __getitem__(self, i):

        start, stop = i.start, i.stop

        if start is None:
            start = self.__start
        else:
            start += self.__start

        if stop is None:
            stop = self.__end
        else:
            stop += self.__start

        return LocationTextPart(self.text, start, stop, self.__bbox, self.__page_no,  self.__page_size)

    def __repr__(self):
        return str(self)

    @classmethod
    def _get_bbox(cls, bbox, length, start=-1, end=-1, with_width_height=False):
        """
        输出坐标格式
            四元组 [左，下，右，上]
            六元组 [左，下，右，上, 宽，高]
        """

        left, bottom, right, top = bbox
        width = right - left

        if start != -1 and end != -1:
            new_left = int((start / length) * width) + left
            new_right = int((end / length) * width) + left
            left, right = new_left, new_right
        if with_width_height:
            char_width = int((right-left) / length)
            height = top - bottom
            return [left, bottom, right, top, char_width, height]
        return [left, bottom, right, top]

    @property
    def bbox(self):
        location = self.get_locations()
        if not location or not location['bbox']:
            return None
        return location['bbox']

    def left(self):
        return self.bbox[0]

    def bottom(self):
        return self.bbox[1]

    def right(self):
        return self.bbox[2]

    def top(self):
        return self.bbox[-1]

    def font_size(self):
        return self.top() - self.bottom()

    def get_old_locations(self):
        """
        输出旧版坐标格式
        """

        assert self.__start <= self.__end

        if self.__start == self.__end:
            return {}

        assert str(self) == self.text[self.__start:self.__end]
        _, _, r, t = self.__page_size
        if r == t == 0:
            page_size = []
        else:
            page_size = [r, t]

        ret = {'page_no': self.__page_no, 'page_size': str(page_size)}

        if sum(self.__bbox) == 0:
            ret['bboxes'] = []
            return ret

        if self.__start == 0 and self.__end == len(self.text):
            ret['bboxes'] = [
                self._get_bbox(self.__bbox, len(self.text), self.__start, self.__end, with_width_height=True)
            ]
            return ret

        ret['bboxes'] = [
            self._get_bbox(self.__bbox, len(self.text), self.__start, self.__end, with_width_height=True)
        ]
        return ret

    def get_locations(self):
        """
        计算内容坐标
        """

        assert self.__start <= self.__end

        if not self.__bbox:
            return {}

        if self.__start == self.__end:
            return {
                'page_no': self.__page_no,
                'page_size': self.__page_size,
                'bbox': self.__bbox,
                'chars': str(self)
            }

        assert str(self) == self.text[self.__start:self.__end]

        ret = {'page_no': self.__page_no, 'page_size': self.__page_size}
        ret['chars'] = str(self)

        if self.__start == 0 and self.__end == len(self.text):
            ret['bbox'] = self.__bbox
            return ret

        ret['bbox'] = self._get_bbox(self.__bbox, len(self.text), self.__start, self.__end)

        return ret

    # @serialize
    # def serialize(self):
    #     """
    #     pkl序列化
    #     """
    #     return self.__dict__

    # @classmethod
    # def deserialize(cls, dict_obj):

    #     ret = cls('')
    #     ret.__dict__ = dict_obj
    #     return ret
