#!/usr/bin/env python
# encoding: utf-8
"""
Author: liuwenmin
Date: 2020/1/29
Desc:
"""

import copy
from .location_text_part import LocationTextPart


class LocationText:
    """
    textline 类
    实例对应一个textline，表示原文中一行内容,每一行由多个字符片段构成
    """

    def __init__(self, parts, without_location: bool = False, print_no_location_chars: bool = False):
        assert isinstance(parts, list)
        self.without_location = without_location
        self.print_no_location_chars = print_no_location_chars
        self.parts = parts
        self.__str = ''.join(str(part) for part in self.parts)

    @classmethod
    def _copy_object(cls, parts):

        ret = cls([copy.copy(part) for part in parts])
        return ret

    @classmethod
    def from_string(cls, string):

        assert isinstance(string, str)
        parts = [LocationTextPart(string)]
        return cls(parts)

    @classmethod
    def from_one_part(cls, text, bbox, page_index, spans, page_size):
        """
        根据解析的part构建实例
        """
        parts = []
        for span in spans:
            if span.get('page_number', ''):
                page_no = span['page_number']
            else:
                page_no = page_index
            location_line = LocationTextPart(
                chars=span['text'],
                bbox=span['bbox'],
                page_no=page_no,
                page_size=page_size
            )
            parts.append(location_line)
        if not parts:
            parts.append(LocationTextPart(
                chars=text,
                bbox=bbox,
                page_no=page_index,
                page_size=page_size
            ))
        location_text = LocationText(parts)
        return location_text

    @classmethod
    def from_part_json(cls, chars, bbox, page_no, page_size):
        location_line = LocationTextPart(chars, bbox=bbox, page_no=page_no, page_size=page_size)
        location_text = LocationText([location_line])
        return location_text

    @classmethod
    def from_locations(cls, jobj):
        if 'locations' not in jobj:
            raise Exception('json格式不正确')
        lines = []
        for part in jobj['locations']:
            location_line = LocationTextPart(**part)
            lines.append(location_line)
        location_text = LocationText(lines)
        return location_text

    @classmethod
    def from_spans(cls, jobj, page_no, page_size):
        """
        根据解析的span构建实例
        """
        if 'spans' not in jobj:
            raise Exception('json格式不正确')
        lines = []
        for span in jobj['spans']:
            location_line = LocationTextPart(
                chars=span['text'],
                bbox=span['bbox'],
                page_no=page_no,
                page_size=page_size
            )
            lines.append(location_line)
        location_text = LocationText(lines)
        return location_text

    def top(self):
        assert self.parts
        return self.parts[0].top()

    def right(self):
        assert self.parts
        return max([part.right() for part in self.parts])

    def left(self):
        assert self.parts
        return min([part.left() for part in self.parts])

    def bottom(self):
        assert self.parts
        return self.parts[-1].bottom()

    def height(self):
        assert self.parts
        return self.parts[0].height()

    @property
    def bbox(self):
        return [self.left(), self.bottom(), self.right(), self.top()]

    def spans_str(self, spliter=''):
        true_ret = spliter.join(str(part) for part in self.parts)
        return true_ret

    def first_page_no(self):
        # 文本可能跨页，但是为了兼容广发POC，这里只返回第一页的页数
        if not self.parts:
            return -1
        return self.parts[0].page_no()

    def first_page_size(self):
        # 文本可能跨页，但是为了兼容广发POC，这里只返回第一页的页数
        if not self.parts:
            return -1
        return self.parts[0].page_size()

    def __bool__(self):
        return bool(str(self))

    def __getitem__(self, i):
        if isinstance(i, str):
            return getattr(self, i)

        i_ = len(self) + i if isinstance(i, int) and i < 0 else i
        i_ = slice(i_, i_ + 1) if isinstance(i_, int) else i_

        start = 0 if i_.start is None else i_.start
        stop = len(self) if i_.stop is None else i_.stop
        step = 1 if i_.step is None else i_.step

        start = max(len(self) + start, 0) if start < 0 else start
        stop = min(len(self) + stop, len(self)) if stop < 0 else stop

        if start >= stop:
            true_ret = str(self)[i]
            ret = self._copy_object('')
            assert true_ret == ret
            return ret

        if step != 1 or start < 0 or stop < 0:
            raise ValueError('没时间写，以后再说')

        new_parts = []
        for part in self.parts:
            length = len(part)

            if start >= length:
                start -= length
                stop -= length
            elif stop <= length:
                new_parts.append(part[start:stop])
                break
            else:
                new_parts.append(part[start:])
                start = 0
                stop -= length
        ret = self._copy_object(new_parts)

        true_ret = str(self)[i]
        assert ret == true_ret
        return ret

    def __add__(self, other):

        if other == '':
            return self

        if isinstance(other, LocationText):
            return self._copy_object(self.parts + other.parts)

        if isinstance(other, str):

            new_parts = self.parts[:]
            if not new_parts:
                new_parts.append(other)
            else:
                new_part = self.parts[-1] + other
                if new_part:
                    new_parts[-1] = new_part
                else:
                    new_parts = self.parts + [other]

            ret = self._copy_object(new_parts)

            true_ret = str(self) + other
            assert true_ret == ret
            return ret

        raise ValueError(f'not support type {type(other)} for LocationText __add__')

    def __mul__(self, n):
        return self._copy_object(self.parts * n)

    def __rmul__(self, n):
        return self.__mul__(n)

    def __mod__(self, *args):
        # todo 位置信息会丢失
        return str(self) % args

    def __eq__(self, x):
        return str(self) == str(x) if isinstance(x, str) else False

    def __ne__(self, x):
        return not self.__eq__(x)

    def __lt__(self, x):
        return str(self) < (str(x) if isinstance(x, str) else x)

    def __le__(self, x):
        return str(self) <= (str(x) if isinstance(x, str) else x)

    def __gt__(self, x):
        return str(self) > (str(x) if isinstance(x, str) else x)

    def __ge__(self, x):
        return str(self) <= (str(x) if isinstance(x, str) else x)

    def __len__(self):
        return len(str(self))

    def __contains__(self, s):
        return str(s) in str(self)

    def __iter__(self):
        for part in self.parts:
            for char in part:
                yield LocationText([char])

    def __str__(self):
        true_ret = ''.join(str(part) for part in self.parts)
        assert self.__str == true_ret
        return self.__str

    def __repr__(self):
        return "'" + str(self) + "'"

    def __int__(self):
        return int(str(self))

    def __float__(self):
        return float(str(self))

    def __hash__(self) -> int:
        return str(self).__hash__()

    def __radd__(self, other):

        if other == '':
            return self

        if isinstance(other, LocationText):
            raise ValueError('不应该呀，这个时候应该调__add__')

        if isinstance(other, str):

            new_parts = self.parts[:]
            if not new_parts:
                new_parts.append(other)
            else:
                new_part = other + self.parts[0]
                if new_part:
                    new_parts[0] = new_part
                else:
                    new_parts = [other] + self.parts

            ret = self._copy_object(new_parts)

            true_ret = other + str(self)
            assert true_ret == ret
            return ret

        raise ValueError(f'not support type {type(other)} for LocationText __radd__')

    # def capitalize(self) -> str: ...  todo
    # def center(self, width: int, fillchar: str = ' ') -> str: ...  todo

    def count(self, x, start=None, end=None):
        x = str(x) if isinstance(x, str) else x
        return str(self).count(x, start, end)

    def encode(self, encoding='utf-8', errors='strict'):
        return str(self).encode(encoding, errors)

    def endswith(self, suffix, start=None, end=None):
        suffix = str(suffix) if isinstance(suffix, str) else suffix
        return str(self).endswith(str(suffix), start, end)

    # def expandtabs(self, tabsize: int = 8) -> str: ...  todo

    def find(self, sub, start=None, end=None):
        sub = str(sub) if isinstance(sub, str) else sub
        return str(self).find(sub, start, end)

    def format(self, *args, **kwargs):
        # todo 会丢失位置信息
        return str(self).format(*args, **kwargs)

    # def format_map(self, map: Mapping[str, Any]) -> str: ...  # todo

    def index(self, sub, start=None, end=None):
        sub = str(sub) if isinstance(sub, str) else sub
        return str(self).index(sub, start, end)

    def isalnum(self):
        return str(self).isalnum()

    def isalpha(self):
        return str(self).isalpha()

    def isdecimal(self):
        return str(self).isdecimal()

    def isdigit(self):
        return str(self).isdigit()

    def isidentifier(self):
        return str(self).isidentifier()

    def islower(self):
        return str(self).islower()

    def isnumeric(self):
        return str(self).isnumeric()

    def isprintable(self):
        return str(self).isprintable()

    def isspace(self):
        return str(self).isspace()

    def istitle(self):
        return str(self).istitle()

    def isupper(self):
        return str(self).isupper()

    def join(self, iterable):
        """
        拼接操作
        """

        sequence = list(iter(iterable))
        if not sequence:
            return self

        ret = self._copy_object([])
        for item in sequence[:-1]:
            ret += item + self
        ret += sequence[-1]

        true_ret = str(self).join([str(s) for s in sequence])
        assert ret == true_ret
        return ret

    # def ljust(self, width: int, fillchar: str = ' ') -> str: ...  # todo
    # def lower(self) -> str: ...  # todo

    def lstrip(self, chars=None):
        """
        去除末尾的空字符
        """

        chars_ = chars
        if chars_ is None:
            chars_ = [' ', '\n', '\r']
        elif isinstance(chars_, str):
            chars_ = [str(chars_)]
        else:
            chars_ = [chars_]

        ret = self

        flag = True
        while flag:
            flag = False
            for char in chars_:
                if ret.startswith(char):
                    ret = ret[len(char):]
                    flag = True
                    break

        chars = str(chars) if isinstance(chars, str) else chars
        assert ret == str(self).lstrip(chars)
        return ret

    # def partition(self, sep: str) -> Tuple[str, str, str]: ...  # todo

    def replace(self, old, new, count=-1):
        """
        替换接口
        """

        if str(old) not in self:
            return self

        count_ = len(self) if count == -1 else count

        ret = self._copy_object([])
        res_str = self
        while str(old) in res_str and count_ > 0:
            count_ -= 1
            idx = res_str.index(old)
            ret += res_str[:idx]
            ret += new
            res_str = res_str[idx+len(old):]
        ret += res_str

        old = str(old) if isinstance(old, str) else old
        new = str(new) if isinstance(new, str) else new
        assert ret == str(self).replace(old, new, count)
        return ret

    def rfind(self, sub, start=None, end=None):

        sub = str(sub) if isinstance(sub, str) else sub
        return str(self).rfind(sub, start, end)

    def rindex(self, sub, start=None, end=None):

        sub = str(sub) if isinstance(sub, str) else sub
        return str(self).rindex(sub, start, end)

    # def rjust(self, width: int, fillchar: str = ' ') -> str: ...  # todo
    # def rpartition(self, sep: str) -> Tuple[str, str, str]: ...  # todo
    # def rsplit(self, sep: str = None, maxsplit: int = -1) -> List[str]: ...  # todo

    def rstrip(self, chars=None):
        """
        去掉空格、换行符
        """

        chars_ = chars
        if chars_ is None:
            chars_ = [' ', '\n', '\r']
        elif isinstance(chars_, str):
            chars_ = list(str(chars_))
        else:
            chars_ = list(chars_)

        ret = self

        flag = True
        while flag:
            flag = False
            for char in chars_:
                if ret.endswith(char):
                    ret = ret[:-len(char)]
                    flag = True
                    break

        chars = str(chars) if isinstance(chars, str) else chars
        true_ret = str(self).rstrip(chars)
        assert ret == true_ret
        return ret

    def split(self, sep=None, maxsplit=-1):
        """
        根据sep 分割接口
        """

        if sep is None and str(self) == '':
            assert [] == str(self).split(sep)
            return []

        max_split = maxsplit if maxsplit != -1 else len(self) + 1
        res_str = self

        # sep=None 和 sep=' '的行为不一样
        sep_ = str(sep) if isinstance(sep, str) else sep
        if sep_ is None:
            res_str = res_str.strip()
            res_str = res_str.replace('\n', ' ')
            res_str = res_str.replace('\r', ' ')
            while '  ' in res_str:
                res_str = res_str.replace('  ', ' ')  # 把多个空格替换成一个
            sep_ = ' '

        ret = []
        while max_split > 0 and sep_ in res_str:
            max_split -= 1
            idx = res_str.index(sep_)
            ret.append(res_str[0:idx])
            res_str = res_str[idx+len(sep_):]
        ret.append(res_str)

        sep = str(sep) if isinstance(sep, str) else sep
        true_ret = str(self).split(sep, maxsplit)
        assert [str(s) for s in ret] == true_ret
        return ret

    # def splitlines(self, keepends: bool = ...) -> List[str]: ...  # todo

    def startswith(self, prefix, start=None, end=None):

        prefix = str(prefix) if isinstance(prefix, str) else prefix
        return str(self).startswith(prefix, start, end)

    def strip(self, chars=None):
        return self.rstrip(chars).lstrip(chars)

    # def swapcase(self) -> str: ...  # todo
    # def title(self) -> str: ...  # todo
    # def translate(self, table: Dict[int, Any]) -> str: ...  # todo
    # def upper(self) -> str: ...  # todo

    def zfill(self, width):

        if width < len(self):
            return self

        ret = (len(self) - width) * '0' + self

        assert ret == str(self).zfill(width)
        return ret

    # @staticmethod
    # @overload
    # def maketrans(x: Union[Dict[int, Any], Dict[str, Any]]) -> Dict[int, Any]: ...  # todo
    # @staticmethod
    # @overload
    # def maketrans(x: str, y: str, z: str = ...) -> Dict[int, Any]: ...  # todo

    @classmethod
    def _merge_location(cls, old_locations, new_location):
        """
        合并相连的坐标bbox
        """

        if not new_location:
            return old_locations

        if not old_locations:
            old_locations.append(new_location)
            return old_locations

        last_old_location = old_locations[-1]
        if last_old_location['page_no'] != new_location['page_no']:
            old_locations.append(new_location)
            return old_locations

        last_bbox = last_old_location['bboxes'][-1]
        first_bbox = new_location['bboxes'][0]

        _, bottom1, left1, top1, width1, height1 = last_bbox
        right2, bottom2, left2, top2, _, height2 = first_bbox

        if [left1, top1, bottom1, width1, height1] == [right2, top2, bottom2, width1, height2]:
            old_locations[-1]['bboxes'][-1][2] = left2
            old_locations[-1]['bboxes'].extend(new_location['bboxes'][1:])
        else:
            old_locations[-1]['bboxes'].extend(new_location['bboxes'])
        return old_locations

    def _print_no_location_str(self):
        ret = ''
        for part in self.parts:
            if isinstance(part, str):
                ret += f'[<{part}>]'
            else:
                ret += str(part)
        return ret

    def get_locations(self, format_='bbox', str_bboxes=True):
        """ 返回对象的坐标信息
        Args:
        Returns:
        Raises:
        """
        if format_ == 'bboxes':
            empty_locations = [{'bboxes': '[]', 'page_no': -1, 'page_size': '[]'}]
            if self == '':
                return empty_locations
            locations = []
            for part in self.parts:
                if isinstance(part, LocationTextPart):
                    locations = self._merge_location(locations, part.get_old_locations())
            if str_bboxes:
                for location in locations:
                    location['bboxes'] = str(location['bboxes'])
            return locations

        locations = []
        if self == '':
            locations = []
        else:
            locations = []
            for part in self.parts:
                if isinstance(part, LocationTextPart):
                    part_location = part.get_locations()
                    if part_location:
                        locations.append(part_location)
        return locations

    def to_str(self):
        """ 对象以字符串格式输出文本数据
        Args:
        Returns:
            str: 本文数据内容
        Raises:
        """
        if self.print_no_location_chars:
            return self._print_no_location_str()
        return str(self)

    def jsonify(self, **kwargs):
        """
        json格式化接口
        """
        format_ = kwargs.get('location_format', '')

        if self.without_location:
            return self._print_no_location_str() if self.print_no_location_chars else str(self)

        chars = self.to_str()
        locations = self.get_locations(format_=format_)
        for location in locations:
            # TODO 位置信息格式兼容
            if 'bboxes' in location:
                location['bboxes'] = str(location['bboxes'])
        return {
            'chars': chars,
            'locations': locations
        }
