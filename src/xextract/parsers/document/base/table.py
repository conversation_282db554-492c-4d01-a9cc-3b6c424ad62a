#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Author: lixiaobing
Date: 2021/02/09
Desc: 表格数据封装类
"""
import json
from collections import defaultdict
from typing import Optional

from ....utils.utils import str_join
from ..location.location_text_part import LocationTextPart
from ..location.location_text import LocationText


class Cell:
    """
    Cell 单元格类
    """

    def __init__(
        self, data, row_index, row_span,
        col_index, col_span,
        page_no,
        page_size
    ) -> None:
        self.row_index = row_index
        self.row_span = row_span
        self.col_index = col_index
        self.col_span = col_span
        self.page_size = page_size
        self.page_no = page_no
        self.__bbox = None
        self.org_data = data
        self.merged = data.get('merged', False)
        self.merged_cells = []
        self.location_data = self._parser_json(data, page_size)

    @property
    def data(self):
        return str(self.location_data)

    @property
    def text(self):
        return str(self.location_data)

    @property
    def cell_data(self):
        return {
            'row_index': self.row_index,
            'col_index': self.col_index,
            'row_span': self.row_span,
            'col_span': self.col_span,
            'text': self.data
        }

    @property
    def bbox(self):
        return self.__bbox

    def __getitem__(self, attr):
        return getattr(self, attr)

    def _parser_json(self, cell, page_size):
        if cell.get('merged', False):
            self.merged = cell.get('merged', False)
            mcellspans = []
            for mcell in cell['merged_cells']:
                self.merged_cells.append(mcell)
                mcellspans.append(
                    LocationText.from_one_part(
                        text=mcell.get('text', ''),
                        bbox=mcell['bbox'],
                        page_index=mcell['page_number'],
                        spans=mcell['spans'],
                        page_size=page_size
                    )
                )
            celltext = str_join('', mcellspans)
        else:
            self.merged_cells.append(cell)
            self.__bbox = cell['bbox']
            celltext = LocationText.from_one_part(
                text=cell.get('text', ''),
                bbox=cell['bbox'],
                page_index=self.page_no,
                spans=cell['spans'],
                page_size=page_size
            )
        return celltext

    def get_locations(self, format_=''):
        """
        获取单元格的坐标
        """

        rets = []
        for mcell in self.merged_cells:
            bbox = LocationTextPart._get_bbox(mcell['bbox'], length=1, with_width_height=False)
            if not rets:
                chars = self.data
            else:
                chars = ''
            rets.append(
                {
                    'bbox': bbox,
                    'chars': chars,
                    'page_no':  self.page_no,
                    'page_size': self.page_size
                }
            )
        return rets


class Table:
    """
    Table 类
    """

    CELL = 'cell'
    CELL_TYPE = 'cell_type'
    ROW_INDEX = 'row_index'
    COL_INDEX = 'col_index'
    ROW_NUM = 'row_num'
    COL_NUM = 'col_num'

    def __init__(self, cells, row_num, col_num, page_number, bbox, merge_tables=None, page_size=None):
        """
        data为数据
        merge_info为表格的merge信息
        unit为表格的单位
        """
        self.__data = None
        self.__merge_info = None
        self.__bbox = None
        self.cell_data = None
        self.cell_org = None
        self.page_size = page_size if page_size else [0, 0, 0, 0]
        # TODO 参数验证
        self.row_num = row_num
        self.col_num = col_num
        self.page_number = page_number
        self.merge_table = merge_tables if merge_tables else []
        self.cells = []
        self.bbox = bbox

        self._create_data(cells, page_number, page_size)

    def _create_data(self, cells, page_no, page_size):
        """
        将一维的单元格转化为二维数组
        """

        data = [[''] * self.col_num for _ in range(self.row_num)]
        cell_data = [[''] * self.col_num for _ in range(self.row_num)]
        cell_org = [list([]) for _ in range(self.row_num)]
        merge = []
        for cell in cells:
            row_span, col_span = cell['row_span'], cell['col_span']
            row_idx, col_idx = cell['row_index'], cell['col_index']
            if 'page_number' in cell:
                page_no = cell['page_number']

            cell_obj = Cell(
                data=cell,
                row_index=row_idx, row_span=row_span,
                col_index=col_idx, col_span=col_span,
                page_no=page_no,
                page_size=page_size
            )
            self.cells.append(cell_obj)
            cell_org[row_idx].append(cell_obj)
            for i in range(row_span):
                for j in range(col_span):
                    data[row_idx + i][col_idx + j] = cell_obj.data
                    cell_data[row_idx + i][col_idx + j] = cell_obj

            if row_span > 1 or col_span > 1:
                merge.append(
                    [
                        row_idx,
                        row_idx + row_span - 1,
                        col_idx,
                        col_idx + col_span - 1
                    ]
                )
        self.__data = data
        self.__merge_info = merge
        self.cell_data = cell_data
        self.cell_org = cell_org

    @property
    def two_dim_data(self):
        return self.__data

    @property
    def data(self):
        cells_json = []
        for cell in self.cells:
            cells_json.append(cell.cell_data)
        json_data = {
            "row_num": self.row_num,
            "col_num": self.col_num,
            "cells": cells_json
        }
        return json.dumps(json_data, ensure_ascii=False)

    @property
    def bbox(self):
        """
        四个数字，分别为表格左下角和右上角的坐标
        """
        # TODO apiserver合并后的表格如何处理
        return self.__bbox

    @bbox.setter
    def bbox(self, value):
        if not isinstance(value, list):
            raise ValueError(f'{value} is not a currect bbox value')
        self.__bbox = value

    @property
    def page_range(self):
        if self.merge_table:
            range_ = set()
            for mtable in self.merge_table:
                range_.add(mtable['page_number'])
            return range_
        return set([self.page_number])

    @property
    def merge_info(self):
        return self.__merge_info

    @property
    def column_lines(self):
        column_lines = []
        for cell in self.cells:
            left, _, right, _ = cell.bbox
            column_lines.append(left)
            column_lines.append(right)
        return sorted(list(set(column_lines)))

    def __repr__(self) -> str:
        first_row = str_join('|', self.data[0])
        return f'Table({first_row})'

    def __str__(self) -> str:
        return str(self.to_markdown())

    def get_row_iter(self, index: Optional[int] = None):
        """
        获取表格的每一行
        param: index: 指定行的索引, 从0开始
        return:
        """
        if index:
            yield self.data[index]
            raise StopIteration()
        for row in self.data:
            yield row

    def get_col_iter(self, index: Optional[int] = None):
        """
        获取表格的每一列
        param: index: 指定列的索引,从0开始
        return:
        """
        transpose_data = list(zip(*self.data))
        if index:
            yield list(transpose_data[index])
            raise StopIteration()
        for row in transpose_data:
            yield list(row)

    def get_cell(self, row_i: int, col_i: int):
        """
        param: row_i: 行索引
        param: col_i: 列索引
        return:
        """
        return self.cell_data[row_i][col_i]

    def get_cell_iter(self, direct: str = 'row'):
        """
        param:
        return:
        """
        if direct == 'row':
            rows = self.rows
            cols = self.cols
            data = self.data
        else:
            rows = self.cols
            cols = self.rows
            data = list(zip(*self.data))
        for i in range(rows):
            for j in range(cols):
                yield data[i][j]

    def get_cell_index_iter_by_text(self, text: str):
        """
        param: text: 单元格内容
        return:
        """
        for i in range(self.rows):
            for j in range(self.cols):
                # 考虑到标注内容可能是单元格中部分内容
                if text in self.data[i][j]:
                    yield [i, j]

    @classmethod
    def create_single_table(cls, table_json, page_no, page_size):
        """
        通过解析表格json创建对象
        Args:
            table_json: 解析输出的表格json
            page_size: 页面坐标范围，用于还原原文坐标
        Returns: Table实例
        Raises:
        """
        if table_json.get('merged', False):
            merge_tables = table_json.get('merged_tables', [])
        else:
            merge_tables = [table_json]
        return cls(
            cells=table_json['cells'],
            row_num=table_json['row_num'],
            col_num=table_json['col_num'],
            merge_tables=merge_tables,
            page_number=page_no,
            page_size=page_size
        )

    @classmethod
    def create_from_json(cls, table_json, page_size):
        """
        通过解析表格json创建对象
        Args:
            table_json: 解析输出的表格json
            page_size: 页面坐标范围，用于还原原文坐标
        Returns: Table实例
        Raises:
        """
        if table_json.get('merged', False):
            merge_tables = table_json.get('merged_tables', [])
        else:
            merge_tables = [table_json]
        return cls(
            cells=table_json['cells'],
            row_num=table_json['row_num'],
            col_num=table_json['col_num'],
            merge_tables=merge_tables,
            page_number=table_json['page_number'],
            bbox=table_json.get('bbox', []),
            page_size=page_size
        )

    def transpose(self):
        """
        表格转置
        返回转置后结果，原始数据不变
        """
        new_cells = []
        for item in zip(*self.data):
            tmp_row = []
            for cell in item:
                cell[self.ROW_INDEX], cell[self.COL_INDEX] = cell[self.COL_INDEX], cell[self.ROW_INDEX]
                tmp_row.append(cell)
            new_cells.append(tmp_row)
        return new_cells

    def jsonify_old(self):
        return {
            'data': self.data,
            'merge_info': self.merge_info,
            'bbox': self.bbox
        }

    def jsonify(self, **kwargs):
        lineitem_format = kwargs.get('lineitem_format', '')
        location_foramt = kwargs.get('location_format', '')
        if lineitem_format in ['general', 'value_context']:
            return {
                'chars': self.to_str(),
                'locations': self.get_locations(format_=location_foramt)
            }
        else:
            return self.jsonify_old()

    def to_str(self, format_='html'):
        if format_ == 'html':
            return self.to_html()
        else:
            return self.to_markdown()

    def to_markdown(self):
        """
        渲染markdwon格式的表格
        """

        lines = []
        for i, row in enumerate(self.two_dim_data):
            if i == 1:
                header_split = [ '---' ] * self.col_num
                lines.append(
                    '|' + str_join('|', header_split) + '|'
                )
            lines.append(
                '|' + str_join('|', row) + '|'
            )
        return str_join('\n', lines)

    @staticmethod
    def __to_html_wrap_around(content_str):
        return fr'<table border="1", cellspacing="0", cellpadding="0">{content_str}</table>'

    @staticmethod
    def __to_html_single_table(cells):
        rows = defaultdict(list)
        for cell in cells:
            row_index = cell['row_index']
            rows[row_index].append(cell)
        trs = []
        for row in rows.values():
            tds = []
            for col in row:
                start = '<td rowspan="{}" colspan="{}">'.format(col['row_span'], col['col_span'])
                text = col['text']
                end = r'</td>'
                cell = start + text + end
                tds.append(cell)
            td = ''.join(tds)
            trs.append(fr'<tr>{td}</tr>')
        trs_ht = ''.join(trs)
        return trs_ht

    def to_html(self):
        # 合并的表格的行序会乱，分派以修正
        show_merged = False
        if show_merged and self.merge_table:
            table_contents = []
            for table_dict in self.merge_table:
                cells = table_dict['cells']
                table_contents.append(self.__to_html_wrap_around(self.__to_html_single_table(cells)))
            return ''.join(table_contents)
        else:
            cells = self.cells
            return self.__to_html_wrap_around(self.__to_html_single_table(cells))

    def get_bboxes(self, data):
        bbox = LocationTextPart._get_bbox(data['bbox'], length=1, with_width_height=True)
        return bbox

    @classmethod
    def _merge_location(cls, old_locations, new_location):

        if not new_location:
            return old_locations

        if not old_locations:
            old_locations.append(new_location)
            return old_locations

        last_old_location = old_locations[-1]
        if last_old_location['page_no'] != new_location['page_no']:
            old_locations.append(new_location)
            return old_locations

        last_bbox = last_old_location['bboxes'][-1]
        first_bbox = new_location['bboxes'][0]

        _, bottom1, left1, top1, width1, height1 = last_bbox
        right2, bottom2, left2, top2, width2, height2 = first_bbox

        if [left1, top1, bottom1, width1, height1] == [right2, top2, bottom2, width1, height2]:
            old_locations[-1]['bboxes'][-1][2] = left2
            old_locations[-1]['bboxes'].extend(new_location['bboxes'][1:])
        else:
            old_locations[-1]['bboxes'].extend(new_location['bboxes'])
        return old_locations

    def get_locations(self, format_='', str_bboxes=True):

        if format_ == 'bboxes':
            locations = []
            for each_location in self.__get_locations(format_=format_):
                self._merge_location(locations, each_location)
            if str_bboxes:
                for location in locations:
                    location['bboxes'] = str(location['bboxes'])
            return locations
        else:
            return self.__get_locations(format_=format_)

    def __get_locations(self, format_=''):
        """
        计算表格的完整bbox
        """
        rets = []
        if format_ == 'bboxes':
            for mtable in self.merge_table:
                bbox = LocationTextPart._get_bbox(mtable['bbox'], length=1, with_width_height=True)
                rets.append(
                    {
                        'bboxes': [bbox],
                        'page_no':  mtable['page_number'],
                        'page_size': str(self.page_size[2:])
                    }
                )
        else:
            for mtable in self.merge_table:
                bbox = LocationTextPart._get_bbox(mtable['bbox'], length=1, with_width_height=False)
                if not rets:
                    chars = self.to_str()
                else:
                    chars = ''
                rets.append(
                    {
                        'bbox': bbox,
                        'chars': chars,
                        'page_no':  mtable['page_number'],
                        'page_size': self.page_size
                    }
                )
        return rets

    def is_empty_row(self):
        for row in self.data:
            if any(row):
                break
        else:
            return True
        return False
