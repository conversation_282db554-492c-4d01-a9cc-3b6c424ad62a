#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Author: lixiaobing
Date: 2021/11/05
Desc:
"""
from ..location.location_text_part import LocationTextPart


class Figure:
    """
    图片
    """

    def __init__(self, bbox, page_no, filename, page_size) -> None:
        self.bbox = bbox
        self.page_no = page_no
        self.filename = filename
        self.page_size = page_size
        self.desc: str = None

    def to_str(self):
        # TODO 图片字符串输出接口
        return self.filename

    def get_locations(self, format_='', str_bboxes=True):
        """
        计算图片坐标
        """
        rets = []
        if format_ == 'bboxes':
            bbox = LocationTextPart._get_bbox(self.bbox, length=1, with_width_height=True)
            rets.append(
                {
                    'bboxes': [bbox],
                    'page_no':  self.page_no,
                    'page_size': str(self.page_size[2:])
                }
            )
        else:
            bbox = LocationTextPart._get_bbox(self.bbox, length=1, with_width_height=False)
            rets.append(
                {
                    'bbox': bbox,
                    'chars': self.filename,
                    'page_no':  self.page_no,
                    'page_size': self.page_size
                }
            )
        return rets
