#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Author: lixiaobing
Date: 2021/09/24
Desc:
"""
from typing import Dict
from statistics import mean

from ..location.location_text import LocationText
from .table import Table
from .figure import Figure


class LineInfo:
    """
    文本元素类
    """

    def __init__(
        self,
        text: str,
        data: Dict,
        page_no: int,
        page_bbox=None,
        type_: str = '',
        page=None,
        is_paragraph_start: bool = True
    ) -> None:
        self.id_: int = -1
        self.text = text
        self.__data = data
        self.type = type_
        self.page_no = page_no
        self.page_bbox = page_bbox if page_bbox else None
        self.is_paragraph_start = is_paragraph_start
        self.page = page

    def __repr__(self) -> str:
        classname = type(self).__name__
        return '{}(text={},par_start={},type={},page_no={})'.format(
            classname,
            self.text,
            self.is_paragraph_start,
            self.type,
            self.page_no
        )

    def __str__(self) -> str:
        return repr(self)

    @property
    def id(self):
        if self.id_ == -1:
            raise Exception('id 未设置')
        return self.id_

    @id.setter
    def id(self, value: int):
        if not isinstance(value, int):
            raise Exception(f'{value} 类型错误')
        self.id_ = value

    @property
    def data(self):
        if isinstance(self.__data, str):
            return self.__data

        tmp = {}
        tmp.update(self.__data)
        tmp['page_number'] = self.page_no
        tmp['page_size'] = self.page_bbox
        return tmp

    @property
    def data_object(self):
        if self.type == 'table':
            return Table.create_from_json(self.data, self.page_bbox)
        elif self.type in ['text', 'catlog', 'title', 'header', 'footer']:
            return self.lineinfo2loctext(self.data)
        elif self.type in ['figure']:
            return self.lieninfo2figure(self.data)

    @property
    def bbox(self):
        if not self.__data:
            return [0, 0, 0, 0]
        return self.__data['bbox']

    @property
    def font_size(self):
        if self.type in ['table']:
            return self.bbox[-1] - self.bbox[1]
        fonts = set()
        for span in self.__data['spans']:
            sbbox = span['bbox']
            fonts.add((sbbox[-1]-sbbox[1]))
        return mean(fonts)

    def __getitem__(self, attr: str):
        return getattr(self, attr)

    def __setitem__(self, attr: str, value):
        return setattr(self, attr, value)

    @property
    def is_catalogue(self):
        return self.type == 'catlog'

    def set_is_catalogue(self):
        self.type = 'catlog'

    def is_text(self):
        return self.type == 'text'

    def is_title(self):
        return self.type == 'title'

    def is_table(self):
        return self.type == 'table'

    def is_figure(self):
        return self.type == 'figure'

    def is_mid(self):
        left, _, right, _ = self.bbox
        _, _, pr, _ = self.page_bbox
        return ((left + right) / 2) - (pr/2) < 3

    def get_top(self):
        return self.__data['bbox'][-1]

    def get_right(self):
        return self.__data['bbox'][-2]

    def get_bottom(self):
        return self.__data['bbox'][1]

    def get_left(self):
        return self.__data['bbox'][0]

    def char_align(self):

        pass

    def lieninfo2figure(self, line_data):
        return Figure(
            bbox=line_data['bbox'],
            page_no=line_data['page_number'],
            filename=line_data['filename'],
            page_size=line_data['page_size']
        )

    def lineinfo2loctext(self, line_data):
        if isinstance(line_data, str):
            ltext = LocationText.from_string(line_data)
        else:
            ltext = LocationText.from_one_part(
                text=line_data.get('text', ''),
                bbox=line_data['bbox'],
                page_index=line_data['page_number'],
                spans=line_data['spans'],
                page_size=self.page_bbox
            )
        return ltext

    def jsonify(self):
        return self.__data
