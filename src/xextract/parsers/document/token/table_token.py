#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Author: lixiaobing
Date: 2021/10/19
Desc:
"""
from typing import List

from ....utils.utils import str_join

from ..constant import DocAttr
from .text_token import TextToken
from ..base.table import Table


class TableToken(TextToken):

    type = DocAttr.TABLE

    def __init__(
        self,
        id_: int,
        page_size: List,
        page_no: int,
        data: List = None,
        title_level: int = -1,
        title_path: List = None,
        without_location: bool = False,
        title_path_id: List = None,
        title_path_no: List = None,
        doc=None
    ) -> None:
        super().__init__(
            id_,
            page_size,
            page_no,
            data=data,
            title_level=title_level,
            title_path=title_path,
            without_location=without_location,
            title_path_id=title_path_id,
            title_path_no=title_path_no,
            doc=doc
        )
        self.__org_data: List = []

    @property
    def line(self):
        return str(self.location_data.desc)

    @property
    def data_str(self):
        return str(self.location_data.to_str())

    @property
    def textline(self):
        rets = []
        for row in self.location_data.two_dim_data:
            for col in row:
                rets.append(str(col))
        return rets

    @property
    def location_textline(self):
        rets = []
        for row in self.location_data.cell_data:
            tmp = []
            for col in row:
                tmp.append(col.location_data)
            row = str_join('', tmp)
            rets.append(row)
        return rets

    @property
    def data(self):
        return self.location_data.data
        # rets = []
        # for row in self.location_data.data:
        #     tmp = []
        #     for col in row:
        #         tmp.append(str(col))
        #     rets.append(tmp)
        # return json.dumps(rets, ensure_ascii=False)

    @property
    def location_data(self):
        return self.__data[0]

    @property
    def page_range(self):
        return self.location_data.page_range

    @data.setter
    def data(self, data_dict):
        if not data_dict:
            self.__data = []
        elif isinstance(data_dict, Table):
            self.__data = [data_dict]
        else:
            raise Exception('表格数据初始化未实现')

    @property
    def bbox_bottom(self):
        return self.__data.bbox[1]

    def to_html(self, spans=False):
        return self.location_data.to_html()

    def to_markdown(self, spans=False):
        return self.location_data.to_markdown()

    def set_data_from_lineinfo(self, line_dict, page_no, org_data):
        self.__data.append(line_dict)
        self.__org_data.append(org_data)
        self._page_range.add(page_no)

    @classmethod
    def cal_bbox2value(cls, location_data, evalue, location_format='bbox', strict=False):
        """
        表格内容计算坐标
        """
        if not isinstance(location_data, Table):
            raise TypeError('input object is not Table')
        # 根据提取输出是否为二维list，判断时整张表
        if isinstance(evalue['value'], str) and 'cells' in evalue['value']:
            evalue['spans'] = location_data.get_locations(location_format)
            return evalue
        cell = location_data.get_cell(evalue['row_index'], evalue['col_index'])
        value = evalue['value']
        if strict and value != str(cell.location_data[evalue['start']:evalue['end']]):
            raise Exception(f'{value} 坐标索引异常')
        if str(cell.location_data[evalue['start']:evalue['end']]) == '' or value == cell.data:
            # TODO 输出单元格坐标，等待底层接口开发完成
            locations = cell.get_locations(location_format)
        else:
            locations = cell.location_data[evalue['start']:evalue['end']].get_locations(location_format)
        evalue['spans'] = locations
        return evalue

    def format_data(self):
        data = {
            # DocAttr.TABLE: self.location_data,
            DocAttr.DATA: self.data,
            DocAttr.CONTENT: self.data,
            DocAttr.PAGE_RANGE: sorted([i for i in self.page_range]),
            DocAttr.DOC_DATA: self.__org_data
        }
        return data
