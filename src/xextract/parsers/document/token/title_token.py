#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Author: lixiaobing
Date: 2021/09/24
Desc: 标题
"""
from typing import List

from .text_token import TextToken
from ..constant import DocAttr


class TitleToken(TextToken):

    type = DocAttr.TITLE

    def __init__(
        self,
        id_: int,
        page_size: List,
        page_no: int,
        data: List = None,
        title_level: int = -1,
        title_path: List = None,
        without_location: bool = False,
        title_path_id: List = None,
        title_path_no: List = None,
        doc=None
    ) -> None:
        super().__init__(
            id_,
            page_size,
            page_no,
            data=data,
            title_level=title_level,
            title_path=title_path,
            without_location=without_location,
            title_path_id=title_path_id,
            title_path_no=title_path_no,
            doc=doc
        )
        self.__no = ''

    @property
    def no(self):
        return self.__no

    @no.setter
    def no(self, value):
        self.__no = value

    def to_html(self, spans=False, withbr=False):
        if spans:
            lines = []
            for line in self.__data:
                lines.append(line.spans_str('   '))
        else:
            lines = self.textline
        if withbr:
            data = '<br>'.join(lines)
        else:
            data = ''.join(lines)
        return f"<h{len(self.title_path)} id={self.id}>{data}</h{len(self.title_path)}>"
    
    def to_markdown(self, spans=False):
        return '#'* self.title_level + ' ' + self.data_str

    def format_data(self):
        title_path = []
        title_path.extend(self.title_path)
        title_path.append(self.data)
        title_path_no = []
        title_path_no.extend(self.title_path_no)
        title_path_no.append(self.no)
        return {
            DocAttr.TEXT: self.line,
            DocAttr.CONTENT: self.data,
            DocAttr.DATA: self.data,
            DocAttr.DOC_DATA: self.org_data,
            DocAttr.TITLE_PATH: title_path,
            DocAttr.TITLE_PATH_NO: title_path_no,
        }
