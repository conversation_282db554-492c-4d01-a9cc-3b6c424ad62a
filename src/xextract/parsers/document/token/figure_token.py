#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Author: lixiaobing
Date: 2021/10/19
Desc:
"""
from typing import List

from ..constant import DocAttr
from .text_token import TextToken


class FigureToken(TextToken):

    type = DocAttr.FIGURE

    def __init__(
        self,
        id_: int,
        page_size: List,
        page_no: int,
        data: List = None,
        title_level: int = -1,
        title_path: List = None,
        without_location: bool = False,
        title_path_id: List = None,
        title_path_no: List = None,
        doc=None
    ) -> None:
        super().__init__(
            id_,
            page_size,
            page_no,
            data=data,
            title_level=title_level,
            title_path=title_path,
            without_location=without_location,
            title_path_id=title_path_id,
            title_path_no=title_path_no,
            doc=doc
        )
        self.__org_data: List = []

    @property
    def line(self):
        return str(self.data.desc)

    @property
    def textline(self):
        return self.__data[0]

    @property
    def location_data(self):
        return self.__data[0]

    @property
    def data_str(self):
        return self.data

    @property
    def data(self):
        return self.__data[0].filename

    @data.setter
    def data(self, data_dict):
        if not data_dict:
            self.__data = []
        else:
            raise Exception('表格数据初始化未实现')

    @property
    def bbox_bottom(self):
        return self.__data.bbox[1]

    def to_html(self, spans=False):
        return rf"<p>{self.data_str}<\p>"

    def set_data_from_lineinfo(self, line_dict, page_no, org_data):
        self.__data.append(line_dict)
        self.__org_data.append(org_data)
        self._page_range.add(page_no)

    @classmethod
    def cal_bbox2value(cls, location_data, evalue, location_format='bbox', strict=False):
        """
        返回图片坐标
        """
        locations = location_data.get_locations(location_format)
        evalue['spans'] = locations
        return evalue

    def format_data(self, without_location: bool = False):
        data = {}
        data[DocAttr.FIGURE] = self.data
        data[DocAttr.CONTENT] = self.data
        data[DocAttr.PAGE_RANGE] = [i for i in self.page_range]
        data[DocAttr.DOC_DATA] = self.__org_data
        return data
