#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Author: lixiaobing
Date: 2021/10/19
Desc:
"""
from typing import List, Set
from ....utils.utils import str_join

from ..constant import DocAttr
from ..location.location_text import LocationText


class TextToken:

    type = DocAttr.TEXT

    def __init__(
        self,
        id_: int,
        page_size: List = None,
        page_no: int = -1,
        data: List = None,
        title_level: int = -1,
        title_path: List = None,
        without_location: bool = False,
        title_path_id: List = None,
        title_path_no: List = None,
        doc=None
    ) -> None:
        self.lineinfos = []
        self._page_range: Set = set()
        self.__data: List = []
        self.doc = doc

        self.id = id_
        self.title_level = title_level
        self.__page_size: List = page_size
        self.__org_data: List = []
        self._parent = None
        self.without_location = without_location
        self._title_path = title_path if title_path else []
        self.title_path_id = title_path_id if title_path_id else []
        self.title_path_no = title_path_no if title_path_no else []
        self.page_no = page_no
        self.data = data if data else []
        self.merged_line: List = []
        self.children: List = []

    def __repr__(self) -> str:
        classname = type(self).__name__
        return '{}(id={},type={},title_level={},lines={})'.format(
            classname,
            self.id,
            self.type,
            self.title_level,
            len(self.__data)
        )

    def __str__(self) -> str:
        return repr(self)

    def __getitem__(self, attr: str):
        return getattr(self, attr)

    def __setitem__(self, attr: str, value):
        return setattr(self, attr, value)

    @property
    def content_ids(self):
        contents = [self.id]
        contents.extend(self.children)
        return contents

    @property
    def data_str(self):
        return str(str_join('', self.__data))

    @property
    def data(self):
        return str(str_join('', self.__data))

    @property
    def location_data(self):
        return str_join('', self.__data)

    @data.setter
    def data(self, data_dict):
        if not data_dict:
            self.__data = []
        elif isinstance(data_dict, str):
            self.__data = [data_dict]
        else:
            for part in data_dict['locations']:
                self.__data.append(LocationText.from_part_json(**part))

    @property
    def title_level(self):
        if self.__title_level:
            return self.__title_level
        return len(self.title_path_id) + 1

    @property
    def title_path(self):
        return self._title_path

    @title_path.setter
    def title_path(self, title_path):
        self._title_path = title_path

    @title_level.setter
    def title_level(self, value: int):
        self.__title_level = value

    def set_level(self, value: int):
        self.__title_level = value

    @property
    def page_no(self):
        return self.__page_no

    @page_no.setter
    def page_no(self, value: int):
        self.__page_no = value

    @property
    def page_size(self):
        return self.__page_size

    @property
    def coord_left(self):
        assert self.__data
        return self.__data[0].bbox[0]

    @property
    def coord_right(self):
        assert self.__data
        return self.__data[0].bbox[3]

    @property
    def parent(self):
        return self._parent
        # return self.title_path_id[-1]


    @parent.setter
    def parent(self, parent):
        self._parent = parent
        # return self.title_path_id[-1]

    @property
    def line(self):
        return str(self.data)

    @property
    def textline(self):
        return [str(line) for line in self.__data]

    @property
    def location_textline(self):
        return self.__data

    @location_textline.setter
    def location_textline(self, data):
        self.__data = data

    @property
    def bbox(self):
        assert self.data
        if isinstance(self.data, str):
            return [0, 0, 0, 0]
        return self.data[0].bbox

    @property
    def org_data(self):
        return self.__org_data

    @org_data.setter
    def org_data(self, value):
        self.__org_data = value

    @property
    def page_range(self):
        return self._page_range

    @page_range.setter
    def page_range(self, prange):
        self._page_range = prange

    @property
    def bbox_bottom(self):
        if isinstance(self.__data, LocationText):
            return self.__data.bottom()
        else:
            raise Exception(f'{type(self.__data)} 未支持获取文本块下坐标')

    def is_title(self):
        return self.type == 'title'

    def is_text(self):
        return self.type == 'text'

    @property
    def is_catalogue(self):
        return self.type == 'catlog'

    def to_html(self, spans=False, withbr=False):
        if spans:
            lines = []
            for line in self.__data:
                lines.append(line.spans_str('   '))
        else:
            lines = self.textline
        if withbr:
            data = '<br>'.join(lines)
        else:
            data = ''.join(lines)
        return f"<p id={self.id}>{data}</p>"

    def to_markdown(self, spans=False):
        return self.data_str

    def set_data_from_lineinfo(self, line_dict, page_no, org_data):
        self.__data.append(line_dict)
        self.__org_data.append(org_data)
        self._page_range.add(page_no)

    def merge_lineinfo(self, lineinfo):
        self.lineinfos.append(lineinfo)

    @classmethod
    def cal_bbox2value(cls, location_data, evalue, location_format='bbox', strict=False):
        """
        表格内容计算坐标
        """
        if not isinstance(location_data, LocationText):
            raise TypeError('input object is not LocationText')
        value = evalue['value']
        if strict and value != str(location_data[evalue['start']:evalue['end']]):
            raise Exception(f'{value} 坐标索引异常')
        locations = location_data[evalue['start']:evalue['end']].get_locations(location_format)
        evalue['spans'] = locations
        return evalue

    def format_data(self):
        return {
            DocAttr.TEXT: self.line,
            DocAttr.CONTENT: self.data,
            DocAttr.DATA: self.data,
            DocAttr.DOC_DATA: self.org_data,
        }

    def jsonify(self):
        data = self.format_data()
        ret = {
            DocAttr.ID: self.id,
            DocAttr.TYPE: self.type,
            DocAttr.PAGE_RANGE: sorted([i for i in self.page_range]),
            DocAttr.TITLE_PATH_NO: self.title_path_no,
            DocAttr.TITLE_PATH_ID: self.title_path_id,
            DocAttr.TITLE_PATH: self.title_path,
        }
        ret.update(data)
        return ret
