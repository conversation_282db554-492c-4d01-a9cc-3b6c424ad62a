#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Author: lixiaobing
Date: 2021/09/24
Desc: 标题
"""
from typing import List

from ..constant import DocAttr
from .text_token import TextToken


class FooterToken(TextToken):
    """
    页脚
    """

    type = DocAttr.FOOTER

    def __init__(
        self,
        id_: int,
        page_size: List,
        page_no: int,
        data: List = None,
        title_level: int = -1,
        title_path: List = None,
        without_location: bool = False,
        title_path_id: List = None,
        doc=None
    ) -> None:
        super().__init__(
            id_,
            page_size,
            page_no,
            data=data,
            title_level=title_level,
            title_path=title_path,
            without_location=without_location,
            title_path_id=title_path_id,
            doc=doc
        )
