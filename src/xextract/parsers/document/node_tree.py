#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@File    : node_tree.py
<AUTHOR> lhh
@Date    : 2025-05-12 01:21:17
@Desc    : doc.json转为节点树.
          1. 输入doc.json文件
"""


from collections import defaultdict
import json
from typing import Dict, Any

from pandas import DataFrame

from .constant import Const
from .token import TextToken


class DocJson(Const):
    ROOT = "root"
    ROOTNODE = "RootNode"
    PARENT_PATH = "parent_path"
    ID = "id"
    TREE = "tree"
    TABLE = "table"
    TEXTLINES = "textlines"
    NUMBER = "number"
    PAGE_NUMBER = "page_number"
    TYPE = "type"
    HEADER = "header"
    FOOTER = "footer"
    SECTION = "section"
    TITLE = "title"
    TEXT = "text"
    FIGURE = "figure"
    DATA = "data"
    CHILDREN = "children"
    PAGES = "pages"
    TABLES = "tables"
    BBOX = "bbox"
    BODY = "body"
    BLOCK = "blocks"
    COVER = "<封面>"
    MAINTEXT = "<正文>"


class DocAttr(Const):
    """
    doc.json属性字面量
    """

    META = "doc_meta"
    NAME = "json_filename"
    ID = "id"
    TYPE = "type"
    TITLE = "title"
    TABLE = "table"
    TEXT = "text"
    HEADER = "header"
    FOOTER = "footer"
    CHAPTER = "chapter"
    FIGURE = "figure"
    CATLOG = "catlog"
    PAGE_RANGE = "page_range"
    TITLE_PATH = "title_path"
    TITLE_PATH_ID = "title_path_id"
    TITLE_PATH_NO = "title_path_no"
    CONTENT = "content"
    DATA = "data"
    DESCRIPTIONS = "descriptions"
    DOC_DATA = "doc_data"
    TOKEN = "token"
    TABLE_HTML = "table_html"


class NodeTree:
    """docstring for NodeTree."""

    def __init__(
        self,
        json_filename: str,
        doc_meta: Dict[str, Any],
        pdf_info: Dict[str, Any] = {},
        fonts: Dict[str, Any] = {},
        debug: bool = False,
        with_location: bool = True,
    ):
        if not doc_meta:
            self.doc_meta = {}
        elif not isinstance(doc_meta, dict):
            raise ValueError(f"doc_meta{type(doc_meat)} type error!")
        else:
            self.doc_meta = doc_meta

        self.pdf_info = pdf_info
        self.debug = debug
        self.with_location = with_location
        self.json_filename = json_filename
        self.pages: list = []
        self.tokens: list[TextToken] = []
        self.header_footer = []
        self.fonts = fonts
        self.outline = {}

    def doc_to_dict(self):
        """convert doc to dict type"""

        feat_cols = [
            DocAttr.PAGE_RANGE,
            DocAttr.ID,
            DocAttr.TYPE,
            # DocAttr.TITLE_PATH_ID,
            # DocAttr.TITLE_PATH_NO,
            DocAttr.TITLE_PATH,
            DocAttr.CONTENT,
            # DocAttr.DOC_DATA
        ]
        data = defaultdict(list)
        for token_item in self.tokens:
            if token_item.id < 1:
                continue

            for meta_col_name in set(["doc_id", "pdf_url"]) & set(self.doc_meta.keys()):
                data[meta_col_name].append(self.doc_meta.get(meta_col_name, ""))
            data[DocAttr.NAME].append(self.json_filename)
            item_data = token_item.jsonify()
            for key in feat_cols:
                if key not in item_data:
                    data[key].append(None)
                elif key in [DocAttr.DOC_DATA]:
                    data[key].append(item_data[key])
                else:
                    data[key].append(item_data[key])
            data[DocAttr.TOKEN].append(token_item)
            for token_item in self.header_footer:

                for meta_key in set(["doc_id", "pdf_url"]) & set(self.doc_meta.keys()):
                    data[meta_key].append(self.doc_meta.get(meta_key, ""))
                data[DocAttr.NAME].append(self.json_filename)
                item_data = token_item.jsonify()
                for key in feat_cols:
                    if key not in item_data:
                        data[key].append(None)
                    elif key in [DocAttr.DOC_DATA]:
                        data[key].append(item_data[key])
                    else:
                        data[key].append(item_data[key])
                data[DocAttr.TOKEN].append(token_item)
        return data

    def jsonify(self):
        """
        doc实例序列化为json
        """
        # TODO json序列化
        rets = []
        for token in self.tokens:
            rets.append(token.jsonify())
        return rets

    def to_db_json(self, json_filename=""):
        """解析数据转为JSON

        Args:
            json_filename (str, optional): _description_. Defaults to "".

        Returns:
            _type_: json
        """
        feat_cols = [
            DocAttr.PAGE_RANGE,
            DocAttr.ID,
            DocAttr.TYPE,
            # DocAttr.TITLE_PATH_ID,
            # DocAttr.TITLE_PATH_NO,
            DocAttr.TITLE_PATH,
            DocAttr.DATA,
            DocAttr.DOC_DATA,
        ]
        doc_elements = defaultdict(list)
        for token_item in self.tokens:
            if token_item.id < 1:
                continue
            item_data = token_item.jsonify()
            for key in feat_cols:
                if key not in item_data:
                    doc_elements[key].append(None)
                else:
                    doc_elements[key].append(item_data[key])
        for token_item in self.header_footer:
            item_data = token_item.jsonify()
            for key in feat_cols:
                if key not in item_data:
                    doc_elements[key] = None
                else:
                    doc_elements[key] = item_data[key]
        body = {
            "pdf_info": self.pdf_info,
            "fonts": self.fonts,
            "doc_outline": self.outline,
        }
        body.update(doc_elements)

        with open(json_filename, "w", encoding="utf-8") as f:
            f.write(json.dumps(body, ensure_ascii=False, indent=0))
        return body

    def get_doc_table(self):
        """
        结构化文档转换为DataTable接口
        """
        data = self.doc_to_dict()
        columns = list(data.keys())
        cell_data = []
        row_index = []
        each_column = []
        for col in columns:
            each_column.append(data[col])
        for i, row in enumerate(zip(*each_column)):
            row_index.append(i)
            cell_data.append(list(row))

        return DataFrame(data=cell_data, index=row_index, columns=columns)

    def ensure_dataframe(self, ret_dataframe):
        skip_columns = [
            DocAttr.TITLE_PATH_ID,
            DocAttr.TITLE_PATH_NO,
            DocAttr.DESCRIPTIONS,
            DocAttr.TOKEN,
            DocAttr.DOC_DATA,
        ]
        out_cols = []
        for col in ret_dataframe.columns:
            if col in skip_columns:
                continue
            out_cols.append(col)
        out_data = ret_dataframe[out_cols]
        return out_data

    def to_csv(self, csv_filename=""):
        df = self.get_doc_table()
        out_df = self.ensure_dataframe(df)
        out_df.to_csv(csv_filename)
        return out_df

    def to_md(self, md_filename=""):
        """
        doc.json转换成markdown输出，忽略坐标仅仅在乎文本内容
        """
        body = ""
        for token_item in self.tokens:
            if token_item.id < 1:
                continue
            body += token_item.to_markdown() + "\n\n"

        if md_filename:
            with open(md_filename, "w", encoding="utf-8") as f:
                f.write(body)
        return body

    def to_md_with_id(self, md_filename=""):
        """doc.json转换成markdown，并添加id"""
        body = ""
        for token_item in self.tokens:
            if token_item.id < 1:
                continue
            # 使用特殊符号来标识id
            body += f"「id:{token_item.id}」"
            body += token_item.to_markdown() + "\n\n"

        if md_filename:
            with open(md_filename, "w", encoding="utf-8") as f:
                f.write(body)
        return body
