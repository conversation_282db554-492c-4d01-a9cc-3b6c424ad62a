#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Author: lixiaobing
Date: 2023/04/25
Desc:
"""

from typing import Dict

from ..constant import DocAttr
from ..constant import DocJson
from ..base.lineinfo import LineInfo
from ..token import type2token
from ..node_tree import NodeTree
from ....utils.utils import load_json


def json_flunter_iter(node):
    """
    将tree中的节点打平
    将所有嵌套children提取到统一层次下
    """
    for child in node[DocJson.CHILDREN]:
        yield child
        for sub_child in json_flunter_iter(child):
            yield sub_child


class DocJsonAnalyzer:

    name = "pdf2doc"

    def __init__(self, settings=None) -> None:
        self.settings = settings
        pass

    def analyze(self, json_filename):

        doc_json = load_json(path=json_filename)
        doc: NodeTree = NodeTree(
            json_filename,
            doc_meta=doc_json.get(DocAttr.META, {}),
            pdf_info=doc_json.get(DocJson.PDF_INFO, {}),
            fonts=doc_json.get(DocJson.FONTS, {}),
        )
        pages = {}
        head_foot_items = []
        for page in doc_json[DocJson.PAGES]:
            for block in page[DocJson.BODY][DocJson.BLOCK]:
                block[DocJson.TEXTLINES] = []
                block[DocJson.TABLES] = []
            pages[page[DocJson.NUMBER]] = page
            if page.get(DocJson.HEADER, {}):
                item = page[DocJson.HEADER]
                item[DocJson.TYPE] = DocJson.HEADER
                item[DocJson.PAGE_NUMBER] = page[DocJson.NUMBER]
                head_foot_items.append(item)
            if page.get(DocJson.FOOTER, {}):
                item = page[DocJson.FOOTER]
                item[DocJson.PAGE_NUMBER] = page[DocJson.NUMBER]
                item[DocJson.TYPE] = DocJson.FOOTER
                head_foot_items.append(item)

        classtoken = type2token[DocJson.TITLE]
        root_token = classtoken(
            id_=0,
            title_level=0,
            page_no=0,
            page_size=[0, 0, 0, 0],
            data=DocJson.ROOTNODE,
        )

        token_objs = [root_token]

        # 关键：递归解析doc.json
        for token_dict in json_flunter_iter(doc_json[DocJson.TREE][DocJson.ROOT]):
            page = pages[token_dict[DocJson.PAGE_NUMBER]]
            page_size = page[DocJson.BBOX]
            title_path_id = token_dict[DocJson.PARENT_PATH]
            title_path = [token_objs[id_].data for id_ in title_path_id]
            title_path_no = [token_objs[id_].no for id_ in title_path_id]
            if token_dict[DocJson.TYPE] in [DocJson.SECTION, DocJson.TITLE]:
                if token_dict[DocJson.TYPE] == DocJson.SECTION:
                    token_class = type2token[DocJson.TEXT]
                else:
                    token_class = type2token[token_dict[DocJson.TYPE]]

                if token_dict[DocJson.DATA].get(DocJson.TEXT) in [
                    DocJson.COVER,
                    DocJson.MAINTEXT,
                ]:
                    new_token = token_class(
                        id_=token_dict[DocJson.ID],
                        page_no=token_dict[DocJson.PAGE_NUMBER],
                        page_size=page_size,
                        title_path=title_path,
                        title_path_id=title_path_id,
                        title_path_no=title_path_no,
                        doc=doc,
                    )
                    line = LineInfo(
                        text=token_dict[DocJson.DATA][DocJson.TEXT],
                        type_=DocJson.TEXT,
                        data=token_dict[DocJson.DATA].get(DocJson.TEXT),
                        page_bbox=page_size,
                        page_no=token_dict[DocJson.PAGE_NUMBER],
                    )
                    new_token.set_data_from_lineinfo(
                        line.data_object, line.page_no, line.data
                    )
                else:
                    new_token = token_class(
                        id_=token_dict[DocJson.ID],
                        page_no=token_dict[DocJson.PAGE_NUMBER],
                        title_path=title_path,
                        title_path_id=title_path_id,
                        title_path_no=title_path_no,
                        page_size=page_size,
                        doc=doc,
                    )
                if token_dict[DocJson.TYPE] == DocJson.TITLE:
                    new_token.set_level(len(token_dict[DocJson.PARENT_PATH]) + 1)
                    if token_dict[DocJson.DATA].get("no", {}):
                        no = token_dict[DocJson.DATA]["no"]["value"]
                        new_token.no = no

                for textline in token_dict[DocJson.DATA].get(DocJson.TEXTLINES, []):
                    page_number = textline[DocJson.PAGE_NUMBER]
                    line = LineInfo(
                        text=textline[DocJson.TEXT],
                        type_=DocJson.TEXT,
                        data=textline,
                        page_bbox=page_size,
                        page_no=page_number,
                    )
                    new_token.set_data_from_lineinfo(
                        line.data_object, line.page_no, line.data
                    )
                token_objs.append(new_token)
            elif token_dict[DocJson.TYPE] in [DocJson.TABLE]:
                token_class = type2token[token_dict[DocJson.TYPE]]
                new_token = token_class(
                    id_=token_dict[DocJson.ID],
                    page_no=token_dict[DocJson.PAGE_NUMBER],
                    title_path=title_path,
                    title_path_id=title_path_id,
                    title_path_no=title_path_no,
                    page_size=page_size,
                    doc=doc,
                )
                # new_token.set_level(-1)
                page_number = token_dict[DocJson.PAGE_NUMBER]
                line = LineInfo(
                    text=DocJson.TABLE,
                    type_=DocJson.TABLE,
                    data=token_dict[DocJson.DATA],
                    page_bbox=page_size,
                    page_no=token_dict[DocJson.PAGE_NUMBER],
                )
                line.id = token_dict[DocJson.ID]
                new_token.set_data_from_lineinfo(
                    line.data_object, line.page_no, line.data
                )
                token_objs.append(new_token)
            elif token_dict[DocJson.TYPE] in [DocJson.FIGURE]:
                token_class = type2token[token_dict[DocJson.TYPE]]
                page = pages[token_dict[DocJson.PAGE_NUMBER]]
                page_no = token_dict[DocJson.PAGE_NUMBER]
                new_token = token_class(
                    id_=token_dict[DocJson.ID],
                    page_no=page_no,
                    title_path=title_path,
                    title_path_id=title_path_id,
                    title_path_no=title_path_no,
                    page_size=page_size,
                    doc=doc,
                )
                # new_token.set_level(-1)
                page_number = token_dict[DocJson.PAGE_NUMBER]
                line = LineInfo(
                    text=DocJson.FIGURE,
                    type_=DocJson.FIGURE,
                    data=token_dict[DocJson.DATA],
                    page_bbox=page_size,
                    page_no=page_no,
                )
                line.id = token_dict[DocJson.ID]
                new_token.set_data_from_lineinfo(
                    line.data_object, line.page_no, line.data
                )
                token_objs.append(new_token)
        doc.tokens = token_objs

        for i, token_dict in enumerate(head_foot_items):
            id_ = len(doc.tokens) + i
            page = pages[token_dict[DocJson.PAGE_NUMBER]]
            page_number = token_dict[DocJson.PAGE_NUMBER]
            page_size = page[DocJson.BBOX]
            token_class = type2token[token_dict[DocJson.TYPE]]
            new_token = token_class(
                id_=id_,
                page_no=token_dict[DocJson.PAGE_NUMBER],
                page_size=page_size,
                doc=doc,
            )
            line = LineInfo(
                text=token_dict[DocJson.TEXT],
                type_=token_dict[DocJson.TYPE],
                data=token_dict,
                page_bbox=page_size,
                page_no=page_number,
            )
            new_token.set_data_from_lineinfo(line.data_object, line.page_no, line.data)
            doc.header_footer.append(new_token)
        return doc

    def print_doc(self):
        """
        按章节层级打印全文内容接口
        """
        indent = "    "
        content = ""
        for token_id in self.title_tokens:
            token = self.tokens[token_id]
            title_indent = indent * token.title_level
            title_data = []
            for i, textline in enumerate(token.textline):
                if i == 0:
                    title_data.append(f"{str(textline)}" + "\n")
                else:
                    title_data.append("++" + f"{str(textline)}" + "\n")
            if title_data:
                title_data = title_indent.join(title_data)
            else:
                title_data = f"{str(token.line)}" + "\n"
            content += title_indent + title_data
            for child_id in token.children:
                pra_indent = indent * (token.title_level + 1)
                child = self.tokens[child_id]
                if child.type == "table":
                    ch_data = (
                        child.data_str.replace(
                            "\n", "\n" + indent * (token.title_level + 1)
                        )
                        + "\n"
                    )
                elif child.type == "figure":
                    ch_data = child.data_str + "\n"
                else:
                    ch_data = []
                    for i, textline in enumerate(child.textline):
                        if i == 0:
                            ch_data.append(f"{str(textline)}" + "\n")
                        else:
                            ch_data.append("++" + f"{str(textline)}" + "\n")
                    ch_data = pra_indent.join(ch_data)
                content += pra_indent + ch_data
        return content
