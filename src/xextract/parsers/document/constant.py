#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Author: lixiaobing
Date: 2022/05/25
Desc: 字面量统一记录
"""
from pathlib import Path


class Const:
    def __getattr__(self, name):
        return self[name]

    def __setattr__(self, name, value):
        raise TypeError


class DocJson(Const):
    ROOT = 'root'
    PARENT_PATH = 'parent_path'
    ID = 'id'
    TREE = 'tree'
    TEXTLINES = 'textlines'
    NUMBER = 'number'
    PAGE_NUMBER = 'page_number'
    HEADER = 'header'
    FOOTER = 'footer'
    TYPE = 'type'
    DATA = 'data'
    CHILDREN = 'children'
    PAGES = 'pages'
    TABLES = 'tables'
    SPANS = 'spans'
    BBOX = 'bbox'
    BODY = 'body'
    BLOCK = 'blocks'
    PDF_INFO = 'pdf_info'
    FONTS = 'fonts'
    TYPE = 'type'
    # 类型值
    COVER = '<封面>'
    MAINTEXT = '<正文>'
    ROOTNODE = 'RootNode'
    TABLE = 'table'
    SECTION = 'section'
    TITLE = 'title'
    TEXT = 'text'
    FIGURE = 'figure'


class DocAttr(Const):
    """
    doc.json属性字面量
    """

    META = 'doc_meta'
    NAME = 'json_filename'
    ID = 'id'
    TYPE = 'type'
    TITLE = 'title'
    TABLE = 'table'
    TEXT = 'text'
    HEADER = 'header'
    FOOTER = 'footer'
    CHAPTER = 'chapter'
    FIGURE = 'figure'
    CATLOG = 'catlog'
    PAGE_RANGE = 'page_range'
    TITLE_PATH = 'title_path'
    TITLE_PATH_ID = 'title_path_id'
    TITLE_PATH_NO = 'title_path_no'
    CONTENT = 'content'
    DATA = 'data'
    DESCRIPTIONS = 'descriptions'
    DOC_DATA = 'doc_data'
    TOKEN = 'token'
    TABLE_HTML = 'table_html'

    @staticmethod
    def get_rel_filename(abs_filename):
        """
        获取json文件名和上一级目录，避免绝对路径，方便数据迁移
        """
        abs_path = Path(abs_filename)
        remove_path = str(abs_path.parents[1])
        rel_filename = abs_filename.replace(remove_path + '/', '')
        return rel_filename

    @classmethod
    def add_doc_feature(cls, values, show_keys=None, **kwargs):
        """
        添加上下文特征
        """
        # input_json = kwargs.get('json_filename', '')
        token = kwargs.get(cls.TOKEN, None)
        type_ = kwargs.get(cls.TYPE, '')
        if token and values:
            new_value = token.doc.cal_bbox2value(type_, token.location_data, values)
        else:
            new_value = values
        if not show_keys:
            show_keys = [cls.ID, cls.TYPE, cls.TITLE_PATH, cls.PAGE_RANGE] 
        for key in show_keys:
            new_value[key] = kwargs.get(key)
        return new_value


# LocationText, LocationTextPart
class LocationAttr(Const):
    """
    locationtext 字面量
    """

    PAGE_SIZE = 'page_size'
    BBOXES = 'bboxes'
    BBOX = 'bboxes'
    PAGE_NUMNER = 'page_no'
    CHARS = 'chars'
    LOCATIONS = 'locations'


class CellAttr(Const):
    """
    单元格字面量
    """
    CELLS = 'cells'
    ROW_SPAN = 'row_span'
    COL_SPAN = 'col_span'
    ROW_INDEX = 'row_index'
    COL_INDEX = 'col_index'
    MERGED = 'merged'
    MERGED_CELLS = 'merged_cells'
    TEXT = 'text'
    BBOX = 'bbox'
    SPANS = 'spans'
    PAGE_NUMBER = 'page_number'


class FigureAttr(Const):
    """
    图片字面量
    """
    FILENAME = 'filename'
    BBOX = 'bbox'
    PAGE_NUMBER = 'page_number'


class TextAttr(Const):
    """
    文本字面量
    """
    TEXT = 'text'
    BBOX = 'bbox'
    SPANS = 'spans'
    PAGE_NUMBER = 'page_number'
