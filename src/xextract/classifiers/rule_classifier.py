"""Rule-based document classifier."""

import logging
from typing import Dict, Any, Optional, Tuple, List

from .base import BaseClassifier

logger = logging.getLogger(__name__)


class RuleBasedClassifier(BaseClassifier):
    """Rule-based document classifier using keyword matching."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the rule-based classifier.
        
        Args:
            config: Configuration dictionary with classification rules
        """
        super().__init__(config)
        
        # Load classification rules from config
        self.classification_rules = self.config.get("classification_rules", {})
        
        # Default rules if none provided
        if not self.classification_rules:
            self.classification_rules = self._get_default_rules()
            
        logger.debug(f"Initialized RuleBasedClassifier with {len(self.classification_rules)} rules")
        
    def _get_default_rules(self) -> Dict[str, Dict[str, Any]]:
        """Get default classification rules."""
        return {
            "annual_report": {
                "keywords": ["annual report", "财务报告", "年报", "年度报告"],
                "schema": "annual_report_schema",
                "confidence_threshold": 0.8
            },
            "contract": {
                "keywords": ["contract", "agreement", "合同", "协议"],
                "schema": "contract_schema",
                "confidence_threshold": 0.8
            },
            "invoice": {
                "keywords": ["invoice", "发票", "账单", "收据"],
                "schema": "invoice_schema",
                "confidence_threshold": 0.8
            },
            "general_document": {
                "keywords": [],  # Fallback for unmatched documents
                "schema": "test_schema",
                "confidence_threshold": 0.5
            }
        }
        
    def classify(self, text: str, metadata: Optional[Dict[str, Any]] = None) -> Tuple[str, float]:
        """Classify document based on keyword matching.
        
        Args:
            text: Document text content
            metadata: Optional metadata about the document
            
        Returns:
            Tuple of (document_type, confidence_score)
        """
        text_lower = text.lower()
        best_match = None
        best_score = 0.0
        
        # Check each classification rule
        for doc_type, rule in self.classification_rules.items():
            keywords = rule.get("keywords", [])
            
            # Skip general_document rule in first pass (it's the fallback)
            if doc_type == "general_document":
                continue
                
            # Calculate match score based on keyword presence
            matches = sum(1 for keyword in keywords if keyword.lower() in text_lower)
            
            if matches > 0:
                # Calculate confidence based on keyword matches and text length
                confidence = min(0.9, (matches / len(keywords)) * 0.8 + 0.1)
                
                if confidence > best_score:
                    best_match = doc_type
                    best_score = confidence
                    
        # If no specific type matched, use general_document
        if best_match is None:
            best_match = "general_document"
            best_score = self.classification_rules["general_document"]["confidence_threshold"]
            
        logger.debug(f"Classified document as '{best_match}' with confidence {best_score:.2f}")
        return best_match, best_score
        
    def get_schema_for_type(self, document_type: str) -> str:
        """Get the schema name for a document type.
        
        Args:
            document_type: The classified document type
            
        Returns:
            Schema name to use for extraction
        """
        rule = self.classification_rules.get(document_type)
        if rule:
            return rule.get("schema", "test_schema")
        
        # Fallback to general document schema
        return self.classification_rules.get("general_document", {}).get("schema", "test_schema")
        
    def add_rule(self, document_type: str, keywords: List[str], schema: str, confidence_threshold: float = 0.8):
        """Add a new classification rule.
        
        Args:
            document_type: Type of document
            keywords: Keywords to match
            schema: Schema to use for this document type
            confidence_threshold: Minimum confidence threshold
        """
        self.classification_rules[document_type] = {
            "keywords": keywords,
            "schema": schema,
            "confidence_threshold": confidence_threshold
        }
        logger.info(f"Added classification rule for '{document_type}'")
        
    def remove_rule(self, document_type: str):
        """Remove a classification rule.
        
        Args:
            document_type: Type of document to remove
        """
        if document_type in self.classification_rules:
            del self.classification_rules[document_type]
            logger.info(f"Removed classification rule for '{document_type}'")
        else:
            logger.warning(f"No rule found for document type '{document_type}'")
            
    def get_supported_types(self) -> List[str]:
        """Get list of supported document types.
        
        Returns:
            List of supported document types
        """
        return list(self.classification_rules.keys())
