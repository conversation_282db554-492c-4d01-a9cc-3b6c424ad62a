"""Base classifier for document classification."""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Tuple


class BaseClassifier(ABC):
    """Base class for document classifiers."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the classifier.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        
    @abstractmethod
    def classify(self, text: str, metadata: Optional[Dict[str, Any]] = None) -> Tuple[str, float]:
        """Classify a document.
        
        Args:
            text: Document text content
            metadata: Optional metadata about the document
            
        Returns:
            Tuple of (document_type, confidence_score)
        """
        pass
        
    @abstractmethod
    def get_schema_for_type(self, document_type: str) -> str:
        """Get the schema name for a document type.
        
        Args:
            document_type: The classified document type
            
        Returns:
            Schema name to use for extraction
        """
        pass
        
    def get_classification_info(self, text: str, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Get full classification information.
        
        Args:
            text: Document text content
            metadata: Optional metadata about the document
            
        Returns:
            Dictionary with classification details
        """
        document_type, confidence = self.classify(text, metadata)
        schema_name = self.get_schema_for_type(document_type)
        
        return {
            "document_type": document_type,
            "confidence": confidence,
            "schema_name": schema_name,
            "metadata": metadata or {}
        }
