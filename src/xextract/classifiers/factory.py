"""Factory for creating document classifiers."""

import logging
from typing import Dict, Any, Optional, Type

from .base import BaseClassifier
from .rule_classifier import RuleBasedClassifier

logger = logging.getLogger(__name__)


class ClassifierFactory:
    """Factory for creating document classifiers."""
    
    # Registry of available classifier types
    _classifiers: Dict[str, Type[BaseClassifier]] = {}
    
    @classmethod
    def register(cls, name: str, classifier_class: Type[BaseClassifier]) -> None:
        """Register a classifier class.
        
        Args:
            name: Classifier name
            classifier_class: Classifier class
        """
        cls._classifiers[name] = classifier_class
        logger.debug(f"Registered classifier: {name}")
        
    @classmethod
    def create(cls, classifier_type: str = "rule", **kwargs) -> BaseClassifier:
        """Create a classifier.
        
        Args:
            classifier_type: Type of classifier to create
            **kwargs: Classifier parameters
            
        Returns:
            Classifier instance
            
        Raises:
            ValueError: If classifier type is not found
        """
        if classifier_type not in cls._classifiers:
            raise ValueError(f"Classifier type not found: {classifier_type}")
            
        classifier_class = cls._classifiers[classifier_type]
        return classifier_class(**kwargs)
        
    @classmethod
    def create_rule_classifier(cls, config: Optional[Dict[str, Any]] = None) -> RuleBasedClassifier:
        """Create a rule-based classifier.
        
        Args:
            config: Configuration dictionary
            
        Returns:
            RuleBasedClassifier instance
        """
        return RuleBasedClassifier(config=config)
        
    @classmethod
    def get_available_types(cls) -> list:
        """Get list of available classifier types.
        
        Returns:
            List of available classifier types
        """
        return list(cls._classifiers.keys())


# Register built-in classifiers
ClassifierFactory.register("rule", RuleBasedClassifier)
