"""Document classifiers for XExtract.

This module provides document classification functionality for the pipeline.
Currently used for future extensibility, allowing different extraction schemas
to be applied based on document type.
"""

from .base import BaseClassifier
from .rule_classifier import RuleBasedClassifier
from .factory import ClassifierFactory

__all__ = ["BaseClassifier", "RuleBasedClassifier", "ClassifierFactory"]
