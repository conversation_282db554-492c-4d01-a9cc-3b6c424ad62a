"""OpenAI and compatible LLM implementation.

This module provides an implementation of the LLM interface for OpenAI
and compatible APIs, including Gemini API (which provides an OpenAI-compatible endpoint).
"""
import json
import logging
import random
import time
from typing import Any, Dict, Optional, List

from openai import OpenAI
from openai.types.chat import ChatCompletion

from xextract.utils.utils import timer

from .base_llm import LLM
from ..utils.json_extractor import JSONExtractor

logger = logging.getLogger(__name__)


class OpenAILLM(LLM):
    """OpenAI and compatible LLM implementation.

    This class supports both OpenAI API and compatible APIs like Gemini.
    For Gemini, set the api_base_url to the Gemini OpenAI-compatible endpoint.
    """

    def __init__(
        self,
        api_key: str,
        model: str = "fin-model",
        api_base_url: Optional[str] = None,
        timeout: Optional[int] = None,
        max_retries: Optional[int] = None,
        temperature: float = 0.0,
        max_tokens: Optional[int] = None,
        provider: str = "memect",
        **kwargs
    ):
        """Initialize OpenAI or compatible LLM.

        Args:
            api_key: API key
            model: Model to use (e.g., "gpt-4o" for OpenAI, "gemini-2.0-flash" for Gemini)
            api_base_url: Base URL for API
            timeout: Request timeout in seconds
            max_retries: Maximum number of retries
            temperature: Temperature for generation
            max_tokens: Maximum tokens to generate
            provider: Provider name ("openai" or "gemini")
            **kwargs: Additional options
        """
        super().__init__(
            api_key=api_key,
            model=model,
            api_base_url=api_base_url,
            timeout=timeout,
            max_retries=max_retries,
            temperature=temperature,
            max_tokens=max_tokens,
            provider=provider,
            **kwargs
        )

        # Store provider
        self.provider = provider

        # 从配置文件中读取默认值
        # 导入配置
        import sys
        from pathlib import Path
        sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))
        from config.config import config

        # 获取默认LLM配置
        try:
            llm_config = config.llm

            # 读取超时时间和重试次数
            default_timeout = llm_config.get("timeout", 120)  # 默认120秒
            default_max_retries = llm_config.get("max_retries", 3)  # 默认3次重试

            logger.info(f"从配置文件读取LLM超时时间: {default_timeout}秒, 最大重试次数: {default_max_retries}")
        except Exception as e:
            logger.warning(f"读取LLM配置失败，使用默认值: {e}")
            default_timeout = 120  # 默认120秒
            default_max_retries = 3  # 默认3次重试

        # 设置超时时间和重试次数
        if timeout is None:
            self.timeout = default_timeout
            logger.info(f"使用配置的超时时间: {self.timeout}秒")

        if max_retries is None:
            self.max_retries = default_max_retries
            logger.info(f"使用配置的最大重试次数: {self.max_retries}")

        # Set default API base URL based on provider
        if not self.api_base_url:
            if self.provider == "gemini":
                self.api_base_url = "https://generativelanguage.googleapis.com/v1beta/openai/"
            else:  # default to OpenAI
                self.api_base_url = "https://api.openai.com/v1/"

        # Initialize OpenAI client
        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.api_base_url,
            timeout=self.timeout,
            max_retries=self.max_retries
        )

    def generate(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> str:
        """Generate text from a prompt.

        Args:
            prompt: The prompt to generate from
            system_prompt: Optional system prompt
            **kwargs: Model-specific parameters

        Returns:
            Generated text
        """
        # Merge default options with provided kwargs
        options = {
            "temperature": self.options.get("temperature", 0.0),
            "max_tokens": self.options.get("max_tokens"),
        }
        options.update(kwargs)

        # Remove None values
        options = {k: v for k, v in options.items() if v is not None}

        # Prepare messages
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        # Initialize retry counter
        retries = 0
        max_retries = self.max_retries

        while True:
            try:
                # Log input
                logger.info(f"llm input {{'model': '{self.model}', 'messages': {messages}, 'temperature': {options.get('temperature', 0.0)}, 'max_tokens': {options.get('max_tokens', 'None')}}}")

                # Send request using OpenAI SDK
                try:
                    response: ChatCompletion = self.client.chat.completions.create(
                        model=self.model,
                        messages=[{"role": m["role"], "content": m["content"]} for m in messages],
                        **options
                    )

                    # 检查响应是否有效
                    if not response or not hasattr(response, 'choices') or not response.choices:
                        logger.error(f"Invalid response from API: {response}")
                        return "Error: Invalid response from API"

                    # 检查消息是否有效
                    if not hasattr(response.choices[0], 'message') or not response.choices[0].message:
                        logger.error(f"Invalid message in response: {response.choices}")
                        return "Error: Invalid message in response"

                    # 提取并返回内容
                    return response.choices[0].message.content or ""
                except Exception as e:
                    logger.error(f"Error during API call: {e}")
                    logger.error(f"Request details: model={self.model}, messages={messages}")
                    return f"Error during API call: {e}"

            except Exception as e:
                # Log detailed error
                logger.error(f"Error generating text (attempt {retries+1}/{max_retries+1}): {e}")
                logger.error(f"API URL: {self.api_base_url}")
                logger.error(f"Model: {self.model}")
                logger.error(f"Provider: {self.provider}")

                # Log more details for connection errors
                if "connection" in str(e).lower():
                    logger.error(f"Connection error details: {str(e)}")
                    import traceback
                    logger.error(f"Traceback: {traceback.format_exc()}")

                # Check if we should retry
                if retries < max_retries:
                    # Increment retry counter
                    retries += 1

                    # Calculate backoff time (exponential backoff with jitter)
                    backoff_time = min(2 ** retries + random.random(), 60)

                    # Log retry
                    logger.info(f"Retrying in {backoff_time:.2f} seconds...")

                    # Wait before retrying
                    time.sleep(backoff_time)
                else:
                    # Max retries exceeded, raise exception
                    logger.error(f"Max retries exceeded: {e}")
                    raise

    @timer
    def generate_json(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate JSON from a prompt.

        Args:
            prompt: The prompt to generate from
            system_prompt: Optional system prompt
            **kwargs: Model-specific parameters

        Returns:
            Generated JSON as a dictionary
        """
        # Add JSON instruction to prompt
        json_prompt = f"{prompt}\n\nRespond with valid JSON only."

        # Use provided system prompt or default
        if not system_prompt:
            system_prompt = "You are a info extract helpful assistant that responds only with valid JSON."

        # Set response_format to JSON if using OpenAI API
        if self.provider == "openai" and "response_format" not in kwargs:
            kwargs["response_format"] = {"type": "json_object"}

        # Generate text
        response_text = self.generate(
            prompt=json_prompt,
            system_prompt=system_prompt,
            **kwargs
        )

        # Extract JSON from response
        result = self._extract_json_from_text(response_text)
        if result is not None:
            return result

        # If all else fails, raise an error
        raise ValueError(f"Failed to parse JSON from response: {response_text}")

    def _extract_json_from_text(self, text: str) -> Optional[Dict[str, Any]]:
        """Extract JSON from text.

        Args:
            text: Text that may contain JSON

        Returns:
            Extracted JSON as a dictionary, or None if no JSON could be extracted
        """
        # Try to extract JSON using JSONExtractor
        extractor = JSONExtractor()
        json_data = extractor.extract_json(text)

        if json_data:
            return json_data

        # If JSONExtractor fails, try direct JSON parsing
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            return None
