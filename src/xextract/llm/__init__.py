"""LLM module for XExtract."""
from typing import Any, Dict, Optional, Type

from .base_llm import LLM
from .openai_llm import OpenAILLM

# Registry of LLM providers
_llm_registry: Dict[str, Type[LLM]] = {
    "openai": OpenAILLM,
    "gemini": OpenAILLM,  # Gemini uses OpenAI-compatible API
    "openrouter": OpenAILLM,  # OpenRouter uses OpenAI-compatible API
    "memect": OpenAILLM
}


def register_llm(name: str, llm_class: Type[LLM]) -> None:
    """Register an LLM provider.

    Args:
        name: Name of the provider
        llm_class: LLM class to register
    """
    _llm_registry[name] = llm_class


def create_llm(
    provider: str,
    api_key: Optional[str] = None,
    **kwargs
) -> LLM:
    """Create an LLM instance.

    Args:
        provider: LLM provider name
        api_key: API key for the provider
        **kwargs: Additional provider options

    Returns:
        LLM instance

    Raises:
        ValueError: If the provider is not supported
    """
    if provider not in _llm_registry:
        raise ValueError(f"Unsupported LLM provider: {provider}")

    # Get provider class
    llm_class = _llm_registry[provider]

    # Create instance
    return llm_class(api_key=api_key, provider=provider, **kwargs)


__all__ = ["LLM", "OpenAILLM", "create_llm", "register_llm"]
