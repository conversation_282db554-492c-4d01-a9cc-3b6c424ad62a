#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@File    : base_llm.py
<AUTHOR> lhh
@Date    : 2025-05-08 02:06:19
@Desc    : Base LLM interface for XExtract.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union


class LLM(ABC):
    """Abstract base class for LLM implementations."""

    def __init__(
        self,
        api_key: str,
        model: Optional[str] = None,
        api_base_url: Optional[str] = None,
        timeout: int = 60,
        max_retries: int = 3,
        provider: str = "unknown",
        version: str = "unknown",
        **kwargs,
    ):
        """Initialize LLM provider.

        Args:
            api_key: API key for authentication
            model: Model to use
            api_base_url: Base URL for API
            timeout: Request timeout in seconds
            max_retries: Maximum number of retries
            provider: Provider name (e.g., "openai", "gemini")
            version: Model version
            **kwargs: Additional provider options
        """
        self.api_key = api_key
        self.model = model
        self.api_base_url = api_base_url
        self.timeout = timeout
        self.max_retries = max_retries
        self.provider = provider
        self.version = version
        self.options = kwargs

    @abstractmethod
    def generate(self, prompt: str, **kwargs) -> str:
        """Generate text from a prompt.

        Args:
            prompt: The prompt to generate from
            **kwargs: Model-specific parameters

        Returns:
            Generated text
        """
        pass

    @abstractmethod
    def generate_json(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate JSON from a prompt.

        Args:
            prompt: The prompt to generate from
            **kwargs: Model-specific parameters

        Returns:
            Generated JSON as a dictionary
        """
        pass

    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the model.

        Returns:
            Information about the model
        """
        return {
            "model": self.model,
            "provider": self.provider,
            "version": self.version,
            "class": self.__class__.__name__,
            "options": self.options,
        }

    def format_prompt(self, prompt: str, **kwargs) -> str:
        """Format prompt for provider.

        This method can be overridden by subclasses to provide provider-specific
        prompt formatting. For example, some providers might require specific
        formatting or have limitations on prompt length.

        Args:
            prompt: Raw prompt
            **kwargs: Additional formatting options

        Returns:
            str: Formatted prompt
        """
        # Default implementation just returns the prompt
        return prompt
