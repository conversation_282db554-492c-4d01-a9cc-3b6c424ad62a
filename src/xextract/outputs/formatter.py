"""Unified output formatter for extraction results."""
import json
from typing import Any, Dict, Optional, Union

from .extraction_result import ExtractionResult, ExtractedField, create_extraction_result


class OutputFormatter:
    """输出格式化器，创建包含溯源信息的结构化提取结果。"""

    @staticmethod
    def _process_extracted_data(extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process extracted data to separate values and source_ids."""
        simple_data = {}
        source_ids = {}

        for field_name, field_data in extracted_data.items():
            if isinstance(field_data, dict) and "value" in field_data:
                # Handle structured format: {"value": "...", "source_id": "..."}
                simple_data[field_name] = field_data["value"]
                if "source_id" in field_data:
                    source_ids[field_name] = field_data["source_id"]
            else:
                # Handle simple format: direct value
                simple_data[field_name] = field_data

        return {"simple_data": simple_data, "source_ids": source_ids}

    @staticmethod
    def create_enhanced_result(
        extracted_data: Dict[str, Any],
        doc_table: Any,
        document_id: str = "",
        schema_id: str = "",
        extraction_time: float = 0.0,
        document_metadata: Optional[Dict[str, Any]] = None,
        model_info: Optional[Dict[str, Any]] = None,
        include_bbox: bool = True,
    ) -> ExtractionResult:
        """创建包含溯源信息的结构化提取结果。

        Args:
            extracted_data: 大模型的原始提取结果
            doc_table: 文档表格，用于溯源查找
            document_id: 文档标识符
            schema_id: 模式标识符
            extraction_time: 提取耗时
            document_metadata: 文档元数据
            model_info: 模型信息
            include_bbox: 是否包含bbox位置信息

        Returns:
            包含溯源信息的提取结果对象
        """
        # 准备基础元数据
        metadata = document_metadata or {}
        if model_info:
            metadata.setdefault("extraction", {})["model"] = model_info

        # 处理提取数据，分离值和source_id
        processed_data = OutputFormatter._process_extracted_data(extracted_data)

        # 创建提取结果
        result = create_extraction_result(
            document_id=document_id,
            schema_id=schema_id,
            simple_result=processed_data["simple_data"],
            extraction_time=extraction_time,
            metadata=metadata,
        )

        # 保存source_id信息到字段元数据并添加溯源信息
        for field_name, field in result.fields.items():
            if field_name in processed_data["source_ids"]:
                field.metadata["source_id"] = processed_data["source_ids"][field_name]

            # 添加溯源信息
            OutputFormatter._add_field_source_info(field, doc_table, include_bbox)

        return result



    @staticmethod
    def _add_field_source_info(field: ExtractedField, doc_table: Optional[Any], include_bbox: bool = True) -> None:
        """根据source_id从doc_table中添加溯源信息到字段。

        Args:
            field: 提取的字段
            doc_table: 文档表格数据
            include_bbox: 是否包含bbox位置信息
        """
        if (
            doc_table is None
            or not hasattr(field, "metadata")
            or "source_id" not in field.metadata
        ):
            return

        try:
            source_id = field.metadata.get("source_id")
            if not source_id or source_id == "0" or source_id == "":
                return

            # Convert source_id to int, skip if conversion fails
            try:
                source_id_int = int(source_id)
            except (ValueError, TypeError):
                return

            # Find matching row
            matching_rows = doc_table[doc_table["id"] == source_id_int]
            if matching_rows.empty:
                return

            row = matching_rows.iloc[0]

            # Add source information
            field.metadata["source_info"] = {
                "chapter_path": row.get("title_path", ""),
                "text_type": row.get("type", ""),
                "original_text": row.get("content", ""),
                "page_range": row.get("page_range", ""),
            }

            # Extract bbox information from token if enabled
            if include_bbox:
                bbox_info = OutputFormatter._extract_bbox_info(row)
                if bbox_info:
                    field.metadata["bbox"] = bbox_info

            # Update context if not set
            if not field.context and row.get("content"):
                content = str(row.get("content", ""))
                field.context = content[:200] + "..." if len(content) > 200 else content

        except (ValueError, KeyError, AttributeError):
            pass

    @staticmethod
    def _extract_bbox_info(row) -> Optional[Dict[str, Any]]:
        """从doc_table行中提取bbox信息。"""
        try:
            # 获取token对象
            token = row.get("token")
            if not token:
                return None

            # 尝试从token的location_textline中提取bbox信息
            location_textline = getattr(token, 'location_textline', None)
            if location_textline:
                bbox_info = OutputFormatter._extract_from_location_text(location_textline)
                if bbox_info:
                    return bbox_info

            # 从token的org_data中提取bbox信息
            org_data = getattr(token, 'org_data', None)
            if not org_data:
                return None

            bbox_list = []
            page_numbers = []

            # 处理org_data列表
            for data_item in org_data:
                if isinstance(data_item, dict):
                    # 提取bbox信息
                    bbox = data_item.get('bbox')
                    page_no = data_item.get('page_number')

                    if bbox and len(bbox) >= 4:
                        bbox_info = {
                            "bbox": bbox,  # [left, bottom, right, top]
                            "page_number": page_no
                        }

                        # 如果有spans信息，也包含进来
                        spans = data_item.get('spans', [])
                        if spans:
                            bbox_info["spans"] = spans

                        bbox_list.append(bbox_info)

                        if page_no is not None:
                            page_numbers.append(page_no)

            if bbox_list:
                return {
                    "bboxes": bbox_list,
                    "page_numbers": sorted(list(set(page_numbers))) if page_numbers else [],
                    "total_bboxes": len(bbox_list)
                }

        except (AttributeError, TypeError, KeyError):
            pass

        return None

    @staticmethod
    def _extract_from_location_text(location_textline) -> Optional[Dict[str, Any]]:
        """从LocationText对象中提取bbox信息。"""
        try:
            bbox_list = []
            page_numbers = []

            # 处理LocationText列表
            for location_text in location_textline:
                # 检查是否有get_locations方法
                if hasattr(location_text, 'get_locations'):
                    locations = location_text.get_locations()

                    for location in locations:
                        if isinstance(location, dict) and 'bbox' in location:
                            bbox_info = {
                                "bbox": location['bbox'],
                                "page_number": location.get('page_no'),
                                "page_size": location.get('page_size'),
                                "chars": location.get('chars', '')
                            }
                            bbox_list.append(bbox_info)

                            page_no = location.get('page_no')
                            if page_no is not None:
                                page_numbers.append(page_no)

                # 检查是否有bbox属性
                elif hasattr(location_text, 'bbox'):
                    bbox = location_text.bbox
                    if bbox and len(bbox) >= 4:
                        bbox_info = {
                            "bbox": bbox,
                            "page_number": getattr(location_text, 'first_page_no', lambda: None)(),
                            "page_size": getattr(location_text, 'first_page_size', lambda: None)(),
                            "chars": str(location_text)
                        }
                        bbox_list.append(bbox_info)

                        page_no = bbox_info["page_number"]
                        if page_no is not None:
                            page_numbers.append(page_no)

            if bbox_list:
                return {
                    "bboxes": bbox_list,
                    "page_numbers": sorted(list(set(page_numbers))) if page_numbers else [],
                    "total_bboxes": len(bbox_list)
                }

        except (AttributeError, TypeError, KeyError):
            pass

        return None

    @staticmethod
    def to_json(
        result: Union[Dict[str, Any], ExtractionResult],
        indent: int = 2,
        ensure_ascii: bool = False,
        **kwargs,
    ) -> str:
        """Convert result to JSON string.

        Args:
            result: Extraction result (dict or ExtractionResult)
            indent: JSON indentation
            ensure_ascii: Whether to ensure ASCII output
            **kwargs: Additional arguments for ExtractionResult.to_dict()

        Returns:
            JSON string
        """
        if isinstance(result, ExtractionResult):
            data = result.to_dict(**kwargs)
        else:
            data = result
        return json.dumps(data, indent=indent, ensure_ascii=ensure_ascii)

    @staticmethod
    def format_for_api(
        extraction_result: ExtractionResult,
        include_metadata: bool = True,
        include_confidence: bool = True,
    ) -> Dict[str, Any]:
        """Format extraction result for API response."""
        result = {
            "status": "success",
            "data": extraction_result.get_simple_result(),
            "document_id": extraction_result.document_id,
            "schema_id": extraction_result.schema_id,
        }

        if include_metadata:
            result["metadata"] = extraction_result.metadata

        if include_confidence:
            confidence_scores = {
                field_name: field.metadata["confidence"]
                for field_name, field in extraction_result.fields.items()
                if "confidence" in field.metadata
            }
            if confidence_scores:
                result["confidence_scores"] = confidence_scores

        return result


# 向后兼容的别名
ExtractionResultFormatter = OutputFormatter
ExtractionFormatter = OutputFormatter
