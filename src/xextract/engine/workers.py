"""Pipeline workers for different processing stages.

Optimized for "Read Once, Process in Memory, Write at End" pattern:
- ParseWorker: Reads file once, processes in memory
- ClassifyWorker: Works with in-memory data, no I/O
- ExtractWorker: Works with in-memory data, writes final result
"""

import logging
import time
from pathlib import Path
from typing import Any, Dict, Optional, Union

from .pipeline import PipelineWorker, PipelineItem, PipelineData, StageType, StageConfig
from ..parsers.factory import PDFParserFactory
from ..extractors.factory import ExtractorFactory
from ..schema import Schema

logger = logging.getLogger(__name__)


class ParseWorker(PipelineWorker):
    """Worker for parsing stage - Read Once, Process in Memory."""

    def __init__(self, worker_id: str, config: StageConfig, parser_config: Dict[str, Any] = None):
        super().__init__(StageType.PARSE, worker_id, config)
        self.parser_config = parser_config or {}
        self.parser = None

    async def process(self, item: PipelineItem) -> PipelineItem:
        """Parse document from file path, load all data into memory."""
        start_time = time.time()

        source_path = item.data.source_path
        source_type = item.data.source_type
        file_path = Path(source_path)

        logger.debug(f"Parsing {source_type} file: {file_path}")

        try:
            if source_type == "doc_json":
                # Handle doc.json files directly
                await self._process_doc_json(item, file_path, start_time)
            elif source_type == "pdf":
                # Handle PDF files with API parsing
                await self._process_pdf(item, file_path, start_time)
            else:
                raise ValueError(f"Unsupported source type: {source_type}")

            return item.to_next_stage(stage_metadata={
                "parsed_at": time.time(),
                "source_type": source_type,
                "processing_time": time.time() - start_time
            })

        except Exception as e:
            error_msg = f"Failed to parse {source_type} file {file_path}: {str(e)}"
            logger.error(error_msg)
            item.add_processing_error("parse", error_msg)
            item.error = error_msg
            return item

    async def _process_doc_json(self, item: PipelineItem, file_path: Path, start_time: float):
        """Process doc.json file directly (no API call needed)."""
        # Step 1: Read doc.json content into memory
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            item.data.raw_content = content.encode('utf-8')

        # Step 2: Parse JSON data
        import json
        parsed_data = json.loads(content)
        item.data.parsed_data = parsed_data

        # Step 3: Convert to document structure in memory
        from ..parsers.document.analyzer.doc_json import DocJsonAnalyzer

        analyzer = DocJsonAnalyzer()
        doc_tree = analyzer.analyze_dict(parsed_data, str(file_path))

        # Store all processed data in memory
        item.data.doc_tree = doc_tree
        item.data.doc_table = doc_tree.get_doc_table()
        item.data.structured_text = doc_tree.to_md_with_id()

        # Load schema (support both name and file path)
        from ..schema import Schema
        try:
            schema = self._load_schema(item.data.schema_name)
            item.data.schema_object = schema
        except Exception as e:
            logger.warning(f"Failed to load schema {item.data.schema_name}: {e}")

        # Update metadata
        processing_time = time.time() - start_time
        item.add_processing_time("parse", processing_time)

        logger.debug(f"Processed doc.json {file_path} in {processing_time:.2f}s")

    async def _process_pdf(self, item: PipelineItem, file_path: Path, start_time: float):
        """Process PDF file with API parsing."""
        if self.parser is None:
            self.parser = PDFParserFactory.create_parser(**self.parser_config)

        # Step 1: Read file content into memory (Read Once)
        with open(file_path, 'rb') as f:
            item.data.raw_content = f.read()

        # Step 2: Parse the PDF using the API
        parsed_data = await self.parser.parse_pdf(file_path)
        item.data.parsed_data = parsed_data

        # Step 3: Convert to document structure in memory (no temp files)
        from ..parsers.document.analyzer.doc_json import DocJsonAnalyzer

        analyzer = DocJsonAnalyzer()
        doc_tree = analyzer.analyze_dict(parsed_data, str(file_path))

        # Store all processed data in memory
        item.data.doc_tree = doc_tree
        item.data.doc_table = doc_tree.get_doc_table()
        item.data.structured_text = doc_tree.to_md_with_id()

        # Load schema (support both name and file path)
        from ..schema import Schema
        try:
            schema = self._load_schema(item.data.schema_name)
            item.data.schema_object = schema
        except Exception as e:
            logger.warning(f"Failed to load schema {item.data.schema_name}: {e}")

        # Update metadata
        processing_time = time.time() - start_time
        item.add_processing_time("parse", processing_time)

        logger.debug(f"Parsed PDF {file_path} in {processing_time:.2f}s")

    def _load_schema(self, schema_name: str):
        """Load schema from name or file path.

        Args:
            schema_name: Schema name or file path

        Returns:
            Schema object
        """
        from ..schema import Schema
        import os

        # Check if schema_name is a file path
        if os.path.exists(schema_name):
            try:
                # Determine file type based on extension
                if schema_name.endswith(".json"):
                    return Schema.from_json_file(schema_name)
                elif schema_name.endswith((".yaml", ".yml")):
                    return Schema.from_yaml_file(schema_name)
                else:
                    logger.error(f"Unsupported schema file format: {schema_name}")
                    return None
            except Exception as e:
                logger.error(f"Failed to load schema from file {schema_name}: {e}")
                return None
        else:
            # Try to load by name
            schema = Schema.get_by_name(schema_name)
            if not schema:
                logger.error(f"Schema not found: {schema_name}")
                return None
            return schema


# ClassifyWorker removed - schema loading moved to ParseWorker


class ExtractWorker(PipelineWorker):
    """Worker for extraction stage - Process in Memory, Write at End."""

    def __init__(self, worker_id: str, config: StageConfig, extractor_config: Dict[str, Any] = None):
        super().__init__(StageType.EXTRACT, worker_id, config)
        self.extractor_config = extractor_config or {}
        self.extractor = None

    async def process(self, item: PipelineItem) -> PipelineItem:
        """Extract structured data and write final result."""
        start_time = time.time()

        if self.extractor is None:
            self.extractor = ExtractorFactory.create_llm_extractor(**self.extractor_config)

        # Get all data from memory (no I/O until final write)
        text = item.data.structured_text or ""
        schema = item.data.schema_object
        doc_table = item.data.doc_table

        if not schema:
            raise ValueError("No schema provided for extraction")

        logger.debug(f"Extracting data for item {item.id} using schema {item.data.schema_name}")

        try:
            # Convert schema to dict for extraction
            schema_dict = schema.to_dict() if hasattr(schema, "to_dict") else schema

            # Perform extraction using in-memory data
            extraction_result = self.extractor.extract(text, schema_dict)

            # Create enhanced result with source traceability
            if doc_table is not None:
                enhanced_result = self._create_enhanced_result(
                    extraction_result, doc_table, item.data
                )
            else:
                enhanced_result = extraction_result

            # Store results in pipeline data
            item.data.extraction_result = extraction_result
            item.data.enhanced_result = enhanced_result

            # Update metadata
            processing_time = time.time() - start_time
            item.add_processing_time("extract", processing_time)

            stage_metadata = {
                "extracted_at": time.time(),
                "extractor_type": self.extractor.__class__.__name__,
                "model_info": getattr(self.extractor, 'model_info', {}),
                "processing_time": processing_time
            }

            logger.debug(f"Extracted data for item {item.id} in {processing_time:.2f}s")

            # This is the final stage - prepare complete result
            final_result = self._prepare_final_result(item)

            # Create a new item with final result for output
            final_item = PipelineItem(
                id=item.id,
                data=final_result,  # Final result data
                metadata=item.metadata,
                stage=StageType.EXTRACT,
                created_at=item.created_at
            )
            final_item.metadata.update(stage_metadata)

            return final_item

        except Exception as e:
            error_msg = f"Failed to extract data: {str(e)}"
            logger.error(error_msg)
            item.add_processing_error("extract", error_msg)
            item.error = error_msg
            return item

    def _prepare_final_result(self, item: PipelineItem) -> Dict[str, Any]:
        """Prepare the final result with all processing information."""
        return {
            "task_id": item.id,
            "source_path": item.data.source_path,
            "document_type": item.data.document_type,
            "schema_name": item.data.schema_name,
            "extraction_result": item.data.enhanced_result or item.data.extraction_result,
            "raw_extraction": item.data.extraction_result,
            "processing_times": item.data.processing_times,
            "processing_errors": item.data.processing_errors,
            "total_processing_time": sum(item.data.processing_times.values()),
            "status": "error" if item.data.processing_errors else "success"
        }

    def _create_enhanced_result(self, extraction_result: Dict[str, Any],
                              doc_table, pipeline_data: PipelineData) -> Dict[str, Any]:
        """Create enhanced result with source traceability."""
        try:
            from ..outputs.formatter import OutputFormatter

            # 获取配置中的bbox设置
            include_bbox = getattr(self.config, 'INCLUDE_BBOX', True)

            return OutputFormatter.create_enhanced_result(
                extracted_data=extraction_result,
                doc_table=doc_table,
                document_id=pipeline_data.source_path,
                schema_id=pipeline_data.schema_name,
                extraction_time=pipeline_data.processing_times.get("extract", 0.0),
                document_metadata={
                    "source_path": pipeline_data.source_path,
                    "document_type": pipeline_data.document_type,
                    "schema_name": pipeline_data.schema_name
                },
                model_info=getattr(self.extractor, 'model_info', {}),
                include_bbox=include_bbox
            )
        except Exception as e:
            logger.warning(f"Failed to create enhanced result: {e}")
            return extraction_result


# Worker factory functions
def create_parse_worker(worker_id: str, config: StageConfig, **kwargs) -> ParseWorker:
    """Factory function for parse workers."""
    return ParseWorker(worker_id, config, kwargs.get("parser_config", {}))


# ClassifyWorker removed - no longer needed


def create_extract_worker(worker_id: str, config: StageConfig, **kwargs) -> ExtractWorker:
    """Factory function for extract workers."""
    return ExtractWorker(worker_id, config, kwargs.get("extractor_config", {}))
