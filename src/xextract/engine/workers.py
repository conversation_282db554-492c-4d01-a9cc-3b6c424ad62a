"""Pipeline workers for different processing stages."""

import logging
from pathlib import Path
from typing import Any, Dict, Optional, Union

from .pipeline import PipelineWorker, PipelineItem, StageType, StageConfig
from ..parsers.factory import PDFParserFactory
from ..extractors.factory import ExtractorFactory
from ..schema import Schema

logger = logging.getLogger(__name__)


class ParseWorker(PipelineWorker):
    """Worker for parsing stage (Q1 -> P1...Pn -> Q2)."""

    def __init__(self, worker_id: str, config: StageConfig, parser_config: Dict[str, Any] = None):
        super().__init__(StageType.PARSE, worker_id, config)
        self.parser_config = parser_config or {}
        self.parser = None

    async def process(self, item: PipelineItem) -> PipelineItem:
        """Parse document from file path to structured data."""
        if self.parser is None:
            self.parser = PDFParserFactory.create_parser(**self.parser_config)

        file_path = item.data
        if isinstance(file_path, str):
            file_path = Path(file_path)

        logger.debug(f"Parsing file: {file_path}")

        # Parse the PDF
        parsed_data = await self.parser.parse_pdf(file_path)

        # Convert to document structure using DocJsonAnalyzer
        import json
        import tempfile
        from ..parsers.document.analyzer.doc_json import DocJsonAnalyzer

        # Create temporary file for parsed data
        with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False) as temp_file:
            json.dump(parsed_data, temp_file, ensure_ascii=False)
            temp_path = temp_file.name

        try:
            analyzer = DocJsonAnalyzer()
            doc = analyzer.analyze(temp_path)

            # Prepare data for next stage
            processed_data = {
                "doc": doc,
                "doc_table": doc.get_doc_table(),
                "text": doc.to_md_with_id(),
                "file_path": str(file_path)
            }

            # Add parsing metadata
            metadata = {
                "parsed_at": item.created_at,
                "parser_type": self.parser.__class__.__name__,
                "file_size": file_path.stat().st_size if file_path.exists() else 0
            }

            return item.to_next_stage(data=processed_data, metadata=metadata)

        finally:
            # Clean up temporary file
            import os
            if os.path.exists(temp_path):
                os.remove(temp_path)


class ClassifyWorker(PipelineWorker):
    """Worker for classification stage (Q2 -> C1...Cn -> Q3)."""

    def __init__(self, worker_id: str, config: StageConfig, classification_rules: Dict[str, Any] = None):
        super().__init__(StageType.CLASSIFY, worker_id, config)
        self.classifier = None
        self.classification_config = {"classification_rules": classification_rules or {}}

    async def process(self, item: PipelineItem) -> PipelineItem:
        """Classify document and determine extraction schema."""
        parsed_data = item.data
        text = parsed_data.get("text", "")

        logger.debug(f"Classifying document for item {item.id}")

        # Check if classification is enabled
        classification_enabled = self.classification_config.get("classification_enabled", False)

        if classification_enabled and self.classification_config.get("classification_rules"):
            # Use classifier for document classification
            if self.classifier is None:
                from ..classifiers.factory import ClassifierFactory
                self.classifier = ClassifierFactory.create_rule_classifier(config=self.classification_config)

            classification_info = self.classifier.get_classification_info(text)
            document_type = classification_info["document_type"]
            confidence = classification_info["confidence"]
            schema_name = classification_info["schema_name"]
            classifier_type = "rule_based"
        else:
            # Use default schema (classification disabled for simplicity)
            document_type = "general_document"
            schema_name = self.classification_config.get("default_schema", "test_schema")
            confidence = 1.0  # High confidence for default schema
            classifier_type = "default"

        # Load the appropriate schema
        schema = self._load_schema(schema_name)

        # Prepare data for extraction stage
        classified_data = parsed_data.copy()
        classified_data.update({
            "document_type": document_type,
            "schema": schema,
            "schema_name": schema_name
        })

        # Add classification metadata
        metadata = {
            "classified_at": item.created_at,
            "document_type": document_type,
            "schema_name": schema_name,
            "confidence": confidence,
            "classifier_type": classifier_type,
            "classification_enabled": classification_enabled
        }

        return item.to_next_stage(data=classified_data, metadata=metadata)

    def _load_schema(self, schema_name: str) -> Optional[Schema]:
        """Load schema by name."""
        try:
            return Schema.get_by_name(schema_name)
        except Exception as e:
            logger.warning(f"Failed to load schema {schema_name}: {e}")
            # Return default schema
            try:
                return Schema.get_by_name("test_schema")
            except Exception:
                logger.error("Failed to load default schema 'test_schema'")
                return None


class ExtractWorker(PipelineWorker):
    """Worker for extraction stage (Q3 -> E1...En -> Results)."""

    def __init__(self, worker_id: str, config: StageConfig, extractor_config: Dict[str, Any] = None):
        super().__init__(StageType.EXTRACT, worker_id, config)
        self.extractor_config = extractor_config or {}
        self.extractor = None

    async def process(self, item: PipelineItem) -> PipelineItem:
        """Extract structured data using the appropriate extractor."""
        if self.extractor is None:
            self.extractor = ExtractorFactory.create_llm_extractor(**self.extractor_config)

        classified_data = item.data
        text = classified_data.get("text", "")
        schema = classified_data.get("schema")
        doc_table = classified_data.get("doc_table")

        if not schema:
            raise ValueError("No schema provided for extraction")

        logger.debug(f"Extracting data for item {item.id} using schema {schema.name}")

        # Convert schema to dict for extraction
        schema_dict = schema.to_dict() if hasattr(schema, "to_dict") else schema

        # Perform extraction
        extraction_result = self.extractor.extract(text, schema_dict)

        # Create enhanced result with source traceability
        if doc_table is not None:
            enhanced_result = self._create_enhanced_result(
                extraction_result, doc_table, classified_data, item.metadata
            )
        else:
            enhanced_result = extraction_result

        # Prepare final result
        final_data = {
            "extraction_result": enhanced_result,
            "raw_result": extraction_result,
            "document_metadata": {
                "file_path": classified_data.get("file_path"),
                "document_type": classified_data.get("document_type"),
                "schema_name": classified_data.get("schema_name")
            }
        }

        # Add extraction metadata
        metadata = {
            "extracted_at": item.created_at,
            "extractor_type": self.extractor.__class__.__name__,
            "model_info": getattr(self.extractor, 'model_info', {})
        }

        return item.to_next_stage(data=final_data, metadata=metadata)

    def _create_enhanced_result(self, extraction_result: Dict[str, Any],
                              doc_table, classified_data: Dict[str, Any],
                              item_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Create enhanced result with source traceability."""
        try:
            from ..outputs.formatter import OutputFormatter

            return OutputFormatter.create_enhanced_result(
                extracted_data=extraction_result,
                doc_table=doc_table,
                document_id=classified_data.get("file_path", "unknown"),
                schema_id=classified_data.get("schema_name", "unknown"),
                extraction_time=0.0,  # Will be calculated by formatter
                document_metadata=classified_data.get("document_metadata", {}),
                model_info=getattr(self.extractor, 'model_info', {})
            )
        except Exception as e:
            logger.warning(f"Failed to create enhanced result: {e}")
            return extraction_result


# Worker factory functions
def create_parse_worker(worker_id: str, config: StageConfig, **kwargs) -> ParseWorker:
    """Factory function for parse workers."""
    return ParseWorker(worker_id, config, kwargs.get("parser_config", {}))


def create_classify_worker(worker_id: str, config: StageConfig, **kwargs) -> ClassifyWorker:
    """Factory function for classify workers."""
    return ClassifyWorker(worker_id, config, kwargs.get("classification_rules", {}))


def create_extract_worker(worker_id: str, config: StageConfig, **kwargs) -> ExtractWorker:
    """Factory function for extract workers."""
    return ExtractWorker(worker_id, config, kwargs.get("extractor_config", {}))
