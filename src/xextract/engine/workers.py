"""Pipeline workers for different processing stages.

Optimized for "Read Once, Process in Memory, Write at End" pattern:
- ParseWorker: Reads file once, processes in memory
- ClassifyWorker: Works with in-memory data, no I/O
- ExtractWorker: Works with in-memory data, writes final result
"""

import logging
import time
from pathlib import Path
from typing import Any, Dict, Optional, Union

from .pipeline import PipelineWorker, PipelineItem, PipelineData, StageType, StageConfig
from ..parsers.factory import PDFParserFactory
from ..extractors.factory import ExtractorFactory
from ..schema import Schema

logger = logging.getLogger(__name__)


class ParseWorker(PipelineWorker):
    """Worker for parsing stage - Read Once, Process in Memory."""

    def __init__(self, worker_id: str, config: StageConfig, parser_config: Dict[str, Any] = None):
        super().__init__(StageType.PARSE, worker_id, config)
        self.parser_config = parser_config or {}
        self.parser = None

    async def process(self, item: PipelineItem) -> PipelineItem:
        """Parse document from file path, load all data into memory."""
        start_time = time.time()

        if self.parser is None:
            self.parser = PDFParserFactory.create_parser(**self.parser_config)

        # Get source path from item data (should be a string path)
        source_path = item.data.source_path if hasattr(item.data, 'source_path') else str(item.data)
        file_path = Path(source_path)

        logger.debug(f"Parsing file: {file_path}")

        try:
            # Step 1: Read file content into memory (Read Once)
            with open(file_path, 'rb') as f:
                item.data.raw_content = f.read()

            # Step 2: Parse the PDF using the API
            parsed_data = await self.parser.parse_pdf(file_path)
            item.data.parsed_data = parsed_data

            # Step 3: Convert to document structure in memory (no temp files)
            from ..parsers.document.analyzer.doc_json import DocJsonAnalyzer

            # Create analyzer and process data directly in memory
            analyzer = DocJsonAnalyzer()

            # Instead of using temp file, pass parsed_data directly
            # We need to modify DocJsonAnalyzer to accept dict input
            doc_tree = analyzer.analyze_dict(parsed_data)

            # Store all processed data in memory
            item.data.doc_tree = doc_tree
            item.data.doc_table = doc_tree.get_doc_table()
            item.data.structured_text = doc_tree.to_md_with_id()

            # Update metadata
            processing_time = time.time() - start_time
            item.add_processing_time("parse", processing_time)

            stage_metadata = {
                "parsed_at": time.time(),
                "parser_type": self.parser.__class__.__name__,
                "file_size": len(item.data.raw_content),
                "processing_time": processing_time
            }

            logger.debug(f"Parsed file {file_path} in {processing_time:.2f}s")
            return item.to_next_stage(stage_metadata=stage_metadata)

        except Exception as e:
            error_msg = f"Failed to parse {file_path}: {str(e)}"
            logger.error(error_msg)
            item.add_processing_error("parse", error_msg)
            item.error = error_msg
            return item


class ClassifyWorker(PipelineWorker):
    """Worker for classification stage - Pure in-memory processing."""

    def __init__(self, worker_id: str, config: StageConfig, classification_rules: Dict[str, Any] = None):
        super().__init__(StageType.CLASSIFY, worker_id, config)
        self.classifier = None
        self.classification_config = classification_rules or {}

    async def process(self, item: PipelineItem) -> PipelineItem:
        """Classify document using in-memory data."""
        start_time = time.time()

        # Get structured text from memory (no I/O)
        text = item.data.structured_text or ""

        logger.debug(f"Classifying document for item {item.id}")

        try:
            # Check if classification is enabled
            classification_enabled = self.classification_config.get("classification_enabled", False)

            if classification_enabled and self.classification_config.get("classification_rules"):
                # Use classifier for document classification
                if self.classifier is None:
                    from ..classifiers.factory import ClassifierFactory
                    self.classifier = ClassifierFactory.create_rule_classifier(config=self.classification_config)

                classification_info = self.classifier.get_classification_info(text)
                document_type = classification_info["document_type"]
                confidence = classification_info["confidence"]
                schema_name = classification_info["schema_name"]
                classifier_type = "rule_based"
            else:
                # Use default schema (classification disabled for simplicity)
                document_type = "general_document"
                schema_name = self.classification_config.get("default_schema", "test_schema")
                confidence = 1.0  # High confidence for default schema
                classifier_type = "default"

            # Load the appropriate schema
            schema = self._load_schema(schema_name)

            # Update pipeline data in memory
            item.data.document_type = document_type
            item.data.schema_name = schema_name
            item.data.schema_object = schema
            item.data.classification_confidence = confidence

            # Update metadata
            processing_time = time.time() - start_time
            item.add_processing_time("classify", processing_time)

            stage_metadata = {
                "classified_at": time.time(),
                "document_type": document_type,
                "schema_name": schema_name,
                "confidence": confidence,
                "classifier_type": classifier_type,
                "classification_enabled": classification_enabled,
                "processing_time": processing_time
            }

            logger.debug(f"Classified document as '{document_type}' with schema '{schema_name}' in {processing_time:.2f}s")
            return item.to_next_stage(stage_metadata=stage_metadata)

        except Exception as e:
            error_msg = f"Failed to classify document: {str(e)}"
            logger.error(error_msg)
            item.add_processing_error("classify", error_msg)
            item.error = error_msg
            return item

    def _load_schema(self, schema_name: str) -> Optional[Schema]:
        """Load schema by name."""
        try:
            return Schema.get_by_name(schema_name)
        except Exception as e:
            logger.warning(f"Failed to load schema {schema_name}: {e}")
            # Return default schema
            try:
                return Schema.get_by_name("test_schema")
            except Exception:
                logger.error("Failed to load default schema 'test_schema'")
                return None


class ExtractWorker(PipelineWorker):
    """Worker for extraction stage - Process in Memory, Write at End."""

    def __init__(self, worker_id: str, config: StageConfig, extractor_config: Dict[str, Any] = None):
        super().__init__(StageType.EXTRACT, worker_id, config)
        self.extractor_config = extractor_config or {}
        self.extractor = None

    async def process(self, item: PipelineItem) -> PipelineItem:
        """Extract structured data and write final result."""
        start_time = time.time()

        if self.extractor is None:
            self.extractor = ExtractorFactory.create_llm_extractor(**self.extractor_config)

        # Get all data from memory (no I/O until final write)
        text = item.data.structured_text or ""
        schema = item.data.schema_object
        doc_table = item.data.doc_table

        if not schema:
            raise ValueError("No schema provided for extraction")

        logger.debug(f"Extracting data for item {item.id} using schema {item.data.schema_name}")

        try:
            # Convert schema to dict for extraction
            schema_dict = schema.to_dict() if hasattr(schema, "to_dict") else schema

            # Perform extraction using in-memory data
            extraction_result = self.extractor.extract(text, schema_dict)

            # Create enhanced result with source traceability
            if doc_table is not None:
                enhanced_result = self._create_enhanced_result(
                    extraction_result, doc_table, item.data
                )
            else:
                enhanced_result = extraction_result

            # Store results in pipeline data
            item.data.extraction_result = extraction_result
            item.data.enhanced_result = enhanced_result

            # Update metadata
            processing_time = time.time() - start_time
            item.add_processing_time("extract", processing_time)

            stage_metadata = {
                "extracted_at": time.time(),
                "extractor_type": self.extractor.__class__.__name__,
                "model_info": getattr(self.extractor, 'model_info', {}),
                "processing_time": processing_time
            }

            logger.debug(f"Extracted data for item {item.id} in {processing_time:.2f}s")

            # This is the final stage - prepare complete result
            final_result = self._prepare_final_result(item)

            # Create a new item with final result for output
            final_item = PipelineItem(
                id=item.id,
                data=final_result,  # Final result data
                metadata=item.metadata,
                stage=StageType.EXTRACT,
                created_at=item.created_at
            )
            final_item.metadata.update(stage_metadata)

            return final_item

        except Exception as e:
            error_msg = f"Failed to extract data: {str(e)}"
            logger.error(error_msg)
            item.add_processing_error("extract", error_msg)
            item.error = error_msg
            return item

    def _prepare_final_result(self, item: PipelineItem) -> Dict[str, Any]:
        """Prepare the final result with all processing information."""
        return {
            "task_id": item.id,
            "source_path": item.data.source_path,
            "document_type": item.data.document_type,
            "schema_name": item.data.schema_name,
            "classification_confidence": item.data.classification_confidence,
            "extraction_result": item.data.enhanced_result or item.data.extraction_result,
            "raw_extraction": item.data.extraction_result,
            "processing_times": item.data.processing_times,
            "processing_errors": item.data.processing_errors,
            "total_processing_time": sum(item.data.processing_times.values()),
            "status": "error" if item.data.processing_errors else "success"
        }

    def _create_enhanced_result(self, extraction_result: Dict[str, Any],
                              doc_table, pipeline_data: PipelineData) -> Dict[str, Any]:
        """Create enhanced result with source traceability."""
        try:
            from ..outputs.formatter import OutputFormatter

            return OutputFormatter.create_enhanced_result(
                extracted_data=extraction_result,
                doc_table=doc_table,
                document_id=pipeline_data.source_path,
                schema_id=pipeline_data.schema_name,
                extraction_time=pipeline_data.processing_times.get("extract", 0.0),
                document_metadata={
                    "source_path": pipeline_data.source_path,
                    "document_type": pipeline_data.document_type,
                    "schema_name": pipeline_data.schema_name
                },
                model_info=getattr(self.extractor, 'model_info', {})
            )
        except Exception as e:
            logger.warning(f"Failed to create enhanced result: {e}")
            return extraction_result


# Worker factory functions
def create_parse_worker(worker_id: str, config: StageConfig, **kwargs) -> ParseWorker:
    """Factory function for parse workers."""
    return ParseWorker(worker_id, config, kwargs.get("parser_config", {}))


def create_classify_worker(worker_id: str, config: StageConfig, **kwargs) -> ClassifyWorker:
    """Factory function for classify workers."""
    return ClassifyWorker(worker_id, config, kwargs.get("classification_rules", {}))


def create_extract_worker(worker_id: str, config: StageConfig, **kwargs) -> ExtractWorker:
    """Factory function for extract workers."""
    return ExtractWorker(worker_id, config, kwargs.get("extractor_config", {}))
