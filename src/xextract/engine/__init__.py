"""Engine module for XExtract.

This module provides the main orchestration layer for document extraction.

Available drivers:
- FileExtractorDriver: Legacy sequential processing (maintained for compatibility)
- PipelineExtractorDriver: Multi-stage pipeline processing with queues and workers

The pipeline approach is recommended for high-throughput scenarios requiring
better scalability, fault tolerance, and observability.
"""

from .driver import FileExtractorDriver, PipelineExtractorDriver

__all__ = ["FileExtractorDriver", "PipelineExtractorDriver"]