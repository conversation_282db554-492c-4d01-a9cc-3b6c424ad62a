"""Extractor factory module for XExtract.

This module provides factory classes for creating extractors.
"""

import logging
from typing import Any, Dict, Optional, Type

from ..extractors.base import BaseExtractor
from ..extractors.llm_extractor import LLMExtractor
from ..extractors.rule_extractor import RuleExtractor
from ..llm import LLM, create_llm

logger = logging.getLogger(__name__)


class ExtractorFactory:
    """Factory for creating extractors."""

    # 注册的提取器类
    _extractors: Dict[str, Type[BaseExtractor]] = {}

    @classmethod
    def register(cls, name: str, extractor_class: Type[BaseExtractor]) -> None:
        """注册提取器类。

        Args:
            name: 提取器名称
            extractor_class: 提取器类
        """
        cls._extractors[name] = extractor_class
        logger.debug(f"Registered extractor: {name}")

    @classmethod
    def create(cls, extractor_type: str, **kwargs) -> BaseExtractor:
        """创建提取器。

        Args:
            extractor_type: 提取器类型
            **kwargs: 提取器参数

        Returns:
            提取器实例

        Raises:
            ValueError: 如果提取器类型不存在
        """
        if extractor_type not in cls._extractors:
            raise ValueError(f"Extractor type not found: {extractor_type}")

        extractor_class = cls._extractors[extractor_type]
        return extractor_class(**kwargs)

    @classmethod
    def create_llm_extractor(cls, llm: Optional[LLM] = None, **kwargs) -> LLMExtractor:
        """创建LLM提取器。

        Args:
            llm: LLM实例（如果提供，将直接使用）
            **kwargs: 其他参数，可以包含以下LLM配置参数：
                - llm_provider: LLM提供商 (默认: "openai")
                - llm_api_key: API密钥
                - llm_model: 模型名称 (默认: "gpt-3.5-turbo")
                - llm_api_base_url: API基础URL
                - llm_timeout: 超时时间（秒）(默认: 60)
                - llm_max_retries: 最大重试次数 (默认: 3)
                - llm_temperature: 温度 (默认: 0.0)
                - llm_max_tokens: 最大令牌数

        Returns:
            LLM提取器实例
        """
        # 如果提供了LLM实例，直接使用
        if llm:
            return LLMExtractor(llm=llm, **kwargs)

        # 从kwargs中获取LLM配置参数
        llm_provider = kwargs.pop("llm_provider", kwargs.pop("provider", "openrouter"))
        llm_api_key = kwargs.pop("llm_api_key", kwargs.pop("api_key", None))
        llm_model = kwargs.pop("llm_model", kwargs.pop("model", "gpt-3.5-turbo"))
        llm_api_base_url = kwargs.pop(
            "llm_api_base_url", kwargs.pop("api_base_url", None)
        )
        llm_timeout = kwargs.pop("llm_timeout", kwargs.pop("timeout", 60))
        llm_max_retries = kwargs.pop("llm_max_retries", kwargs.pop("max_retries", 3))
        llm_temperature = kwargs.pop("llm_temperature", kwargs.pop("temperature", 0.0))
        llm_max_tokens = kwargs.pop("llm_max_tokens", kwargs.pop("max_tokens", 1024))

        # 创建LLM配置
        llm_config = {
            "api_key": llm_api_key,
            "model": llm_model,
            "api_base_url": llm_api_base_url,  # 使用 api_base_url 而不是 base_url
            "timeout": llm_timeout,
            "max_retries": llm_max_retries,
            "temperature": llm_temperature,
            "max_tokens": llm_max_tokens,
        }

        # 创建LLM实例
        llm = create_llm(llm_provider, **llm_config)

        # 创建LLM提取器
        return LLMExtractor(llm=llm, **kwargs)

    @classmethod
    def create_rule_extractor(cls, **kwargs) -> RuleExtractor:
        """创建规则提取器。

        Args:
            **kwargs: 规则提取器参数

        Returns:
            规则提取器实例
        """
        return RuleExtractor(**kwargs)


# 注册提取器
ExtractorFactory.register("llm", LLMExtractor)
ExtractorFactory.register("rule", RuleExtractor)
