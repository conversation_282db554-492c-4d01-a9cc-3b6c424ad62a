"""Pipeline-based extraction engine for XExtract.

This module implements a multi-stage pipeline architecture for document processing:
1. Parse Queue (Q1) -> Parsers (P1...Pn) -> Classify Queue (Q2)
2. Classify Queue (Q2) -> Classifiers (C1...Cn) -> Extract Queue (Q3)
3. Extract Queue (Q3) -> Extractors (E1...En) -> Results

The pipeline provides:
- Decoupled processing stages
- Elastic scaling per stage
- Fault tolerance
- Backpressure handling
- Observability
"""

import asyncio
import logging
import time
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Callable
from queue import Queue
import threading
import uuid

logger = logging.getLogger(__name__)


class StageType(Enum):
    """Pipeline stage types."""

    PARSE = "parse"
    EXTRACT = "extract"


@dataclass
class PipelineData:
    """Data container for pipeline processing."""

    # Original input
    source_path: str = ""
    source_type: str = "pdf"  # pdf, doc_json, folder

    # Raw data (loaded once at the beginning)
    raw_content: Optional[bytes] = None
    raw_text: Optional[str] = None

    # Parsed data (after parsing stage)
    parsed_data: Optional[Dict[str, Any]] = None
    doc_tree: Optional[Any] = None  # NodeTree object
    doc_table: Optional[Any] = None  # DataFrame
    structured_text: Optional[str] = None  # Markdown with IDs

    # Schema data (no classification needed)
    document_type: str = "general_document"
    schema_name: str = "shareholder_meeting_notice"  # 使用现有的schema
    schema_object: Optional[Any] = None

    # Extraction data (after extraction stage)
    extraction_result: Optional[Dict[str, Any]] = None
    enhanced_result: Optional[Dict[str, Any]] = None

    # Processing metadata
    processing_times: Dict[str, float] = field(default_factory=dict)
    processing_errors: Dict[str, str] = field(default_factory=dict)


@dataclass
class PipelineItem:
    """Item flowing through the pipeline with optimized data handling."""

    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    data: PipelineData = field(default_factory=PipelineData)
    metadata: Dict[str, Any] = field(default_factory=dict)
    stage: StageType = StageType.PARSE
    created_at: float = field(default_factory=time.time)
    error: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    is_fatal_error: bool = False

    def to_next_stage(self, stage_metadata: Dict[str, Any] = None) -> "PipelineItem":
        """Create item for next stage."""
        next_stages = {
            StageType.PARSE: StageType.EXTRACT,
            StageType.EXTRACT: None,
        }

        next_stage = next_stages.get(self.stage)
        if next_stage is None:
            raise ValueError(f"No next stage after {self.stage}")

        new_metadata = self.metadata.copy()
        if stage_metadata:
            new_metadata.update(stage_metadata)

        return PipelineItem(
            id=self.id,
            data=self.data,  # Same data object, modified in-place
            metadata=new_metadata,
            stage=next_stage,
            created_at=self.created_at,
        )

    def add_processing_time(self, stage: str, duration: float):
        """Add processing time for a stage."""
        self.data.processing_times[stage] = duration

    def add_processing_error(self, stage: str, error: str):
        """Add processing error for a stage."""
        self.data.processing_errors[stage] = error

    def can_retry(self) -> bool:
        """Check if item can be retried."""
        return not self.is_fatal_error and self.retry_count < self.max_retries

    def mark_for_retry(self, error_msg: str) -> "PipelineItem":
        """Mark item for retry and reset to current stage."""
        self.retry_count += 1
        self.error = error_msg
        logger.warning(f"Item {self.id} retry {self.retry_count}/{self.max_retries}: {error_msg}")
        return self

    def mark_fatal_error(self, error_msg: str):
        """Mark item as having a fatal error that should not be retried."""
        self.is_fatal_error = True
        self.error = error_msg
        logger.error(f"Item {self.id} fatal error: {error_msg}")

    @staticmethod
    def is_retryable_error(error: Exception) -> bool:
        """Determine if an error is retryable."""
        # Network/API errors are usually retryable
        retryable_errors = [
            "timeout", "connection", "network", "503", "502", "500",
            "rate limit", "quota", "temporary", "unavailable"
        ]

        error_str = str(error).lower()
        return any(keyword in error_str for keyword in retryable_errors)


@dataclass
class StageConfig:
    """Configuration for a pipeline stage."""

    name: str
    worker_count: int = 1
    queue_size: int = 100
    timeout: float = 60.0
    retry_count: int = 3


@dataclass
class PipelineStats:
    """Pipeline statistics."""

    items_processed: Dict[StageType, int] = field(default_factory=dict)
    items_failed: Dict[StageType, int] = field(default_factory=dict)
    queue_sizes: Dict[StageType, int] = field(default_factory=dict)
    processing_times: Dict[StageType, List[float]] = field(default_factory=dict)

    def __post_init__(self):
        for stage in StageType:
            self.items_processed.setdefault(stage, 0)
            self.items_failed.setdefault(stage, 0)
            self.queue_sizes.setdefault(stage, 0)
            self.processing_times.setdefault(stage, [])


class PipelineWorker:
    """Base class for pipeline workers."""

    def __init__(self, stage: StageType, worker_id: str, config: StageConfig):
        self.stage = stage
        self.worker_id = worker_id
        self.config = config
        self.is_running = False
        self._thread = None

    async def process(self, item: PipelineItem) -> PipelineItem:
        """Process a pipeline item. Override in subclasses."""
        raise NotImplementedError

    def start(self, input_queue: Queue, output_queue: Queue, stats: PipelineStats, dead_letter_queue: Queue = None):
        """Start the worker thread."""
        self.is_running = True
        self._thread = threading.Thread(
            target=self._worker_loop,
            args=(input_queue, output_queue, stats, dead_letter_queue),
            daemon=True,
        )
        self._thread.start()

    def stop(self):
        """Stop the worker."""
        self.is_running = False
        if self._thread:
            self._thread.join(timeout=5.0)

    def _worker_loop(
        self, input_queue: Queue, output_queue: Queue, stats: PipelineStats, dead_letter_queue: Queue = None
    ):
        """Main worker loop."""
        while self.is_running:
            try:
                # Get item from input queue with timeout
                item = input_queue.get(timeout=1.0)
                if item is None:  # Shutdown signal
                    break

                start_time = time.time()

                # Process the item
                try:
                    processed_item = asyncio.run(self.process(item))

                    # Clear error on successful processing
                    processed_item.error = None

                    output_queue.put(processed_item)
                    stats.items_processed[self.stage] += 1

                    # Record processing time
                    processing_time = time.time() - start_time
                    stats.processing_times[self.stage].append(processing_time)

                except Exception as e:
                    error_msg = f"Worker {self.worker_id} failed to process item {item.id}: {e}"
                    logger.error(error_msg)

                    # Determine if error is retryable
                    if PipelineItem.is_retryable_error(e) and item.can_retry():
                        # Retry: put back to same stage queue
                        retry_item = item.mark_for_retry(str(e))
                        input_queue.put(retry_item)
                        logger.info(f"Item {item.id} queued for retry {retry_item.retry_count}/{retry_item.max_retries}")
                    else:
                        # Fatal error or max retries reached
                        if not item.can_retry():
                            item.mark_fatal_error(f"Max retries ({item.max_retries}) exceeded: {str(e)}")
                        else:
                            item.mark_fatal_error(str(e))

                        # Send to dead letter queue if available, otherwise to output
                        if dead_letter_queue is not None:
                            dead_letter_queue.put(item)
                            logger.error(f"Item {item.id} sent to dead letter queue: {item.error}")
                        else:
                            output_queue.put(item)

                        stats.items_failed[self.stage] += 1

                finally:
                    input_queue.task_done()
            except:
                # Queue timeout is expected when no items are available
                # Only log if there are actual errors, not timeouts
                pass


class ExtractionPipeline:
    """Multi-stage extraction pipeline."""

    def __init__(
        self,
        parse_config: StageConfig,
        extract_config: StageConfig,
    ):
        """Initialize the pipeline.

        Args:
            parse_config: Configuration for parsing stage
            extract_config: Configuration for extraction stage
        """
        self.configs = {
            StageType.PARSE: parse_config,
            StageType.EXTRACT: extract_config,
        }

        # Create queues
        self.queues = {
            StageType.PARSE: Queue(maxsize=parse_config.queue_size),
            StageType.EXTRACT: Queue(maxsize=extract_config.queue_size),
        }

        # Results queue (unbounded)
        self.results_queue = Queue()

        # Dead letter queue for failed items
        self.dead_letter_queue = Queue()

        # Workers
        self.workers: Dict[StageType, List[PipelineWorker]] = {
            stage: [] for stage in StageType
        }

        # Statistics
        self.stats = PipelineStats()

        # Control
        self.is_running = False

    def register_worker_factory(
        self, stage: StageType, factory: Callable[[str, StageConfig], PipelineWorker]
    ):
        """Register a worker factory for a stage."""
        self.worker_factories = getattr(self, "worker_factories", {})
        self.worker_factories[stage] = factory

    def start(self):
        """Start the pipeline."""
        if self.is_running:
            return

        self.is_running = True

        # Create and start workers for each stage
        for stage, config in self.configs.items():
            factory = getattr(self, "worker_factories", {}).get(stage)
            if not factory:
                logger.warning(f"No worker factory registered for stage {stage}")
                continue

            for i in range(config.worker_count):
                worker_id = f"{stage.value}-{i}"
                worker = factory(worker_id, config)

                # Determine input and output queues
                input_queue = self.queues[stage]
                if stage == StageType.EXTRACT:
                    output_queue = self.results_queue
                else:
                    next_stage = {
                        StageType.PARSE: StageType.EXTRACT,
                    }[stage]
                    output_queue = self.queues[next_stage]

                worker.start(input_queue, output_queue, self.stats)
                self.workers[stage].append(worker)

        logger.info("Pipeline started successfully")

    def stop(self):
        """Stop the pipeline."""
        if not self.is_running:
            return

        self.is_running = False

        # Stop all workers
        for stage_workers in self.workers.values():
            for worker in stage_workers:
                worker.stop()

        # Clear workers
        for stage in StageType:
            self.workers[stage].clear()

        logger.info("Pipeline stopped")

    def submit(self, source_path: str, metadata: Dict[str, Any] = None) -> str:
        """Submit a file path to the pipeline.

        Args:
            source_path: Path to the file to process (PDF, doc.json, or folder)
            metadata: Optional metadata

        Returns:
            Item ID for tracking
        """
        if not self.is_running:
            raise RuntimeError("Pipeline is not running")

        # Auto-detect source type based on file extension and path
        from pathlib import Path

        path = Path(source_path)

        if path.is_dir():
            source_type = "folder"
        elif path.suffix.lower() == ".pdf":
            source_type = "pdf"
        elif path.suffix.lower() == ".json":
            source_type = "doc_json"
        else:
            source_type = "pdf"  # Default to PDF

        # Create pipeline data with source path and detected type
        pipeline_data = PipelineData(source_path=source_path, source_type=source_type)

        # Set schema from metadata if provided
        if metadata and "schema_name" in metadata:
            pipeline_data.schema_name = metadata["schema_name"]

        item = PipelineItem(
            data=pipeline_data, metadata=metadata or {}, stage=StageType.PARSE
        )

        self.queues[StageType.PARSE].put(item)
        return item.id

    def get_result(self, timeout: float = None) -> Optional[PipelineItem]:
        """Get a result from the pipeline.

        Args:
            timeout: Timeout in seconds

        Returns:
            Processed item or None if timeout
        """
        try:
            return self.results_queue.get(timeout=timeout)
        except:
            return None

    def get_failed_item(self, timeout: float = None) -> Optional[PipelineItem]:
        """Get a failed item from the dead letter queue.

        Args:
            timeout: Timeout in seconds

        Returns:
            Failed item or None if timeout
        """
        try:
            return self.dead_letter_queue.get(timeout=timeout)
        except:
            return None

    def get_failed_items_count(self) -> int:
        """Get the number of items in the dead letter queue."""
        return self.dead_letter_queue.qsize()

    def get_stats(self) -> PipelineStats:
        """Get pipeline statistics."""
        # Update queue sizes
        for stage, queue in self.queues.items():
            self.stats.queue_sizes[stage] = queue.qsize()

        return self.stats
