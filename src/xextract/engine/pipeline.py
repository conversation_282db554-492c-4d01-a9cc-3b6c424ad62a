"""Pipeline-based extraction engine for XExtract.

This module implements a multi-stage pipeline architecture for document processing:
1. Parse Queue (Q1) -> Parsers (P1...Pn) -> Classify Queue (Q2)
2. Classify Queue (Q2) -> Classifiers (C1...Cn) -> Extract Queue (Q3)  
3. Extract Queue (Q3) -> Extractors (E1...En) -> Results

The pipeline provides:
- Decoupled processing stages
- Elastic scaling per stage
- Fault tolerance
- Backpressure handling
- Observability
"""

import asyncio
import logging
import time
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Callable
from queue import Queue
import threading
import uuid

logger = logging.getLogger(__name__)


class StageType(Enum):
    """Pipeline stage types."""
    PARSE = "parse"
    CLASSIFY = "classify"
    EXTRACT = "extract"


@dataclass
class PipelineItem:
    """Item flowing through the pipeline."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    data: Any = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    stage: StageType = StageType.PARSE
    created_at: float = field(default_factory=time.time)
    error: Optional[str] = None
    
    def to_next_stage(self, data: Any = None, metadata: Dict[str, Any] = None) -> 'PipelineItem':
        """Create item for next stage."""
        next_stages = {
            StageType.PARSE: StageType.CLASSIFY,
            StageType.CLASSIFY: StageType.EXTRACT,
            StageType.EXTRACT: None
        }
        
        next_stage = next_stages.get(self.stage)
        if next_stage is None:
            raise ValueError(f"No next stage after {self.stage}")
            
        new_metadata = self.metadata.copy()
        if metadata:
            new_metadata.update(metadata)
            
        return PipelineItem(
            id=self.id,
            data=data if data is not None else self.data,
            metadata=new_metadata,
            stage=next_stage,
            created_at=self.created_at
        )


@dataclass
class StageConfig:
    """Configuration for a pipeline stage."""
    name: str
    worker_count: int = 1
    queue_size: int = 100
    timeout: float = 60.0
    retry_count: int = 3


@dataclass
class PipelineStats:
    """Pipeline statistics."""
    items_processed: Dict[StageType, int] = field(default_factory=dict)
    items_failed: Dict[StageType, int] = field(default_factory=dict)
    queue_sizes: Dict[StageType, int] = field(default_factory=dict)
    processing_times: Dict[StageType, List[float]] = field(default_factory=dict)
    
    def __post_init__(self):
        for stage in StageType:
            self.items_processed.setdefault(stage, 0)
            self.items_failed.setdefault(stage, 0)
            self.queue_sizes.setdefault(stage, 0)
            self.processing_times.setdefault(stage, [])


class PipelineWorker:
    """Base class for pipeline workers."""
    
    def __init__(self, stage: StageType, worker_id: str, config: StageConfig):
        self.stage = stage
        self.worker_id = worker_id
        self.config = config
        self.is_running = False
        self._thread = None
        
    async def process(self, item: PipelineItem) -> PipelineItem:
        """Process a pipeline item. Override in subclasses."""
        raise NotImplementedError
        
    def start(self, input_queue: Queue, output_queue: Queue, stats: PipelineStats):
        """Start the worker thread."""
        self.is_running = True
        self._thread = threading.Thread(
            target=self._worker_loop,
            args=(input_queue, output_queue, stats),
            daemon=True
        )
        self._thread.start()
        
    def stop(self):
        """Stop the worker."""
        self.is_running = False
        if self._thread:
            self._thread.join(timeout=5.0)
            
    def _worker_loop(self, input_queue: Queue, output_queue: Queue, stats: PipelineStats):
        """Main worker loop."""
        while self.is_running:
            try:
                # Get item from input queue with timeout
                item = input_queue.get(timeout=1.0)
                if item is None:  # Shutdown signal
                    break
                    
                start_time = time.time()
                
                # Process the item
                try:
                    processed_item = asyncio.run(self.process(item))
                    output_queue.put(processed_item)
                    stats.items_processed[self.stage] += 1
                    
                    # Record processing time
                    processing_time = time.time() - start_time
                    stats.processing_times[self.stage].append(processing_time)
                    
                except Exception as e:
                    logger.error(f"Worker {self.worker_id} failed to process item {item.id}: {e}")
                    item.error = str(e)
                    output_queue.put(item)
                    stats.items_failed[self.stage] += 1
                    
                finally:
                    input_queue.task_done()
                    
            except Exception as e:
                if self.is_running:  # Only log if not shutting down
                    logger.debug(f"Worker {self.worker_id} queue timeout or error: {e}")


class ExtractionPipeline:
    """Multi-stage extraction pipeline."""
    
    def __init__(self, 
                 parse_config: StageConfig,
                 classify_config: StageConfig,
                 extract_config: StageConfig):
        """Initialize the pipeline.
        
        Args:
            parse_config: Configuration for parsing stage
            classify_config: Configuration for classification stage  
            extract_config: Configuration for extraction stage
        """
        self.configs = {
            StageType.PARSE: parse_config,
            StageType.CLASSIFY: classify_config,
            StageType.EXTRACT: extract_config
        }
        
        # Create queues
        self.queues = {
            StageType.PARSE: Queue(maxsize=parse_config.queue_size),
            StageType.CLASSIFY: Queue(maxsize=classify_config.queue_size),
            StageType.EXTRACT: Queue(maxsize=extract_config.queue_size)
        }
        
        # Results queue (unbounded)
        self.results_queue = Queue()
        
        # Workers
        self.workers: Dict[StageType, List[PipelineWorker]] = {
            stage: [] for stage in StageType
        }
        
        # Statistics
        self.stats = PipelineStats()
        
        # Control
        self.is_running = False
        
    def register_worker_factory(self, stage: StageType, factory: Callable[[str, StageConfig], PipelineWorker]):
        """Register a worker factory for a stage."""
        self.worker_factories = getattr(self, 'worker_factories', {})
        self.worker_factories[stage] = factory
        
    def start(self):
        """Start the pipeline."""
        if self.is_running:
            return
            
        self.is_running = True
        
        # Create and start workers for each stage
        for stage, config in self.configs.items():
            factory = getattr(self, 'worker_factories', {}).get(stage)
            if not factory:
                logger.warning(f"No worker factory registered for stage {stage}")
                continue
                
            for i in range(config.worker_count):
                worker_id = f"{stage.value}-{i}"
                worker = factory(worker_id, config)
                
                # Determine input and output queues
                input_queue = self.queues[stage]
                if stage == StageType.EXTRACT:
                    output_queue = self.results_queue
                else:
                    next_stage = {
                        StageType.PARSE: StageType.CLASSIFY,
                        StageType.CLASSIFY: StageType.EXTRACT
                    }[stage]
                    output_queue = self.queues[next_stage]
                
                worker.start(input_queue, output_queue, self.stats)
                self.workers[stage].append(worker)
                
        logger.info("Pipeline started successfully")
        
    def stop(self):
        """Stop the pipeline."""
        if not self.is_running:
            return
            
        self.is_running = False
        
        # Stop all workers
        for stage_workers in self.workers.values():
            for worker in stage_workers:
                worker.stop()
                
        # Clear workers
        for stage in StageType:
            self.workers[stage].clear()
            
        logger.info("Pipeline stopped")
        
    def submit(self, data: Any, metadata: Dict[str, Any] = None) -> str:
        """Submit data to the pipeline.
        
        Args:
            data: Data to process
            metadata: Optional metadata
            
        Returns:
            Item ID for tracking
        """
        if not self.is_running:
            raise RuntimeError("Pipeline is not running")
            
        item = PipelineItem(
            data=data,
            metadata=metadata or {},
            stage=StageType.PARSE
        )
        
        self.queues[StageType.PARSE].put(item)
        return item.id
        
    def get_result(self, timeout: float = None) -> Optional[PipelineItem]:
        """Get a result from the pipeline.
        
        Args:
            timeout: Timeout in seconds
            
        Returns:
            Processed item or None if timeout
        """
        try:
            return self.results_queue.get(timeout=timeout)
        except:
            return None
            
    def get_stats(self) -> PipelineStats:
        """Get pipeline statistics."""
        # Update queue sizes
        for stage, queue in self.queues.items():
            self.stats.queue_sizes[stage] = queue.qsize()
            
        return self.stats
