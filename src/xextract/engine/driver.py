"""File extractor driver for document processing.

This module provides both legacy and pipeline-based drivers for document extraction.

Legacy FileExtractorDriver: Direct sequential processing (maintained for compatibility)
PipelineExtractorDriver: Multi-stage pipeline processing with queues and workers

The pipeline approach provides better scalability, fault tolerance, and observability
for high-throughput document processing scenarios.
"""

import asyncio
import logging
import os
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from pandas import DataFrame

from ..parsers.base import BasePDFParser
from ..parsers.document.node_tree import NodeTree
from ..parsers.factory import PDFParserFactory
from ..schema import Schema
from ..extractors.factory import ExtractorFactory

logger = logging.getLogger(__name__)


class FileExtractorDriver:
    """Driver for coordinating the extraction process.

    This class is responsible for driving all extractors, managing the extraction
    workflow, and coordinating the different components involved in the extraction process.
    """

    def __init__(
        self,
        pdf_parser: Optional[BasePDFParser] = None,
        extractor_type: str = "llm",
        use_cache: bool = True,
        cache_type: str = "file",
        cache_dir: str = ".cache/xextract/pdf",
        **options,
    ):
        """Initialize the file extractor driver.

        Args:
            pdf_parser: PDF parser instance (created if not provided)
            extractor_type: Type of extractor to use ("llm", "rule", or "hybrid")
            confidence_calculator: Confidence calculator instance (created if not provided)
            use_cache: Whether to use cache for PDF parsing (default: True)
            cache_type: Type of cache to use ("memory" or "file") (default: "file")
            cache_dir: Directory to store cache files (default: ".cache/xextract/pdf")
            **options: Additional options for the driver and its components
        """
        # Store options
        self.options = options

        # Get cache settings from options or use defaults
        use_cache = options.get("use_cache", use_cache)
        cache_type = options.get("cache_type", cache_type)
        cache_dir = options.get("cache_dir", cache_dir)

        # Create or use provided PDF parser
        if pdf_parser:
            self.pdf_parser = pdf_parser
        else:
            # Get PDF parser options
            pdf_parser_options = options.get("pdf_parser_options", {})

            # Add cache settings to PDF parser options
            pdf_parser_options.update(
                {
                    "use_cache": use_cache,
                    "cache_type": cache_type,
                    "cache_dir": cache_dir,
                }
            )

            # Create PDF parser
            self.pdf_parser = PDFParserFactory.create_parser(
                parser_type=options.get("pdf_parser_type", "api"), **pdf_parser_options
            )

        # 获取提取器选项
        extractor_options = options.get("extractor_options", {})

        # 创建提取器
        if extractor_type == "llm":
            # 使用 create_llm_extractor 方法创建 LLM 提取器
            # 将所有选项传递给 create_llm_extractor，它会自动提取需要的参数
            self.extractor = ExtractorFactory.create_llm_extractor(
                **options, **extractor_options
            )
        elif extractor_type == "rule":
            # 使用 create_rule_extractor 方法创建规则提取器
            self.extractor = ExtractorFactory.create_rule_extractor(**extractor_options)
        else:
            # 使用通用 create 方法
            self.extractor = ExtractorFactory.create(
                extractor_type=extractor_type, **extractor_options
            )

        # 不再使用置信度计算器
        self.confidence_calculator = None

        logger.info(f"FileExtractorDriver initialized with {extractor_type} extractor")

    async def extract_from_file(
        self,
        file_path: Union[str, Path],
        schema: Union[Schema, Dict[str, Any], str],
        output_path: Optional[Union[str, Path]] = None,
        output_format: str = "json",
        include_metadata: bool = True,
        return_structured_result: bool = False,
        **extraction_options,
    ) -> Union[Dict[str, Any]]:
        """Extract data from a file.

        Args:
            file_path: Path to the file
            schema: Schema for extraction (Schema object, dict, or schema name)
            output_path: Path to save the extraction result
            output_format: Format for saving results ("json", "csv", or "auto")
            include_source_text: Whether to include source text in the result
            include_parsed_data: Whether to include parsed data in the result
            include_metadata: Whether to include metadata in the result
            return_structured_result: Whether to return a structured ExtractionResult object
            **extraction_options: Additional options for extraction

        Returns:
            Extraction result as a dictionary or ExtractionResult object
        """
        start_time = time.time()

        # Ensure file_path is a Path object
        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        # Load schema if it's a string (schema name or path)
        if isinstance(schema, str):
            schema_obj = self._load_schema(schema)
            if not schema_obj:
                raise ValueError(f"Failed to load schema: {schema}")
            schema = schema_obj

        # Parse the file
        logger.info(f"Parsing file: {file_path}")
        parsed_data = await self.pdf_parser.parse_pdf(
            file_path, **(extraction_options.get("parser_options", {}))
        )

        # 使用DocJsonAnalyzer将解析后的数据转换为Markdown
        import json
        import os
        import tempfile

        from ..parsers.document.analyzer.doc_json import DocJsonAnalyzer

        # 创建临时文件保存解析后的数据
        with tempfile.NamedTemporaryFile(
            mode="w", suffix=".json", delete=False
        ) as temp_file:
            json.dump(parsed_data, temp_file, ensure_ascii=False)
            temp_path = temp_file.name

        try:
            # 使用DocJsonAnalyzer分析文档
            analyzer = DocJsonAnalyzer()
            doc: NodeTree = analyzer.analyze(temp_path)
            doc_table: DataFrame = doc.get_doc_table()
            # 获取带有id的markdown，后续用于溯源提取结果在原文档的信息
            text: str = doc.to_md_with_id()

        finally:
            # 删除临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)

        # Extract data
        logger.info(
            f"Extracting data from {file_path} using {self.extractor.__class__.__name__}"
        )

        # Convert Schema object to dict if needed for extraction
        if hasattr(schema, "to_dict"):
            schema_dict = schema.to_dict()
        else:
            schema_dict = schema

        # 记录提取前的参数
        logger.debug(f"Extraction schema type: {type(schema_dict)}")
        logger.debug(
            f"Extraction options: {extraction_options.get('extractor_options', {})}"
        )

        # 准备提取参数，过滤掉driver层面的参数
        extractor_options = extraction_options.get("extractor_options", {}).copy()

        # 移除driver层面的参数，这些不应该传递给提取器
        driver_params = {
            "return_structured_result", "include_metadata", "include_source_text",
            "include_parsed_data", "enhance_with_source"
        }
        for param in driver_params:
            extractor_options.pop(param, None)
        # 准备文档信息（用于结构化结果）
        document_id = str(file_path.stem)
        schema_id = schema.name if hasattr(schema, "name") else "unknown"
        document_metadata = {
            "file": {
                "name": file_path.name,
                "path": str(file_path),
                "size": file_path.stat().st_size,
                "last_modified": file_path.stat().st_mtime,
            }
        }

        # 执行提取
        result = self.extractor.extract(text, schema_dict, **extractor_options)

        # 记录提取结果
        logger.debug(f"Extraction result type: {type(result)}")

        # 处理提取结果
        structured_result = None

        # 确保结果是字典格式
        if isinstance(result, str):
            try:
                import json

                result = json.loads(result)
            except Exception as e:
                logger.warning(f"Failed to parse JSON string: {e}")
                result = {
                    "error": "Failed to parse extraction result",
                    "raw_result": result,
                }
        elif not isinstance(result, dict):
            result = {"result": str(result)}

        logger.debug(f"Extraction result keys: {list(result.keys())}")

        # 创建结构化结果（如果需要）
        if return_structured_result:
            from ..outputs.formatter import OutputFormatter

            extraction_time = time.time() - start_time
            model_info = getattr(self.extractor, 'model_info', {})

            # 获取配置中的bbox设置
            include_bbox = getattr(self.config, 'INCLUDE_BBOX', True)

            # 直接创建增强结果（包含溯源信息）
            structured_result = OutputFormatter.create_enhanced_result(
                extracted_data=result,
                doc_table=doc_table,
                document_id=document_id,
                schema_id=schema_id,
                extraction_time=extraction_time,
                document_metadata=document_metadata,
                model_info=model_info,
                include_bbox=include_bbox,
            )

        # 只在需要时添加基本元数据
        if include_metadata and not return_structured_result:
            result.setdefault("metadata", {}).update({
                "timestamp": time.time(),
                "duration": time.time() - start_time,
                "extractor": self.extractor.__class__.__name__,
            })


        # Save result if output path is provided
        if output_path:
            # 保存时使用字典格式
            if return_structured_result and structured_result:
                # 如果是结构化结果，转换为字典保存
                result_to_save = structured_result.to_dict()
            else:
                result_to_save = result
            self._save_result(result_to_save, output_path, format=output_format)

        # 返回结构化结果或字典
        if return_structured_result and structured_result:
            return structured_result
        else:
            return result

    def extract_from_file_sync(
        self,
        file_path: Union[str, Path],
        schema: Union[Schema, Dict[str, Any], str],
        output_path: Optional[Union[str, Path]] = None,
        output_format: str = "json",
        include_metadata: bool = True,
        return_structured_result: bool = False,
        **extraction_options,
    ) -> Union[Dict[str, Any]]:
        """Synchronous version of extract_from_file.

        Args:
            file_path: Path to the file
            schema: Schema for extraction (Schema object, dict, or schema name)
            output_path: Path to save the extraction result
            output_format: Format for saving results ("json", "csv", or "auto")
            include_source_text: Whether to include source text in the result
            include_parsed_data: Whether to include parsed data in the result
            include_metadata: Whether to include metadata in the result
            return_structured_result: Whether to return a structured ExtractionResult object
            **extraction_options: Additional options for extraction

        Returns:
            Extraction result as a dictionary or ExtractionResult object
        """
        return asyncio.run(
            self.extract_from_file(
                file_path,
                schema,
                output_path,
                output_format,
                include_metadata,
                return_structured_result,
                **extraction_options,
            )
        )

    async def extract_from_directory(
        self,
        directory_path: Union[str, Path],
        schema: Union[Schema, Dict[str, Any], str],
        output_dir: Optional[Union[str, Path]] = None,
        file_pattern: str = "*.pdf",
        max_concurrency: int = 5,
        output_format: str = "json",
        include_source_text: bool = False,
        include_parsed_data: bool = False,
        include_metadata: bool = True,
        summary_file: Optional[Union[str, Path]] = None,
        recursive: bool = False,
        skip_errors: bool = True,
        **extraction_options,
    ) -> List[Dict[str, Any]]:
        """Extract data from all files in a directory.

        Args:
            directory_path: Path to the directory
            schema: Schema for extraction (Schema object, dict, or schema name)
            output_dir: Directory to save extraction results
            file_pattern: Pattern to match files (default: "*.pdf")
            max_concurrency: Maximum number of concurrent extractions
            output_format: Format for saving results ("json", "yaml", "csv", or "auto")
            include_source_text: Whether to include source text in the result
            include_parsed_data: Whether to include parsed data in the result
            include_metadata: Whether to include metadata in the result
            summary_file: Path to save a summary of all extraction results
            recursive: Whether to search for files recursively
            skip_errors: Whether to skip files that cause errors
            **extraction_options: Additional options for extraction

        Returns:
            List of extraction results
        """
        start_time = time.time()

        # Ensure directory_path is a Path object
        directory_path = Path(directory_path)
        if not directory_path.exists() or not directory_path.is_dir():
            raise NotADirectoryError(f"Directory not found: {directory_path}")

        # Create output directory if provided
        if output_dir:
            output_dir = Path(output_dir)
            os.makedirs(output_dir, exist_ok=True)

        # Find all matching files
        if recursive:
            files = list(directory_path.glob(f"**/{file_pattern}"))
        else:
            files = list(directory_path.glob(file_pattern))

        if not files:
            raise ValueError(
                f"No files matching '{file_pattern}' found in {directory_path}"
            )

        logger.info(f"Found {len(files)} files to process in {directory_path}")

        # Load schema if it's a string (schema name or path)
        if isinstance(schema, str):
            schema_obj = self._load_schema(schema)
            if not schema_obj:
                raise ValueError(f"Failed to load schema: {schema}")
            schema = schema_obj

        # Process files with concurrency limit
        semaphore = asyncio.Semaphore(max_concurrency)

        async def process_file(file_path):
            async with semaphore:
                # Determine output path if output directory is provided
                output_path = None
                if output_dir:
                    # Preserve directory structure if recursive
                    if recursive:
                        rel_path = file_path.relative_to(directory_path)
                        output_subdir = output_dir / rel_path.parent
                        os.makedirs(output_subdir, exist_ok=True)
                        output_path = (
                            output_subdir / f"{file_path.stem}.{output_format}"
                        )
                    else:
                        output_path = output_dir / f"{file_path.stem}.{output_format}"

                try:
                    return await self.extract_from_file(
                        file_path,
                        schema,
                        output_path,
                        output_format=output_format,
                        include_source_text=include_source_text,
                        include_parsed_data=include_parsed_data,
                        include_metadata=include_metadata,
                        **extraction_options,
                    )
                except Exception as e:
                    error_msg = f"Error extracting from {file_path}: {e}"
                    logger.error(error_msg)

                    if skip_errors:
                        return {
                            "file": str(file_path),
                            "error": str(e),
                            "status": "error",
                        }
                    else:
                        raise ValueError(error_msg)

        # Process all files concurrently (with semaphore limiting concurrency)
        tasks = [process_file(file) for file in files]
        results = await asyncio.gather(*tasks)

        # Create summary if requested
        if summary_file:
            # Get schema name or representation
            if hasattr(schema, "name") and schema.name:
                schema_repr = schema.name
            elif hasattr(schema, "to_dict"):
                schema_dict = schema.to_dict()
                schema_repr = schema_dict.get("name", str(schema_dict))
            else:
                schema_repr = str(schema)

            summary = {
                "extraction_summary": {
                    "total_files": len(files),
                    "successful_files": sum(1 for r in results if "error" not in r),
                    "failed_files": sum(1 for r in results if "error" in r),
                    "start_time": start_time,
                    "end_time": time.time(),
                    "duration": time.time() - start_time,
                    "schema": schema_repr,
                    "directory": str(directory_path),
                    "file_pattern": file_pattern,
                },
                "results": [
                    {
                        "file": (
                            r.get("metadata", {}).get("file", {}).get("name", "unknown")
                            if "error" not in r
                            else r.get("file", "unknown")
                        ),
                        "status": "success" if "error" not in r else "error",
                        "error": r.get("error") if "error" in r else None,
                    }
                    for r in results
                ],
            }
            # Save summary
            self._save_result(summary, summary_file, format=output_format)
            logger.info(f"Summary saved to {summary_file}")

        return results

    def extract_from_directory_sync(
        self,
        directory_path: Union[str, Path],
        schema: Union[Schema, Dict[str, Any], str],
        output_dir: Optional[Union[str, Path]] = None,
        file_pattern: str = "*.pdf",
        max_concurrency: int = 5,
        output_format: str = "json",
        include_source_text: bool = False,
        include_parsed_data: bool = False,
        include_metadata: bool = True,
        summary_file: Optional[Union[str, Path]] = None,
        recursive: bool = False,
        skip_errors: bool = True,
        **extraction_options,
    ) -> List[Dict[str, Any]]:
        """Synchronous version of extract_from_directory.

        Args:
            directory_path: Path to the directory
            schema: Schema for extraction (Schema object, dict, or schema name)
            output_dir: Directory to save extraction results
            file_pattern: Pattern to match files (default: "*.pdf")
            max_concurrency: Maximum number of concurrent extractions
            output_format: Format for saving results ("json", "yaml", "csv", or "auto")
            include_source_text: Whether to include source text in the result
            include_parsed_data: Whether to include parsed data in the result
            include_metadata: Whether to include metadata in the result
            summary_file: Path to save a summary of all extraction results
            recursive: Whether to search for files recursively
            skip_errors: Whether to skip files that cause errors
            **extraction_options: Additional options for extraction

        Returns:
            List of extraction results
        """
        return asyncio.run(
            self.extract_from_directory(
                directory_path,
                schema,
                output_dir,
                file_pattern,
                max_concurrency,
                output_format,
                include_source_text,
                include_parsed_data,
                include_metadata,
                summary_file,
                recursive,
                skip_errors,
                **extraction_options,
            )
        )

    def _load_schema(self, schema_name: str) -> Optional[Schema]:
        """Load schema from name or path.

        Args:
            schema_name: Schema name or path

        Returns:
            Schema object if successful, None otherwise
        """
        from ..schema import Schema

        # Check if schema_name is a file path
        if os.path.exists(schema_name):
            try:
                # Determine file type based on extension
                if schema_name.endswith(".json"):
                    return Schema.from_json_file(schema_name)
                elif schema_name.endswith((".yaml", ".yml")):
                    return Schema.from_yaml_file(schema_name)
                else:
                    logger.error(f"Unsupported schema file format: {schema_name}")
                    return None
            except Exception as e:
                logger.error(f"Failed to load schema from file {schema_name}: {e}")
                return None
        else:
            # Try to load by name
            schema = Schema.get_by_name(schema_name)
            if not schema:
                logger.error(f"Schema not found: {schema_name}")
                return None
            return schema

    def _save_result(
        self,
        result: Dict[str, Any],
        output_path: Union[str, Path],
        format: str = "json",
        **options,
    ) -> None:
        """Save extraction result to file.

        Args:
            result: Extraction result
            output_path: Path to save the result
            format: Output format ("json", "jsonl", or "csv")
            **options: Additional options for saving
        """
        # 确保 options 是一个字典
        if not isinstance(options, dict):
            options = {}

        # Ensure output_path is a Path object
        output_path = Path(output_path)

        # Create parent directory if it doesn't exist
        os.makedirs(output_path.parent, exist_ok=True)

        # Determine format from file extension if not specified
        if format == "auto":
            ext = output_path.suffix.lower()
            if ext == ".json":
                format = "json"
            elif ext == ".csv":
                format = "csv"
            else:
                format = "json"  # Default to JSON

        # Save result in the specified format
        if format == "json":
            import json

            # 使用 options.get 方法安全地获取参数值
            indent = options.get("indent", 2)
            ensure_ascii = options.get("ensure_ascii", False)
            sort_keys = options.get("sort_keys", False)

            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(
                    result,
                    f,
                    ensure_ascii=ensure_ascii,
                    indent=indent,
                    sort_keys=sort_keys,
                )

            logger.debug(f"Saved result to {output_path} in JSON format")

        elif format == "csv":
            # Flatten nested dictionaries for CSV output
            flat_data = self._flatten_dict(result)

            # Try to use pandas if available
            try:
                # Only import pandas if needed
                import pandas as pd

                # Convert to DataFrame and save as CSV
                df = pd.DataFrame([flat_data])
                df.to_csv(output_path, index=False, encoding="utf-8")
                logger.debug("Saved CSV using pandas")
            except ImportError:
                # Fall back to basic CSV writer
                logger.warning("Pandas not installed. Using basic CSV writer.")
                import csv

                # Basic CSV export for simple structures
                with open(output_path, "w", encoding="utf-8", newline="") as f:
                    writer = csv.writer(f)
                    # Write header
                    writer.writerow(flat_data.keys())
                    # Write values
                    writer.writerow(flat_data.values())
                logger.debug("Saved CSV using basic csv module")

        else:
            raise ValueError(f"Unsupported output format: {format}")

        logger.info(f"Result saved to {output_path} in {format} format")

    def _flatten_dict(
        self, d: Dict[str, Any], parent_key: str = "", sep: str = "."
    ) -> Dict[str, Any]:
        """Flatten a nested dictionary.

        Args:
            d: Dictionary to flatten
            parent_key: Parent key for nested dictionaries
            sep: Separator for keys

        Returns:
            Flattened dictionary
        """
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k

            if isinstance(v, dict):
                items.extend(self._flatten_dict(v, new_key, sep=sep).items())
            elif isinstance(v, list):
                # For lists, create indexed keys
                for i, item in enumerate(v):
                    if isinstance(item, dict):
                        items.extend(
                            self._flatten_dict(item, f"{new_key}[{i}]", sep=sep).items()
                        )
                    else:
                        items.append((f"{new_key}[{i}]", item))
            else:
                items.append((new_key, v))

        return dict(items)


class PipelineExtractorDriver:
    """Pipeline-based extractor driver for high-throughput document processing.

    This driver implements the multi-stage pipeline architecture:
    Input -> Parse Queue -> Parsers -> Classify Queue -> Classifiers -> Extract Queue -> Extractors -> Results

    Benefits:
    - Decoupled processing stages
    - Elastic scaling per stage
    - Fault tolerance
    - Backpressure handling
    - Better observability
    """

    def __init__(self,
                 parse_workers: Optional[int] = None,
                 extract_workers: Optional[int] = None,
                 queue_size: Optional[int] = None,
                 config_override: Optional[Dict[str, Any]] = None,
                 **options):
        """Initialize the pipeline extractor driver.

        Args:
            parse_workers: Number of parsing workers (overrides config)
            extract_workers: Number of extraction workers (overrides config)
            queue_size: Size of each queue (overrides config)
            config_override: Dictionary to override specific config values
            **options: Additional options for workers
        """
        from .pipeline import ExtractionPipeline, StageConfig, StageType
        from .workers import create_parse_worker, create_extract_worker

        # Load configuration
        try:
            from config.config import config
            pipeline_config = config.pipeline
            stage_configs = config.pipeline_stage_configs
        except ImportError:
            # Fallback to default values if config is not available
            pipeline_config = {
                "parse_workers": 2,
                "extract_workers": 2,
                "queue_size": 100,
                "timeout": 300.0,
                "retry_count": 3
            }
            stage_configs = {
                "parse": {"worker_count": 2, "queue_size": 100, "timeout": 120.0, "retry_count": 3},
                "extract": {"worker_count": 2, "queue_size": 100, "timeout": 180.0, "retry_count": 3}
            }

        # Apply overrides
        if config_override:
            pipeline_config.update(config_override)

        # Use provided parameters or config values
        parse_workers = parse_workers or pipeline_config.get("parse_workers", 2)
        extract_workers = extract_workers or pipeline_config.get("extract_workers", 2)
        queue_size = queue_size or pipeline_config.get("queue_size", 100)

        # Create stage configurations
        parse_config = StageConfig(
            "parse",
            parse_workers,
            queue_size,
            timeout=stage_configs["parse"]["timeout"],
            retry_count=stage_configs["parse"]["retry_count"]
        )
        extract_config = StageConfig(
            "extract",
            extract_workers,
            queue_size,
            timeout=stage_configs["extract"]["timeout"],
            retry_count=stage_configs["extract"]["retry_count"]
        )

        # Create pipeline
        self.pipeline = ExtractionPipeline(parse_config, extract_config)

        # Prepare worker configurations
        parser_config = options.get("parser_config", {})
        if not parser_config:
            # Use document parser config from main config
            try:
                parser_config = config.document_parser
            except:
                parser_config = {}

        extractor_config = options.get("extractor_config", {})
        if not extractor_config:
            # Use LLM config from main config
            try:
                llm_config = config.llm
                extractor_config = {
                    "llm_provider": llm_config.get("provider", "openai"),
                    "llm_model": llm_config.get("id", "gpt-3.5-turbo"),
                    "llm_api_key": llm_config.get("api_key", ""),
                    "llm_api_base_url": llm_config.get("api_base_url", ""),
                    "llm_temperature": llm_config.get("temperature", 0.0),
                    "llm_max_tokens": llm_config.get("max_tokens", 4096),
                    "llm_timeout": llm_config.get("timeout", 120),
                    "llm_max_retries": llm_config.get("max_retries", 3)
                }
            except:
                extractor_config = {}

        # Register worker factories
        self.pipeline.register_worker_factory(
            StageType.PARSE,
            lambda worker_id, config: create_parse_worker(
                worker_id, config, parser_config=parser_config
            )
        )
        self.pipeline.register_worker_factory(
            StageType.EXTRACT,
            lambda worker_id, config: create_extract_worker(
                worker_id, config, extractor_config=extractor_config
            )
        )

        self.options = options
        self.pipeline_config = pipeline_config
        logger.info(f"PipelineExtractorDriver initialized with config: {pipeline_config}")

    def start(self):
        """Start the pipeline."""
        self.pipeline.start()

    def stop(self):
        """Stop the pipeline."""
        self.pipeline.stop()

    def __enter__(self):
        """Context manager entry."""
        self.start()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.stop()

    async def extract_from_file(self, file_path: Union[str, Path],
                               schema_name: str = None,
                               timeout: float = 300.0) -> Dict[str, Any]:
        """Extract data from a file using the pipeline.

        Args:
            file_path: Path to the file
            schema_name: Schema name or path to schema file
            timeout: Timeout for processing

        Returns:
            Extraction result
        """
        if not self.pipeline.is_running:
            raise RuntimeError("Pipeline is not running. Call start() first.")

        # Prepare metadata with schema
        metadata = {}
        if schema_name:
            metadata["schema_name"] = schema_name

        # Submit file to pipeline
        item_id = self.pipeline.submit(str(file_path), metadata)
        logger.info(f"Submitted file {file_path} to pipeline with ID {item_id} (schema: {schema_name})")

        # Wait for result
        start_time = time.time()
        while time.time() - start_time < timeout:
            result_item = self.pipeline.get_result(timeout=1.0)
            if result_item and result_item.id == item_id:
                if result_item.error:
                    raise RuntimeError(f"Pipeline processing failed: {result_item.error}")
                return result_item.data

        raise TimeoutError(f"Pipeline processing timed out after {timeout} seconds")

    def extract_from_file_sync(self, file_path: Union[str, Path],
                              schema_name: str = None,
                              timeout: float = 300.0) -> Dict[str, Any]:
        """Synchronous version of extract_from_file."""
        return asyncio.run(self.extract_from_file(file_path, schema_name, timeout))

    async def extract_from_directory(self, directory_path: Union[str, Path],
                                   schema_name: str = None,
                                   file_pattern: str = "*.pdf",
                                   max_concurrent: int = 10,
                                   timeout_per_file: float = 300.0) -> List[Dict[str, Any]]:
        """Extract data from all files in a directory using the pipeline.

        Args:
            directory_path: Path to directory
            schema_name: Schema name or path to schema file
            file_pattern: File pattern to match
            max_concurrent: Maximum concurrent submissions
            timeout_per_file: Timeout per file

        Returns:
            List of extraction results
        """
        if not self.pipeline.is_running:
            raise RuntimeError("Pipeline is not running. Call start() first.")

        directory_path = Path(directory_path)
        files = list(directory_path.glob(file_pattern))

        if not files:
            return []

        logger.info(f"Processing {len(files)} files from {directory_path}")

        # Submit all files
        submitted_items = {}
        semaphore = asyncio.Semaphore(max_concurrent)

        async def submit_file(file_path):
            async with semaphore:
                # Prepare metadata with schema
                metadata = {}
                if schema_name:
                    metadata["schema_name"] = schema_name

                item_id = self.pipeline.submit(str(file_path), metadata)
                submitted_items[item_id] = {
                    "file_path": str(file_path),
                    "submitted_at": time.time()
                }
                return item_id

        # 所有待处理文件加入任务
        item_ids = await asyncio.gather(*[submit_file(f) for f in files])

        # Collect results
        results = []
        collected_ids = set()
        start_time = time.time()

        while len(collected_ids) < len(item_ids) and time.time() - start_time < timeout_per_file * len(files):
            result_item = self.pipeline.get_result(timeout=1.0)
            if result_item and result_item.id in submitted_items and result_item.id not in collected_ids:
                collected_ids.add(result_item.id)

                result_data = result_item.data.copy() if result_item.data else {}
                result_data["file_path"] = submitted_items[result_item.id]["file_path"]

                if result_item.error:
                    result_data["error"] = result_item.error
                    result_data["status"] = "error"
                else:
                    result_data["status"] = "success"

                results.append(result_data)

        # Log any missing results
        missing_count = len(item_ids) - len(collected_ids)
        if missing_count > 0:
            logger.warning(f"{missing_count} files did not complete processing within timeout")

        return results

    def get_stats(self) -> Dict[str, Any]:
        """Get pipeline statistics."""
        stats = self.pipeline.get_stats()
        return {
            "items_processed": dict(stats.items_processed),
            "items_failed": dict(stats.items_failed),
            "queue_sizes": dict(stats.queue_sizes),
            "avg_processing_times": {
                stage.value: sum(times) / len(times) if times else 0.0
                for stage, times in stats.processing_times.items()
            }
        }
