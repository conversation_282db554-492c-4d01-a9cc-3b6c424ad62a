"""Prompt template manager."""
import os
import json
import logging
from typing import Any, Dict, List, Optional, Union
from pathlib import Path

from .template import PromptTemplate

logger = logging.getLogger(__name__)


class PromptManager:
    """Manager for prompt templates."""

    def __init__(
        self,
        templates_dir: Optional[str] = None,
        default_templates: Optional[Dict[str, PromptTemplate]] = None
    ):
        """Initialize the prompt manager.

        Args:
            templates_dir: Directory to load templates from
            default_templates: Default templates to use
        """
        self.templates: Dict[str, PromptTemplate] = {}
        self.templates_dir = templates_dir

        # Load default templates
        if default_templates:
            for name, template in default_templates.items():
                self.add_template(name, template)
        else:
            # Add built-in default templates
            for name, template in self.get_default_templates().items():
                self.add_template(name, template)

        # Load templates from directory
        if templates_dir:
            self.load_from_directory(templates_dir)

    def add_template(
        self,
        name: str,
        template: Union[str, Dict[str, Any], PromptTemplate]
    ) -> None:
        """Add a template to the manager.

        Args:
            name: Name of the template
            template: Template to add (string, dict, or PromptTemplate)
        """
        if isinstance(template, str):
            self.templates[name] = PromptTemplate(template)
        elif isinstance(template, dict):
            self.templates[name] = PromptTemplate.from_dict(template)
        elif isinstance(template, PromptTemplate):
            self.templates[name] = template
        else:
            raise TypeError(
                f"Expected str, dict, or PromptTemplate, got {type(template)}"
            )

    def get_template(self, name: str) -> PromptTemplate:
        """Get a template by name.

        Args:
            name: Name of the template

        Returns:
            The template

        Raises:
            KeyError: If the template is not found
        """
        if name not in self.templates:
            raise KeyError(f"Template not found: {name}")
        return self.templates[name]

    def remove_template(self, name: str) -> bool:
        """Remove a template by name.

        Args:
            name: Name of the template

        Returns:
            True if the template was removed, False if it was not found
        """
        if name in self.templates:
            del self.templates[name]
            return True
        return False

    def format_prompt(self, name: str, **kwargs: Any) -> str:
        """Format a template with the given variables.

        Args:
            name: Name of the template
            **kwargs: Variables to fill in the template

        Returns:
            Formatted prompt
        """
        template = self.get_template(name)
        return template.format(**kwargs)

    def list_templates(self) -> List[str]:
        """List all template names.

        Returns:
            List of template names
        """
        return list(self.templates.keys())

    def load_from_directory(self, directory: str) -> int:
        """Load templates from a directory.

        Args:
            directory: Directory to load templates from

        Returns:
            Number of templates loaded
        """
        path = Path(directory)
        count = 0

        if not path.exists() or not path.is_dir():
            logger.warning(f"Templates directory not found: {directory}")
            return 0

        # 加载JSON格式的模板
        for file_path in path.glob("*.json"):
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    data = json.load(f)

                template = PromptTemplate.from_dict(data)
                name = file_path.stem  # Use filename without extension as name
                self.add_template(name, template)
                count += 1

            except Exception as e:
                logger.error(f"Error loading template from {file_path}: {e}")

        # 加载文本格式的模板
        for file_path in path.glob("**/*.txt"):
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    template_text = f.read()

                # 使用相对路径作为模板名称，替换路径分隔符为下划线
                rel_path = file_path.relative_to(path)
                name = str(rel_path.with_suffix("")).replace(os.sep, "_")

                # 创建模板
                template = PromptTemplate(template_text)
                self.add_template(name, template)
                count += 1

                logger.debug(f"Loaded text template: {name}")

            except Exception as e:
                logger.error(f"Error loading template from {file_path}: {e}")

        return count

    def save_to_directory(self, directory: str) -> int:
        """Save templates to a directory.

        Args:
            directory: Directory to save templates to

        Returns:
            Number of templates saved
        """
        path = Path(directory)
        path.mkdir(exist_ok=True, parents=True)

        count = 0
        for name, template in self.templates.items():
            try:
                file_path = path / f"{name}.json"
                with open(file_path, "w") as f:
                    json.dump(template.to_dict(), f, indent=2)
                count += 1

            except Exception as e:
                logger.error(f"Error saving template {name}: {e}")

        return count

    def get_default_templates(self) -> Dict[str, PromptTemplate]:
        """Get default templates for common tasks.

        Returns:
            Dictionary of default templates
        """
        return {
            "extract_fields": PromptTemplate(
                """
                Extract the following fields from the text below:

                Schema:
                {schema}

                Text:
                {text}

                Provide ONLY the extracted fields in valid JSON format.
                For fields not found in the text, use null or empty values as appropriate.
                """
            ),
            "summarize": PromptTemplate(
                """
                Summarize the following text in {max_words} words or less:

                {text}
                """
            ),
            "extract_table": PromptTemplate(
                """
                Extract the following table from the text as JSON:

                {text}

                Instructions:
                - Convert the table to a JSON array of objects
                - Each object should represent a row in the table
                - Use the table headers as keys
                - Maintain the data types (numbers as numbers, etc.)
                """
            )
        }
