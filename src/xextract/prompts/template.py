"""Prompt template for LLM prompts."""
from typing import Any, Dict, List, Optional, Union


class PromptTemplate:
    """Template for LLM prompts with variable substitution."""
    
    def __init__(
        self,
        template: str,
        variables: Optional[List[str]] = None,
        description: Optional[str] = None,
        version: str = "1.0",
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Initialize prompt template.
        
        Args:
            template: Template string with placeholders
            variables: List of variable names in the template
            description: Optional description
            version: Template version
            metadata: Optional metadata
        """
        self.template = template
        self.description = description
        self.version = version
        self.metadata = metadata or {}
        
        # Extract variables from template if not provided
        if variables is None:
            import re
            # Find all {variable} patterns in the template
            self.variables = list(set(re.findall(r'\{([^{}]+)\}', template)))
        else:
            self.variables = variables
    
    def format(self, **kwargs: Any) -> str:
        """Format the template with the given variables.
        
        Args:
            **kwargs: Variables to fill in the template
            
        Returns:
            Formatted prompt
            
        Raises:
            KeyError: If a required variable is missing
        """
        # Check for missing variables
        missing = [var for var in self.variables if var not in kwargs]
        if missing:
            raise KeyError(f"Missing variables: {', '.join(missing)}")
        
        # Format template
        return self.template.format(**kwargs)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert template to dictionary.
        
        Returns:
            Dictionary representation of the template
        """
        return {
            "template": self.template,
            "variables": self.variables,
            "description": self.description,
            "version": self.version,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PromptTemplate':
        """Create template from dictionary.
        
        Args:
            data: Dictionary containing template data
            
        Returns:
            PromptTemplate instance
        """
        return cls(
            template=data["template"],
            variables=data.get("variables"),
            description=data.get("description"),
            version=data.get("version", "1.0"),
            metadata=data.get("metadata", {})
        )
