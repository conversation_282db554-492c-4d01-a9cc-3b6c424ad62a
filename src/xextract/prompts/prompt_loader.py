"""提示词加载器，用于加载和处理提示词模板。

此模块提供了加载和处理提示词模板的功能，支持从文件加载模板，
并根据schema和其他参数填充模板。
"""

import json
import logging
import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

logger = logging.getLogger(__name__)


class PromptLoader:
    """提示词加载器，用于加载和处理提示词模板。"""

    def __init__(self, templates_dir: Optional[Union[str, Path]] = None):
        """初始化提示词加载器。

        Args:
            templates_dir: 提示词模板目录，默认为"models/prompts"
        """
        if templates_dir:
            self.templates_dir = Path(templates_dir)
        else:
            # 默认使用项目根目录下的models/prompts目录
            self.templates_dir = Path("models/prompts")

        if not self.templates_dir.exists():
            logger.warning(f"提示词模板目录不存在: {self.templates_dir}")

    def load_template(self, template_name: str) -> str:
        """加载提示词模板。

        Args:
            template_name: 模板名称，如"extraction/extract"

        Returns:
            模板内容

        Raises:
            FileNotFoundError: 如果模板文件不存在
        """
        # 构建模板文件路径
        if not template_name.endswith(".txt"):
            template_name = f"{template_name}.txt"

        template_path = self.templates_dir / template_name

        # 检查文件是否存在
        if not template_path.exists():
            raise FileNotFoundError(f"提示词模板文件不存在: {template_path}")

        # 加载模板内容
        with open(template_path, "r", encoding="utf-8") as f:
            template = f.read()

        return template

    def format_template(
        self, template: str, schema: Dict[str, Any], text: str, **kwargs
    ) -> str:
        """格式化提示词模板。

        Args:
            template: 提示词模板
            schema: 提取模式
            text: 待提取文本
            **kwargs: 其他参数

        Returns:
            格式化后的提示词
        """
        # 提取schema中的关键信息
        schema_info = self._extract_schema_info(schema)
        
        # 构建替换参数
        params = {
            "input_text": text,
            "data_schema": json.dumps(schema_info, ensure_ascii=False, indent=2),
            "supplement_knowledge": kwargs.get("supplement_knowledge", ""),
            "property_alias": self._format_property_alias(schema),
        }
        
        # 添加其他参数
        params.update(kwargs)
        
        # 替换模板中的占位符
        prompt = template
        for key, value in params.items():
            placeholder = f"{{{{%s}}}}" % key
            prompt = prompt.replace(placeholder, str(value))
            
        return prompt

    def _extract_schema_info(self, schema: Dict[str, Any]) -> Dict[str, Any]:
        """从schema中提取关键信息。

        Args:
            schema: 提取模式

        Returns:
            提取的关键信息
        """
        # 创建一个简化的schema，只包含必要的字段信息
        simplified_schema = {
            "name": schema.get("name", ""),
            "description": schema.get("description", ""),
            "fields": []
        }
        
        # 处理字段
        for field in schema.get("fields", []):
            field_info = {
                "name": field.get("name", ""),
                "type": field.get("type", "string"),
                "description": field.get("description", ""),
                "required": field.get("required", False),
            }
            
            # 添加提示信息
            if "prompt_hint" in field:
                field_info["prompt_hint"] = field["prompt_hint"]
                
            # 处理数组类型
            if field.get("array", False) or field.get("type") == "array":
                field_info["array"] = True
                
                # 处理数组元素的字段
                if "fields" in field:
                    field_info["fields"] = []
                    for subfield in field.get("fields", []):
                        subfield_info = {
                            "name": subfield.get("name", ""),
                            "type": subfield.get("type", "string"),
                            "description": subfield.get("description", ""),
                            "required": subfield.get("required", False),
                        }
                        
                        # 添加提示信息
                        if "prompt_hint" in subfield:
                            subfield_info["prompt_hint"] = subfield["prompt_hint"]
                            
                        field_info["fields"].append(subfield_info)
            
            simplified_schema["fields"].append(field_info)
            
        return simplified_schema

    def _format_property_alias(self, schema: Dict[str, Any]) -> str:
        """格式化属性别名。

        Args:
            schema: 提取模式

        Returns:
            格式化后的属性别名
        """
        alias_text = ""
        
        # 遍历字段，查找别名
        for field in schema.get("fields", []):
            field_name = field.get("name", "")
            field_desc = field.get("description", "")
            
            # 如果有别名，添加到别名文本中
            if "aliases" in field:
                aliases = field["aliases"]
                if isinstance(aliases, list) and aliases:
                    alias_text += f"{field_name}({field_desc}): {', '.join(aliases)}\n"
            
            # 处理数组类型的字段
            if "fields" in field:
                for subfield in field.get("fields", []):
                    subfield_name = subfield.get("name", "")
                    subfield_desc = subfield.get("description", "")
                    
                    # 如果有别名，添加到别名文本中
                    if "aliases" in subfield:
                        aliases = subfield["aliases"]
                        if isinstance(aliases, list) and aliases:
                            alias_text += f"{field_name}.{subfield_name}({subfield_desc}): {', '.join(aliases)}\n"
        
        return alias_text
