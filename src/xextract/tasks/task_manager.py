"""Task management for document processing.

This module provides a task management system for document processing,
allowing for decoupling of document parsing and extraction operations.
Uses thread pool for better readability and maintainability instead of asyncio.
"""

import logging
import uuid
import time
import threading
import concurrent.futures
from enum import Enum
from typing import Any, Dict, List, Optional, Callable, TypeVar, Generic
from datetime import datetime
from queue import Queue, Empty

logger = logging.getLogger(__name__)

# Type variable for task result
T = TypeVar("T")


class TaskStatus(str, Enum):
    """Status of a task."""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELED = "canceled"


class Task(Generic[T]):
    """A task for document processing.

    Attributes:
        id: Unique identifier for the task
        status: Current status of the task
        created_at: Time when the task was created
        started_at: Time when the task started running
        completed_at: Time when the task completed
        result: Result of the task (if completed)
        error: Error message (if failed)
        metadata: Additional metadata for the task
    """

    def __init__(
        self, task_id: str, metadata: Optional[Dict[str, Any]] = None
    ):
        """Initialize a task.

        Args:
            task_id: Unique identifier for the task (generated if not provided)
            metadata: Additional metadata for the task
        """
        self.id = task_id or str(uuid.uuid4())
        self.status = TaskStatus.PENDING
        self.created_at = time.time()
        self.result: Optional[T] = None
        self.error: Optional[str] = None
        self.metadata = metadata or {}
        self._future: Optional[concurrent.futures.Future] = None
        self._completed_event = threading.Event()

    def to_dict(self) -> Dict[str, Any]:
        """Convert task to dictionary.

        Returns:
            Dictionary representation of the task
        """
        return {
            "id": self.id,
            "status": self.status.value,
            "created_at": self.created_at,
            "started_at": self.started_at,
            "completed_at": self.completed_at,
            "error": self.error,
            "metadata": self.metadata,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Task":
        """Create task from dictionary.

        Args:
            data: Dictionary representation of the task

        Returns:
            Task instance
        """
        task = cls(task_id=data["id"], metadata=data.get("metadata", {}))
        task.status = TaskStatus(data["status"])
        task.created_at = data["created_at"]
        task.started_at = data.get("started_at")
        task.completed_at = data.get("completed_at")
        task.error = data.get("error")
        return task

    def __str__(self) -> str:
        """String representation of the task.

        Returns:
            String representation
        """
        return f"Task(id={self.id}, status={self.status.value})"


class TaskManager:
    """Manager for document processing tasks.

    This class provides methods for creating, running, and managing tasks
    for document processing operations.
    """

    def __init__(self, max_concurrent_tasks: int = 5):
        """Initialize the task manager.

        Args:
            max_concurrent_tasks: Maximum number of tasks to run concurrently
        """
        self.max_concurrent_tasks = max_concurrent_tasks
        self.tasks: Dict[str, Task] = {}
        self.task_queue = Queue(maxsize=max_concurrent_tasks * 2)
        self.executor = concurrent.futures.ThreadPoolExecutor(
            max_workers=max_concurrent_tasks
        )
        self._worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
        self._worker_thread.start()

    def create_task(self, metadata: Optional[Dict[str, Any]] = None) -> Task:
        """Create a new task.

        Args:
            metadata: Additional metadata for the task

        Returns:
            Created task
        """
        task = Task(metadata=metadata)
        self.tasks[task.id] = task
        return task

    def get_task(self, task_id: str) -> Optional[Task]:
        """Get a task by ID.

        Args:
            task_id: Task ID

        Returns:
            Task if found, None otherwise
        """
        return self.tasks.get(task_id)

    def get_tasks(
        self,
        status: Optional[TaskStatus] = None,
        limit: Optional[int] = None,
        offset: int = 0,
    ) -> List[Task]:
        """Get tasks, optionally filtered by status.

        Args:
            status: Filter by status
            limit: Maximum number of tasks to return
            offset: Offset for pagination

        Returns:
            List of tasks
        """
        tasks = list(self.tasks.values())

        # Filter by status
        if status:
            tasks = [task for task in tasks if task.status == status]

        # Sort by creation time (newest first)
        tasks.sort(key=lambda task: task.created_at, reverse=True)

        # Apply pagination
        if offset:
            tasks = tasks[offset:]
        if limit:
            tasks = tasks[:limit]

        return tasks

    def _worker_loop(self) -> None:
        """Worker loop for processing tasks from the queue."""
        while True:
            try:
                # Get task from queue
                task, func, args, kwargs = self.task_queue.get()

                # Process task
                self._process_task(task, func, args, kwargs)

                # Mark task as done
                self.task_queue.task_done()
            except Exception as e:
                logger.error(f"Error in worker loop: {e}")
                # Continue processing tasks
                continue

    def run_task(
        self, task_id: str, func: Callable[..., Any], *args: Any, **kwargs: Any
    ) -> Task[T]:
        """Run a task.

        Args:
            task_id: Task ID
            func: Function to run
            *args: Arguments for the function
            **kwargs: Keyword arguments for the function

        Returns:
            Task instance

        Raises:
            ValueError: If task not found or already running
        """
        task = self.get_task(task_id)
        if not task:
            raise ValueError(f"Task not found: {task_id}")

        if task.status == TaskStatus.RUNNING:
            raise ValueError(f"Task already running: {task_id}")

        # Create a future for the task
        task._future = self.executor.submit(lambda: None)  # Placeholder future

        # Add task to queue
        self.task_queue.put((task, func, args, kwargs))

        return task

    def _process_task(
        self, task: Task[T], func: Callable[..., Any], args: Any, kwargs: Any
    ) -> None:
        """Process a task.

        Args:
            task: Task to process
            func: Function to run
            args: Arguments for the function
            kwargs: Keyword arguments for the function
        """
        # Update task status
        task.status = TaskStatus.RUNNING
        task.started_at = time.time()

        # Create a real future for the task
        task._future = self.executor.submit(func, *args, **kwargs)

        # Add a callback to handle task completion
        def on_task_complete(future):
            try:
                # Get result
                result = future.result()

                # Update task with result
                task.result = result
                task.status = TaskStatus.COMPLETED
                task.completed_at = time.time()

                # Set event
                task._completed_event.set()

            except Exception as e:
                # Update task with error
                error_message = str(e)
                logger.error(f"Task {task.id} failed: {error_message}")
                task.error = error_message
                task.status = TaskStatus.FAILED
                task.completed_at = time.time()

                # Set event
                task._completed_event.set()

        # Add callback to future
        task._future.add_done_callback(on_task_complete)

    def wait_for_task(self, task_id: str, timeout: Optional[float] = None) -> Task:
        """Wait for a task to complete.

        Args:
            task_id: Task ID
            timeout: Timeout in seconds

        Returns:
            Completed task

        Raises:
            ValueError: If task not found
            TimeoutError: If timeout is reached
            Exception: If task failed
        """
        task = self.get_task(task_id)
        if not task:
            raise ValueError(f"Task not found: {task_id}")

        # If task is already completed or failed, return immediately
        if task.status in (
            TaskStatus.COMPLETED,
            TaskStatus.FAILED,
            TaskStatus.CANCELED,
        ):
            if task.status == TaskStatus.FAILED and task.error:
                raise Exception(f"Task failed: {task.error}")
            return task

        # Wait for future
        if not task._future:
            raise ValueError(f"Task not running: {task_id}")

        try:
            # Wait for the task to complete
            if not task._completed_event.wait(timeout=timeout):
                raise TimeoutError(f"Timeout waiting for task {task_id}")

            # Check if task failed
            if task.status == TaskStatus.FAILED and task.error:
                raise Exception(f"Task failed: {task.error}")

            return task
        except TimeoutError:
            raise
        except Exception as e:
            # Re-raise any other exceptions
            raise e

    def cancel_task(self, task_id: str) -> bool:
        """Cancel a task.

        Args:
            task_id: Task ID

        Returns:
            True if task was canceled, False otherwise
        """
        task = self.get_task(task_id)
        if not task:
            return False

        # Can only cancel pending or running tasks
        if task.status not in (TaskStatus.PENDING, TaskStatus.RUNNING):
            return False

        # Cancel future if it exists
        if task._future and not task._future.done():
            task._future.cancel()
            # Set the completed event to avoid waiting threads
            task._completed_event.set()

        # Update task status
        task.status = TaskStatus.CANCELED
        task.completed_at = time.time()

        return True

    def delete_task(self, task_id: str) -> bool:
        """Delete a task.

        Args:
            task_id: Task ID

        Returns:
            True if task was deleted, False otherwise
        """
        if task_id not in self.tasks:
            return False

        # Remove from memory
        del self.tasks[task_id]

        return True
