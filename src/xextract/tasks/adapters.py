"""Task adapters for document processing.

This module provides adapters for converting document processing operations
into tasks that can be managed by the task manager.
"""

import logging
import async<PERSON>
from typing import Any, Dict, List, Optional, Union, TypeVar, Generic
from pathlib import Path

from .task_manager import TaskManager, Task, TaskType
from ..parsers.base import BasePDFParser
from ..extractors.base import BaseExtractor
from ..schema import Schema

logger = logging.getLogger(__name__)

# Type variables
T = TypeVar('T')
P = TypeVar('P')
E = TypeVar('E')


class ParseTaskAdapter:
    """Adapter for document parsing tasks."""

    def __init__(self, task_manager: TaskManager, parser: BasePDFParser):
        """Initialize the parse task adapter.

        Args:
            task_manager: Task manager instance
            parser: PDF parser instance
        """
        self.task_manager = task_manager
        self.parser = parser

    async def parse_pdf(
        self,
        pdf_path: Union[str, Path],
        output_path: Optional[Union[str, Path]] = None,
        task_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Task:
        """Parse a PDF file as a task.

        Args:
            pdf_path: Path to the PDF file
            output_path: Path to save the parsed result (optional)
            task_id: Task ID (generated if not provided)
            metadata: Additional metadata for the task
            **kwargs: Additional options for the parser

        Returns:
            Task instance
        """
        # Prepare metadata
        if metadata is None:
            metadata = {}
        
        metadata.update({
            "pdf_path": str(pdf_path),
            "output_path": str(output_path) if output_path else None,
            "parser_type": self.parser.__class__.__name__
        })

        # Submit the task
        return await self.task_manager.submit(
            self._parse_pdf,
            pdf_path,
            output_path,
            task_id=task_id,
            task_type=TaskType.PARSE,
            metadata=metadata,
            **kwargs
        )

    async def _parse_pdf(
        self,
        pdf_path: Union[str, Path],
        output_path: Optional[Union[str, Path]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Internal method to parse a PDF file.

        Args:
            pdf_path: Path to the PDF file
            output_path: Path to save the parsed result (optional)
            **kwargs: Additional options for the parser

        Returns:
            Parsed document content
        """
        return await self.parser.parse_pdf(pdf_path, output_path, **kwargs)


class ExtractTaskAdapter:
    """Adapter for data extraction tasks."""

    def __init__(self, task_manager: TaskManager, extractor: BaseExtractor):
        """Initialize the extract task adapter.

        Args:
            task_manager: Task manager instance
            extractor: Extractor instance
        """
        self.task_manager = task_manager
        self.extractor = extractor

    async def extract(
        self,
        text: str,
        schema: Union[Dict[str, Any], Schema, str],
        task_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Task:
        """Extract data from text as a task.

        Args:
            text: Text to extract from
            schema: Schema defining the structure to extract (dict, Schema instance, or schema name)
            task_id: Task ID (generated if not provided)
            metadata: Additional metadata for the task
            **kwargs: Additional options for the extractor

        Returns:
            Task instance
        """
        # Prepare metadata
        if metadata is None:
            metadata = {}
        
        # Convert schema to dict if it's a Schema instance or a schema name
        schema_dict = None
        if isinstance(schema, dict):
            schema_dict = schema
            schema_name = schema.get("name", "unknown")
        elif isinstance(schema, Schema):
            schema_dict = schema.to_dict()
            schema_name = schema.name
        elif isinstance(schema, str):
            # Load schema by name
            schema_obj = Schema.get_by_name(schema)
            if not schema_obj:
                raise ValueError(f"Schema not found: {schema}")
            schema_dict = schema_obj.to_dict()
            schema_name = schema_obj.name
        else:
            raise TypeError("Schema must be a dict, Schema instance, or schema name")
        
        metadata.update({
            "schema_name": schema_name,
            "extractor_type": self.extractor.__class__.__name__,
            "text_length": len(text)
        })

        # Submit the task
        return await self.task_manager.submit(
            self._extract,
            text,
            schema_dict,
            task_id=task_id,
            task_type=TaskType.EXTRACT,
            metadata=metadata,
            **kwargs
        )

    async def _extract(
        self,
        text: str,
        schema: Dict[str, Any],
        **kwargs
    ) -> Dict[str, Any]:
        """Internal method to extract data from text.

        Args:
            text: Text to extract from
            schema: Schema defining the structure to extract
            **kwargs: Additional options for the extractor

        Returns:
            Extracted data
        """
        return await self.extractor.extract(text, schema, **kwargs)
