"""Base API client for external services."""
import logging
import time
import random
from typing import Any, Dict, Optional, Union, BinaryIO

import httpx

logger = logging.getLogger(__name__)


class BaseAPIClient:
    """Base class for API clients.

    This class provides common functionality for API clients, such as
    request handling, retries, and error handling.
    """

    def __init__(
        self,
        api_base_url: str,
        api_key: Optional[str] = None,
        timeout: int = 60,
        max_retries: int = 3,
        poll_interval: int = 1,
        **kwargs
    ):
        """Initialize the API client.

        Args:
            api_base_url: Base URL for the API
            api_key: API key for authentication
            timeout: Request timeout in seconds
            max_retries: Maximum number of retries for failed requests
            poll_interval: Interval in seconds between polling for async requests
            **kwargs: Additional keyword arguments
        """
        self.api_base_url = api_base_url
        self.api_key = api_key
        self.timeout = timeout
        self.max_retries = max_retries
        self.poll_interval = poll_interval

        # Initialize HTTP client
        headers = {}
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"

        self.client = httpx.Client(
            timeout=self.timeout,
            base_url=self.api_base_url,
            headers=headers
        )

    def close(self):
        """Close the HTTP client."""
        self.client.close()

    def __enter__(self):
        """Enter context manager."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit context manager."""
        self.close()

    def _request(
        self,
        method: str,
        url: str,
        *,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Any] = None,
        files: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        json: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> httpx.Response:
        """Send a request to the API with retry logic.

        Args:
            method: HTTP method (GET, POST, etc.)
            url: URL path (will be appended to base URL)
            params: Query parameters
            data: Request body data
            files: Files to upload
            headers: Additional headers
            json: JSON data to send
            **kwargs: Additional keyword arguments for httpx

        Returns:
            Response object

        Raises:
            ValueError: If the request fails after all retries
        """
        # Initialize retry counter
        retries = 0
        last_error = None

        # Retry loop
        while retries <= self.max_retries:
            try:
                # Send request
                response = self.client.request(
                    method,
                    url,
                    params=params,
                    data=data,
                    files=files,
                    headers=headers,
                    json=json,
                    **kwargs
                )

                # Check for rate limiting or server errors that should be retried
                if response.status_code in [429, 503, 504] and retries < self.max_retries:
                    retries += 1
                    wait_time = 2 ** retries + random.random()  # Exponential backoff with jitter
                    logger.warning(f"Request rate limited or server error. Retrying in {wait_time:.2f} seconds... (attempt {retries}/{self.max_retries})")
                    time.sleep(wait_time)
                    continue

                # Return successful response or raise for other status codes
                response.raise_for_status()
                return response

            except httpx.HTTPStatusError as e:
                last_error = e
                logger.error(f"HTTP error: {e.response.status_code} - {e.response.text}")

                # Check for task status in error response
                response_text = e.response.text.lower()
                if "running" in response_text or "waiting" in response_text:
                    # This is a task status error, not a real error
                    # We'll propagate it with the response text so _poll_task can handle it
                    raise ValueError(f"Task status: {e.response.text}")

                # Don't retry client errors except rate limiting
                if e.response.status_code < 500 and e.response.status_code != 429:
                    raise ValueError(f"API request failed: {e}")

                # Retry server errors
                if retries < self.max_retries:
                    retries += 1
                    wait_time = 2 ** retries + random.random()  # Exponential backoff with jitter
                    logger.warning(f"Request failed with server error. Retrying in {wait_time:.2f} seconds... (attempt {retries}/{self.max_retries})")
                    time.sleep(wait_time)
                else:
                    raise ValueError(f"API request failed after {self.max_retries} retries: {e}")

            except httpx.RequestError as e:
                last_error = e
                logger.error(f"Request error: {e}")

                # Retry network errors
                if retries < self.max_retries:
                    retries += 1
                    wait_time = 2 ** retries + random.random()  # Exponential backoff with jitter
                    logger.warning(f"Network error. Retrying in {wait_time:.2f} seconds... (attempt {retries}/{self.max_retries})")
                    time.sleep(wait_time)
                else:
                    raise ValueError(f"API request failed after {self.max_retries} retries: {e}")

        # If we get here, all retries failed
        raise ValueError(f"API request failed after {self.max_retries} retries: {last_error}")

    def _poll_task(
        self,
        url: str,
        task_id: str,
        params: Optional[Dict[str, Any]] = None
    ) -> httpx.Response:
        """Poll for the result of an asynchronous task.

        Args:
            url: URL to poll
            task_id: Task ID to poll for
            params: Additional query parameters

        Returns:
            Response object with the task result

        Raises:
            ValueError: If polling fails or the task fails
        """
        if params is None:
            params = {}

        params["task_id"] = task_id

        while True:
            try:
                response = self._request("GET", url, params=params)

                # If we get a 200 response, the task is complete
                return response

            except ValueError as e:
                # Check if this is a task status error
                if "running" in str(e).lower() or "waiting" in str(e).lower():
                    # Task is still running, wait and retry
                    time.sleep(self.poll_interval)
                    continue

                # Other errors should be propagated
                raise
