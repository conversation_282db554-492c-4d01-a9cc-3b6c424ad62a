"""Document parsing API client."""
import os
import io
import gzip
import json
import zipfile
import tarfile
import logging
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, BinaryIO

import httpx

from .base import BaseAPIClient

logger = logging.getLogger(__name__)


class DocumentAPIClient(BaseAPIClient):
    """Client for document parsing API.

    This client provides methods to interact with the document parsing API
    at http://api.memect.cn:6111/ or other compatible APIs.

    Supported endpoints:
    - /api/parse: General document parsing
    - /api/pdf2json: Extract text from PDF
    - /api/pdf2table: Extract tables from PDF
    - /api/pdf2doc: Extract document structure from PDF
    - /api/docx2pdf: Convert DOCX to PDF
    """

    def __init__(
        self,
        api_base_url: Optional[str] = "http://api.memect.cn:6111",
        api_key: Optional[str] = None,
        timeout: Optional[int] = 60,
        max_retries: Optional[int] = 3,
        poll_interval: Optional[int] = 1,
        **kwargs
    ):

        """Initialize the document API client.

        Args:
            api_base_url: Base URL for the API (defaults to settings)
            api_key: API key for authentication (defaults to settings)
            timeout: Request timeout in seconds (defaults to settings)
            max_retries: Maximum number of retries for failed requests (defaults to settings)
            poll_interval: Interval in seconds between polling for async requests (defaults to settings)
            **kwargs: Additional keyword arguments including:
                - max_file_size: Maximum file size in bytes
                - supported_formats: List of supported file formats
        """
        super().__init__(
            api_base_url=api_base_url,
            api_key=api_key,
            timeout=timeout,
            max_retries=max_retries,
            poll_interval=poll_interval,
            **kwargs
        )

        # Additional options
        self.supported_formats = kwargs.get("supported_formats", ["pdf", "docx", "pptx"])
        self.max_file_size = kwargs.get("max_file_size", 10 * 1024 * 1024)  # 10MB default

    def parse_document(
        self,
        file_path: Union[str, Path],
        *,
        endpoint: str = "parse",
        params: Optional[Dict[str, Any]] = None,
        output_path: Optional[Union[str, Path]] = None,
        output_format: str = "json",
        async_mode: Optional[bool] = None
    ) -> Union[Dict[str, Any], str, None]:
        """Parse a document using the API.

        Args:
            file_path: Path to the document file
            endpoint: API endpoint to use (e.g., "parse", "pdf2json", "pdf2table")
            params: Additional parameters for the API
            output_path: Path to save the output (required for non-JSON outputs)
            output_format: Output format ("json", "zip", "bz2")
            async_mode: Whether to use asynchronous mode

        Returns:
            Parsed document data if output_format is "json" and output_path is None,
            otherwise None (results are saved to output_path)

        Raises:
            FileNotFoundError: If the file doesn't exist
            ValueError: If the file is not a supported format or other API errors
        """
        # Default to synchronous mode if not specified
        if async_mode is None:
            async_mode = False

        # Validate file path
        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        # Validate file size
        file_size = file_path.stat().st_size
        if file_size > self.max_file_size:
            raise ValueError(f"File too large: {file_size} bytes (max: {self.max_file_size} bytes)")

        # Validate file format
        file_ext = file_path.suffix.lower().lstrip(".")
        if file_ext not in self.supported_formats:
            raise ValueError(f"Unsupported file format: {file_ext} (supported: {', '.join(self.supported_formats)})")

        # Prepare request parameters
        if params is None:
            params = {}

        params["output-format"] = output_format
        params["async"] = "true" if async_mode else "false"

        # Prepare headers and file data
        headers = {}
        with open(file_path, "rb") as f:
            file_data = f.read()

        # Compress JSON files
        if file_path.suffix.lower() == ".json":
            file_data = gzip.compress(file_data)
            headers["Content-Encoding"] = "gzip"

        # Send request
        try:
            if async_mode:
                # Send initial request to start the task
                response = self._request(
                    "POST",
                    f"/api/{endpoint}",
                    params=params,
                    data=file_data,
                    headers=headers
                )

                # Get task ID from response
                response_data = response.json()
                task_id = response_data.get("task", {}).get("id")

                if not task_id:
                    raise ValueError(f"Invalid response from API: {response_data}")

                # Poll for task completion
                response = self._poll_task(
                    f"/api/{endpoint}",
                    task_id,
                    params={"task_id": task_id}
                )
            else:
                # Send synchronous request
                response = self._request(
                    "POST",
                    f"/api/{endpoint}",
                    params=params,
                    data=file_data,
                    headers=headers
                )

            # Process response based on output format
            return self._process_response(response, output_format, output_path)

        except Exception as e:
            logger.error(f"Error parsing document: {e}")
            raise ValueError(f"Error parsing document: {e}")

    def _process_response(
        self,
        response: httpx.Response,
        output_format: str,
        output_path: Optional[Union[str, Path]]
    ) -> Union[Dict[str, Any], str, None]:
        """Process the API response.

        Args:
            response: API response
            output_format: Output format
            output_path: Path to save the output

        Returns:
            Processed response data or None if saved to file

        Raises:
            ValueError: If the output format is not supported
        """
        if output_format == "json":
            # If no output path is specified, return the JSON data
            if not output_path:
                return response.json()

            # Otherwise, save to file
            output_path = Path(output_path)
            os.makedirs(output_path.parent, exist_ok=True)

            with open(output_path, "wb") as f:
                f.write(response.content)

            return None

        elif output_format == "zip":
            if not output_path:
                raise ValueError("output_path is required for zip output format")

            output_path = Path(output_path)
            os.makedirs(output_path, exist_ok=True)

            with io.BytesIO(response.content) as f:
                with zipfile.ZipFile(f) as zf:
                    zf.extractall(output_path)

            return None

        elif output_format == "bz2":
            if not output_path:
                raise ValueError("output_path is required for bz2 output format")

            output_path = Path(output_path)
            os.makedirs(output_path, exist_ok=True)

            with io.BytesIO(response.content) as f:
                with tarfile.open(fileobj=f) as tf:
                    tf.extractall(output_path)

            return None

        else:
            raise ValueError(f"Unsupported output format: {output_format}")

    def pdf_to_text(
        self,
        file_path: Union[str, Path],
        *,
        params: Optional[Dict[str, Any]] = None,
        output_path: Optional[Union[str, Path]] = None,
        async_mode: Optional[bool] = None
    ) -> Union[Dict[str, Any], None]:
        """Extract text from a PDF document.

        Args:
            file_path: Path to the PDF file
            params: Additional parameters for the API
            output_path: Path to save the output
            async_mode: Whether to use asynchronous mode

        Returns:
            Extracted text data if output_path is None, otherwise None

        Raises:
            FileNotFoundError: If the file doesn't exist
            ValueError: If the file is not a PDF or other API errors
        """
        if params is None:
            params = {}

        # Set default parameters for text extraction
        params.update({
            "extract_tables": "false",  # We don't want tables for text extraction
            "extract_layout": "false",  # We don't want layout for text extraction
            "use_ocr": "true",  # Use OCR by default
            "language": "auto"  # Auto-detect language
        })

        return self.parse_document(
            file_path,
            endpoint="pdf2json",
            params=params,
            output_path=output_path,
            output_format="json",
            async_mode=async_mode
        )

    def pdf_to_tables(
        self,
        file_path: Union[str, Path],
        *,
        params: Optional[Dict[str, Any]] = None,
        output_path: Optional[Union[str, Path]] = None,
        output_format: str = "json",
        async_mode: Optional[bool] = None
    ) -> Union[Dict[str, Any], None]:
        """Extract tables from a PDF document.

        Args:
            file_path: Path to the PDF file
            params: Additional parameters for the API including:
                - extract-image: Whether to extract images (default: false)
                - ocr: Whether to use OCR (default: auto)
                - ocr-line: Whether to recognize lines with OCR (default: true)
                - format: Output format version (default: 4)
                - textlines: Whether to output textlines instead of spans (default: false)
                - table: Table extraction mode (default: all, can be ybk, wbk, or all)
                - layout: Layout mode (default: auto, can be default, column, or auto)
                - analyzer: Document type analyzer (default: auto)
                - text: Whether to output text (default: false)
                - excel: Whether to output Excel (default: false)
                - html: Whether to output HTML (default: false)
                - html-scale: Scale for HTML output (default: 1)
                - table-screenshot: Whether to take screenshots of tables (default: false)
                - output-files: Additional files to include in output (comma-separated)
            output_path: Path to save the output
            output_format: Output format ("json", "zip", "bz2")
            async_mode: Whether to use asynchronous mode

        Returns:
            Extracted table data if output_path is None and output_format is "json",
            otherwise None (results are saved to output_path)

        Raises:
            FileNotFoundError: If the file doesn't exist
            ValueError: If the file is not a PDF or other API errors
        """
        # Validate file path
        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        # Validate file format
        file_ext = file_path.suffix.lower().lstrip(".")
        if file_ext != "pdf":
            raise ValueError(f"Unsupported file format: {file_ext} (supported: pdf)")

        # Prepare default parameters
        default_params = {
            "extract-image": "false",
            "ocr": "auto",
            "ocr-line": "true",
            "format": "4",
            "textlines": "false",
            "table": "all",
            "layout": "auto",
            "analyzer": "auto",
            "text": "false",
            "excel": "false",
            "html": "false",
            "html-scale": "1",
            "table-screenshot": "false"
        }

        # Update with user-provided parameters
        if params:
            default_params.update(params)

        return self.parse_document(
            file_path,
            endpoint="pdf2table",
            params=default_params,
            output_path=output_path,
            output_format=output_format,
            async_mode=async_mode
        )

    def pdf_to_doc(
        self,
        file_path: Union[str, Path],
        *,
        params: Optional[Dict[str, Any]] = None,
        output_path: Optional[Union[str, Path]] = None,
        output_format: str = "json",
        async_mode: Optional[bool] = None
    ) -> Union[Dict[str, Any], None]:
        """Extract document structure from a PDF.

        Args:
            file_path: Path to the PDF file
            params: Additional parameters for the API including:
                - extract-image: Whether to extract images (default: false)
                - ocr: Whether to use OCR (default: false, can be true, false, or auto)
                - ocr-line: Whether to recognize lines with OCR (default: true)
                - format: Output format version (default: 1)
                - textlines: Whether to output textlines instead of spans (default: false)
                - table: Table extraction mode (default: ybk, can be ybk, wbk, or all)
                - layout: Layout mode (default: default, can be default, column, or auto)
                - analyzer: Document type analyzer (default: 1)
                - mode: Tree output mode (default: 3)
                - merge-table: Whether to merge tables across pages (default: false)
                - html: Whether to output HTML (default: false)
                - html-scale: Scale for HTML output (default: 1)
                - table-screenshot: Whether to take screenshots of tables (default: false)
                - output-files: Additional files to include in output (comma-separated)
            output_path: Path to save the output
            output_format: Output format ("json", "zip", "bz2")
            async_mode: Whether to use asynchronous mode

        Returns:
            Document structure data if output_path is None and output_format is "json",
            otherwise None (results are saved to output_path)

        Raises:
            FileNotFoundError: If the file doesn't exist
            ValueError: If the file is not a PDF or other API errors
        """
        # Validate file path
        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        # Validate file format
        file_ext = file_path.suffix.lower().lstrip(".")
        if file_ext != "pdf":
            raise ValueError(f"Unsupported file format: {file_ext} (supported: pdf)")

        # Prepare default parameters
        default_params = {
            "mode": "3",
            "extract-image": "false",
            "ocr": "auto",
            "ocr-line": "true",
            "format": "4",
            "textlines": "false",
            "table": "all",
            "layout": "auto",
            "analyzer": "auto",
            "merge-table": "false",
            "html": "false",
            "html-scale": "1",
            "table-screenshot": "false"
        }

        # Update with user-provided parameters
        if params:
            default_params.update(params)

        return self.parse_document(
            file_path,
            endpoint="pdf2doc",
            params=default_params,
            output_path=output_path,
            output_format=output_format,
            async_mode=async_mode
        )

    def docx_to_pdf(
        self,
        file_path: Union[str, Path],
        *,
        params: Optional[Dict[str, Any]] = None,
        output_path: Optional[Union[str, Path]] = None,
        output_format: str = "pdf",
        async_mode: Optional[bool] = None
    ) -> Union[bytes, None]:
        """Convert a DOCX document to PDF.

        Args:
            file_path: Path to the DOCX file
            params: Additional parameters for the API including:
                - table-border: 0 (no border), 1 (with border), 2 (maintain original)
            output_path: Path to save the output PDF
            output_format: Output format ("pdf", "zip", "bz2")
            async_mode: Whether to use asynchronous mode

        Returns:
            PDF content as bytes if output_path is None, otherwise None

        Raises:
            FileNotFoundError: If the file doesn't exist
            ValueError: If the file is not a DOCX or other API errors
        """
        # Validate file path
        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        # Validate file format
        file_ext = file_path.suffix.lower().lstrip(".")
        if file_ext not in ["docx", "doc"]:
            raise ValueError(f"Unsupported file format: {file_ext} (supported: docx, doc)")

        # Prepare request parameters
        if params is None:
            params = {}

        params["output-format"] = output_format
        params["async"] = "true" if async_mode else "false"

        # Send request
        try:
            with open(file_path, "rb") as f:
                file_data = f.read()

            if async_mode:
                # Send initial request to start the task
                response = self._request(
                    "POST",
                    "/api/docx2pdf",
                    params=params,
                    data=file_data
                )

                # Get task ID from response
                response_data = response.json()
                task_id = response_data.get("task", {}).get("id")

                if not task_id:
                    raise ValueError(f"Invalid response from API: {response_data}")

                # Poll for task completion
                response = self._poll_task(
                    "/api/docx2pdf",
                    task_id,
                    params={"task_id": task_id}
                )
            else:
                # Send synchronous request
                response = self._request(
                    "POST",
                    "/api/docx2pdf",
                    params=params,
                    data=file_data
                )

            # If output path is provided, save the PDF
            if output_path:
                output_path = Path(output_path)
                os.makedirs(output_path.parent, exist_ok=True)

                if output_format == "pdf":
                    with open(output_path, "wb") as f:
                        f.write(response.content)
                    return None
                elif output_format in ["zip", "bz2"]:
                    # Process archive formats
                    return self._process_response(response, output_format, output_path)
                else:
                    raise ValueError(f"Unsupported output format: {output_format}")

            # Return PDF content
            return response.content

        except Exception as e:
            logger.error(f"Error converting DOCX to PDF: {e}")
            raise ValueError(f"Error converting DOCX to PDF: {e}")
