"""API v1 package for XExtract."""
from fastapi import APIRouter

from .routes.schemas import router as schemas_router
from .routes.prompts import router as prompts_router
from .routes.extraction import router as extraction_router
from .routes.llm import router as llm_router

# Create API v1 router
api_v1_router = APIRouter(prefix="/api/v1")

# Include routers
api_v1_router.include_router(schemas_router, prefix="/schemas", tags=["schemas"])
api_v1_router.include_router(prompts_router, prefix="/prompts", tags=["prompts"])
api_v1_router.include_router(extraction_router, prefix="/extraction", tags=["extraction"])
api_v1_router.include_router(llm_router, prefix="/llm", tags=["llm"])
