"""LLM API routes for XExtract."""
import logging
from typing import List, Dict, Any, Optional

from fastapi import APIRouter, HTTPException, Depends

from config.config import config
from src.xextract.utils.logging_config import get_logger

# 创建路由器
router = APIRouter()

# 配置日志
logger = get_logger("xextract.api.llm")

@router.get("/models", response_model=List[Dict[str, Any]])
async def list_models():
    """获取可用的大模型列表。

    从配置文件中读取大模型列表，包括每个模型的详细配置信息。

    Returns:
        List[Dict[str, Any]]: 可用的大模型列表
    """
    try:
        logger.info("获取可用大模型列表")

        # 从配置文件中获取大模型列表
        models = config.llm_models

        # 移除敏感信息（如API密钥）
        for model in models:
            if "api_key" in model:
                model["api_key"] = "******" if model["api_key"] else ""

        logger.info(f"返回 {len(models)} 个大模型")
        return models
    except Exception as e:
        logger.error(f"获取大模型列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取大模型列表失败: {str(e)}")

@router.get("/models/{model_id}", response_model=Dict[str, Any])
async def get_model(model_id: str):
    """获取指定大模型的详细信息。

    Args:
        model_id: 大模型ID

    Returns:
        Dict[str, Any]: 大模型详细信息

    Raises:
        HTTPException: 如果找不到指定的大模型
    """
    try:
        logger.info(f"获取大模型详情: {model_id}")

        # 从配置文件中获取大模型列表
        models = config.llm_models

        # 查找指定的大模型
        for model in models:
            if model["id"] == model_id:
                # 移除敏感信息
                if "api_key" in model:
                    model["api_key"] = "******" if model["api_key"] else ""

                return model

        # 如果找不到指定的大模型，返回404错误
        logger.warning(f"找不到大模型: {model_id}")
        raise HTTPException(status_code=404, detail=f"找不到大模型: {model_id}")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取大模型详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取大模型详情失败: {str(e)}")
