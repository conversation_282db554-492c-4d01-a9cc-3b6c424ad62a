"""Schema API routes for XExtract."""
import os
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional

from fastapi import APIRouter, HTTPException, Query, Path as PathParam
from fastapi.responses import JSONResponse

from src.xextract.schema import <PERSON>hem<PERSON>, SchemaField, FieldType, ExtractionMethod
from src.server.api.v1.models import (
    SchemaResponse, SchemaListResponse, SchemaCreateRequest, SchemaUpdateRequest,
    SchemaFieldResponse
)

# Configure logging
logger = logging.getLogger("xextract.api.schemas")

# Create router
router = APIRouter()

# Define schema directory
SCHEMA_DIR = Path("models/schema")
if not SCHEMA_DIR.exists():
    SCHEMA_DIR.mkdir(parents=True)


def _schema_to_response(schema: Schema, schema_id: str) -> SchemaResponse:
    """Convert Schema to SchemaResponse."""
    # Get file stats for created/updated times
    schema_path = None

    # 首先检查直接在SCHEMA_DIR下的文件
    for ext in [".json", ".yaml", ".yml"]:
        file_path = SCHEMA_DIR / f"{schema_id}{ext}"
        if file_path.exists():
            schema_path = file_path
            break

    # 如果没找到，则在子目录中查找
    if not schema_path:
        for ext in [".json", ".yaml", ".yml"]:
            for project_dir in SCHEMA_DIR.iterdir():
                if not project_dir.is_dir():
                    continue

                file_path = project_dir / f"{schema_id}{ext}"
                if file_path.exists():
                    schema_path = file_path
                    break

            if schema_path:
                break

    created_at = None
    updated_at = None
    if schema_path:
        stats = schema_path.stat()
        created_at = datetime.fromtimestamp(stats.st_ctime)
        updated_at = datetime.fromtimestamp(stats.st_mtime)

    # Convert fields
    fields = []
    for field_name, field in schema.fields.items():
        fields.append(SchemaFieldResponse(
            name=field_name,
            type=field.type.value,
            description=field.description,
            required=field.required,
            array=field.array,
            extraction_method=field.extraction_method.value,
            regex_pattern=field.regex_pattern,
            regex_group=field.regex_group,
            prompt_hint=field.prompt_hint,
            format=field.format,
            pre_processors=field.pre_processors,
            post_processors=field.post_processors
        ))

    return SchemaResponse(
        id=schema_id,
        name=schema.name,
        description=schema.description,
        version=schema.version,
        fields=fields,
        metadata=schema.metadata,
        created_at=created_at,
        updated_at=updated_at
    )


@router.get("", response_model=SchemaListResponse)
async def list_schemas(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    search: Optional[str] = Query(None)
):
    """List all schemas."""
    schemas = []

    # 首先查找SCHEMA_DIR目录下的文件
    if SCHEMA_DIR.exists():
        # 直接查找SCHEMA_DIR下的文件
        for ext in [".json", ".yaml", ".yml"]:
            for schema_file in SCHEMA_DIR.glob(f"*{ext}"):
                try:
                    if ext == ".json":
                        schema = Schema.from_json_file(str(schema_file))
                    else:
                        schema = Schema.from_yaml_file(str(schema_file))

                    # Apply search filter if provided
                    if search:
                        search_lower = search.lower()
                        if (search_lower not in schema.name.lower() and
                            (not schema.description or search_lower not in schema.description.lower())):
                            continue

                    schema_id = schema_file.stem
                    schemas.append(_schema_to_response(schema, schema_id))
                except Exception as e:
                    logger.warning(f"Error loading schema from {schema_file}: {e}")

        # 然后查找子目录中的文件
        for project_dir in SCHEMA_DIR.iterdir():
            if not project_dir.is_dir():
                continue

            # Look for JSON and YAML files
            for ext in [".json", ".yaml", ".yml"]:
                for schema_file in project_dir.glob(f"*{ext}"):
                    try:
                        if ext == ".json":
                            schema = Schema.from_json_file(str(schema_file))
                        else:
                            schema = Schema.from_yaml_file(str(schema_file))

                        # Apply search filter if provided
                        if search:
                            search_lower = search.lower()
                            if (search_lower not in schema.name.lower() and
                                (not schema.description or search_lower not in schema.description.lower())):
                                continue

                        schema_id = schema_file.stem
                        schemas.append(_schema_to_response(schema, schema_id))
                    except Exception as e:
                        logger.warning(f"Error loading schema from {schema_file}: {e}")

    # Apply pagination
    total = len(schemas)
    schemas = schemas[skip:skip + limit]

    return SchemaListResponse(items=schemas, total=total)


@router.get("/{schema_id}", response_model=SchemaResponse)
async def get_schema(schema_id: str = PathParam(...)):
    """Get a schema by ID."""
    # 首先尝试在SCHEMA_DIR目录下查找
    schema_path = None

    # 首先检查直接在SCHEMA_DIR下的文件
    for ext in [".json", ".yaml", ".yml"]:
        file_path = SCHEMA_DIR / f"{schema_id}{ext}"
        if file_path.exists():
            schema_path = file_path
            break

    # 如果没找到，则在子目录中查找
    if not schema_path:
        for project_dir in SCHEMA_DIR.iterdir():
            if not project_dir.is_dir():
                continue

            for ext in [".json", ".yaml", ".yml"]:
                file_path = project_dir / f"{schema_id}{ext}"
                if file_path.exists():
                    schema_path = file_path
                    break

            if schema_path:
                break

    # 如果找到了文件，加载schema
    if schema_path:
        try:
            if schema_path.suffix.lower() == ".json":
                schema = Schema.from_json_file(str(schema_path))
            else:
                schema = Schema.from_yaml_file(str(schema_path))

            return _schema_to_response(schema, schema_id)
        except Exception as e:
            logger.error(f"Error loading schema from {schema_path}: {e}")
            raise HTTPException(status_code=500, detail=f"Error loading schema: {str(e)}")

    # 如果没找到文件，尝试使用Schema.get_by_name
    schema = Schema.get_by_name(schema_id)
    if not schema:
        raise HTTPException(status_code=404, detail=f"Schema not found: {schema_id}")

    return _schema_to_response(schema, schema_id)


@router.post("", response_model=SchemaResponse, status_code=201)
async def create_schema(schema_request: SchemaCreateRequest):
    """Create a new schema."""
    schema_id = schema_request.id

    # Check if schema already exists
    for ext in [".json", ".yaml", ".yml"]:
        for project_dir in SCHEMA_DIR.iterdir():
            if not project_dir.is_dir():
                continue

            file_path = project_dir / f"{schema_id}{ext}"
            if file_path.exists():
                raise HTTPException(status_code=409, detail=f"Schema already exists: {schema_id}")

    # Convert fields
    fields = {}
    for field_request in schema_request.fields:
        try:
            field_type = FieldType(field_request.type)
        except ValueError:
            field_type = FieldType.STRING

        try:
            extraction_method = ExtractionMethod(field_request.extraction_method)
        except ValueError:
            extraction_method = ExtractionMethod.LLM

        field = SchemaField(
            name=field_request.name,
            type=field_type,
            description=field_request.description,
            required=field_request.required,
            array=field_request.array,
            extraction_method=extraction_method,
            regex_pattern=field_request.regex_pattern,
            regex_group=field_request.regex_group,
            prompt_hint=field_request.prompt_hint,
            format=field_request.format,
            pre_processors=field_request.pre_processors,
            post_processors=field_request.post_processors
        )

        fields[field_request.name] = field

    # Create schema
    schema = Schema(
        name=schema_request.name,
        description=schema_request.description,
        version=schema_request.version,
        fields=fields,
        metadata=schema_request.metadata
    )

    # Determine schema category from metadata
    category = schema_request.metadata.get("category", "general")
    category_dir = SCHEMA_DIR / category
    if not category_dir.exists():
        category_dir.mkdir(parents=True)

    # Save schema
    schema_path = category_dir / f"{schema_id}.json"
    schema.save_json(str(schema_path))

    return _schema_to_response(schema, schema_id)


@router.put("/{schema_id}", response_model=SchemaResponse)
async def update_schema(
    schema_request: SchemaUpdateRequest,
    schema_id: str = PathParam(...)
):
    """Update a schema."""
    # Find the schema file
    schema_path = None
    for ext in [".json", ".yaml", ".yml"]:
        for project_dir in SCHEMA_DIR.iterdir():
            if not project_dir.is_dir():
                continue

            file_path = project_dir / f"{schema_id}{ext}"
            if file_path.exists():
                schema_path = file_path
                break

        if schema_path:
            break

    if not schema_path:
        raise HTTPException(status_code=404, detail=f"Schema not found: {schema_id}")

    # Load the schema
    if schema_path.suffix == ".json":
        schema = Schema.from_json_file(str(schema_path))
    else:
        schema = Schema.from_yaml_file(str(schema_path))

    # Update schema properties
    if schema_request.name is not None:
        schema.name = schema_request.name

    if schema_request.description is not None:
        schema.description = schema_request.description

    if schema_request.version is not None:
        schema.version = schema_request.version

    if schema_request.metadata is not None:
        schema.metadata = schema_request.metadata

    # Update fields if provided
    if schema_request.fields is not None:
        fields = {}
        for field_request in schema_request.fields:
            try:
                field_type = FieldType(field_request.type)
            except ValueError:
                field_type = FieldType.STRING

            try:
                extraction_method = ExtractionMethod(field_request.extraction_method)
            except ValueError:
                extraction_method = ExtractionMethod.LLM

            field = SchemaField(
                name=field_request.name,
                type=field_type,
                description=field_request.description,
                required=field_request.required,
                array=field_request.array,
                extraction_method=extraction_method,
                regex_pattern=field_request.regex_pattern,
                regex_group=field_request.regex_group,
                prompt_hint=field_request.prompt_hint,
                format=field_request.format,
                pre_processors=field_request.pre_processors,
                post_processors=field_request.post_processors
            )

            fields[field_request.name] = field

        schema.fields = fields

    # Save schema
    schema.save_json(str(schema_path))

    return _schema_to_response(schema, schema_id)


@router.delete("/{schema_id}")
async def delete_schema(schema_id: str = PathParam(...)):
    """Delete a schema."""
    # Find the schema file
    schema_path = None
    for ext in [".json", ".yaml", ".yml"]:
        for project_dir in SCHEMA_DIR.iterdir():
            if not project_dir.is_dir():
                continue

            file_path = project_dir / f"{schema_id}{ext}"
            if file_path.exists():
                schema_path = file_path
                break

        if schema_path:
            break

    if not schema_path:
        raise HTTPException(status_code=404, detail=f"Schema not found: {schema_id}")

    # Delete the schema file
    schema_path.unlink()

    return {"message": f"Schema deleted: {schema_id}"}
