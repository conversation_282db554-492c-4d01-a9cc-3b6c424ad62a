"""Extraction API routes for XExtract."""
import os
import json
import uuid
import logging
import tempfile
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional

from fastapi import APIRouter, HTTPException, Query, Path as PathParam, UploadFile, File, Form, BackgroundTasks
from fastapi.responses import JSONResponse

from src.xextract.engine.driver import FileExtractorDriver as ExtractEngine
from src.xextract.schema import Schema
from src.xextract.schema.field import FieldType
from src.server.api.v1.models import (
    ExtractionResponse, ExtractionListResponse, TextExtractionRequest, ExtractionMetadata
)

# Configure logging
from src.xextract.utils.logging_config import get_logger
logger = get_logger("xextract.api.extraction")

# Create router
router = APIRouter()

# Define extraction results directory
RESULTS_DIR = Path("data/extraction_results")
if not RESULTS_DIR.exists():
    RESULTS_DIR.mkdir(parents=True)

# 不在模块级别初始化提取引擎，而是在需要时创建
# 这样可以避免在导入时就需要有效的API密钥
extraction_engine = None

# 导入配置
from config.config import config


def _save_extraction_result(extraction_id: str, result: Dict[str, Any]) -> None:
    """Save extraction result to file."""
    logger.info(f"Saving extraction result to file: {extraction_id}")
    result_path = RESULTS_DIR / f"{extraction_id}.json"
    try:
        with open(result_path, "w") as f:
            json.dump(result, f, indent=2)
        logger.info(f"Successfully saved extraction result: {extraction_id} to {result_path}")
    except Exception as e:
        logger.error(f"Error saving extraction result {extraction_id}: {e}")
        raise


def _load_extraction_results() -> Dict[str, Dict[str, Any]]:
    """Load all extraction results from the results directory."""
    results = {}

    if RESULTS_DIR.exists():
        for result_file in RESULTS_DIR.glob("*.json"):
            try:
                with open(result_file, "r") as f:
                    result_data = json.load(f)
                    extraction_id = result_file.stem
                    results[extraction_id] = result_data
            except Exception as e:
                logger.warning(f"Error loading extraction result from {result_file}: {e}")

    return results


def _result_to_response(extraction_id: str, result_data: Dict[str, Any]) -> ExtractionResponse:
    """Convert extraction result data to ExtractionResponse."""
    # Extract metadata
    metadata = result_data.get("metadata", {})
    extraction_time = metadata.get("extraction_time")
    if extraction_time:
        try:
            extraction_time = datetime.fromisoformat(extraction_time)
        except (ValueError, TypeError):
            extraction_time = datetime.now()
    else:
        extraction_time = datetime.now()

    # Create metadata model
    metadata_model = ExtractionMetadata(
        confidence=metadata.get("confidence", 0.0),
        processing_time=metadata.get("processing_time", 0.0),
        source=metadata.get("source", "unknown"),
        file_type=metadata.get("file_type"),
        page_count=metadata.get("page_count"),
        file_size=metadata.get("file_size"),
        extraction_time=extraction_time
    )

    return ExtractionResponse(
        id=extraction_id,
        schema_id=result_data.get("schema_id", ""),
        schema_name=result_data.get("schema_name", ""),
        file_name=result_data.get("file_name"),
        extraction_time=extraction_time,
        status=result_data.get("status", "success"),
        data=result_data.get("data", {}),
        metadata=metadata_model
    )


@router.get("", response_model=ExtractionListResponse)
async def list_extractions(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    schema_id: Optional[str] = Query(None)
):
    """List all extraction results."""
    results_data = _load_extraction_results()
    results = []

    for extraction_id, result_data in results_data.items():
        # Apply schema filter if provided
        if schema_id and result_data.get("schema_id") != schema_id:
            continue

        results.append(_result_to_response(extraction_id, result_data))

    # Sort by extraction time (newest first)
    results.sort(key=lambda x: x.extraction_time, reverse=True)

    # Apply pagination
    total = len(results)
    results = results[skip:skip + limit]

    return ExtractionListResponse(items=results, total=total)


@router.get("/{extraction_id}", response_model=ExtractionResponse)
async def get_extraction(extraction_id: str = PathParam(...)):
    """Get an extraction result by ID."""
    results_data = _load_extraction_results()

    if extraction_id not in results_data:
        raise HTTPException(status_code=404, detail=f"Extraction result not found: {extraction_id}")

    return _result_to_response(extraction_id, results_data[extraction_id])


@router.post("/file", response_model=ExtractionResponse, status_code=201)
async def extract_from_file(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    schema_id: str = Form(...),
    prompt_id: Optional[str] = Form(None)
):
    """Extract data from a file."""
    # 记录请求信息
    logger.info(f"Received file extraction request: schema_id={schema_id}, file={file.filename}, prompt_id={prompt_id}")

    # 记录开始时间
    start_time = datetime.now()

    # Check if schema exists
    schema = Schema.get_by_name(schema_id)
    if not schema:
        logger.error(f"Schema not found: {schema_id}")
        raise HTTPException(status_code=404, detail=f"Schema not found: {schema_id}")

    logger.info(f"Using schema: {schema_id} ({schema.name})")

    # Generate extraction ID
    extraction_id = str(uuid.uuid4())
    logger.info(f"Generated extraction ID: {extraction_id}")

    # Save file to temporary location
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.filename).suffix)
    try:
        # Write file content
        content = await file.read()
        temp_file.write(content)
        temp_file.close()

        logger.info(f"Saved file to temporary location: {temp_file.name}, size: {len(content)} bytes")

        # Extract data
        extraction_time = datetime.now().isoformat()

        # 使用已导入的ExtractEngine

        # 准备 LLM 参数 (从配置文件读取)
        llm_config = config.llm
        llm_params = {
            "llm_provider": llm_config["provider"],
            "llm_model": llm_config["id"],
            "llm_api_key": llm_config["api_key"],
            "llm_api_base_url": llm_config["api_base_url"],
            "llm_temperature": llm_config["temperature"],
            "llm_max_tokens": llm_config["max_tokens"]
        }
        logger.info(f"Initializing extraction engine with params: {llm_params}")

        # 创建提取引擎实例
        extraction_engine = ExtractEngine(**llm_params)
        logger.info("Extraction engine initialized")

        # 使用线程池执行提取任务
        try:
            # 导入超时处理模块
            import concurrent.futures

            # 创建一个线程池
            with concurrent.futures.ThreadPoolExecutor() as executor:
                # 提交任务到线程池
                logger.info(f"Submitting extraction task to thread pool: file_path={temp_file.name}, schema={schema_id}")
                future = executor.submit(
                    extraction_engine.extract_from_file_sync,
                    file_path=temp_file.name,
                    schema=schema,  # 传递schema对象，而不是schema_name
                    **llm_params
                )
                logger.info("Extraction task submitted to thread pool")

                # 初始化 result 变量
                result = None

                try:
                    # 从配置文件中读取超时时间，默认为120秒
                    extraction_timeout = config.get("EXTRACTION_TIMEOUT", 120)
                    logger.info(f"等待提取结果，超时时间: {extraction_timeout}秒")

                    # 等待任务完成
                    result = future.result(timeout=extraction_timeout)
                    logger.info(f"Extracted result using ExtractEngine: {result}")

                    # 检查是否有取消标记
                    cancel_path = RESULTS_DIR / f"{extraction_id}.cancel"
                    if cancel_path.exists():
                        logger.info(f"Extraction task {extraction_id} was cancelled")
                        raise HTTPException(status_code=499, detail="Task cancelled by user")
                except concurrent.futures.TimeoutError:
                    # 如果超时，记录警告
                    logger.warning(f"提取超时，超过了 {extraction_timeout} 秒")
                    raise HTTPException(status_code=500, detail=f"提取超时，超过了 {extraction_timeout} 秒")
        except Exception as extract_error:
            # 如果提取失败，记录错误
            logger.error(f"Error using ExtractEngine: {str(extract_error)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"Error in extraction: {str(extract_error)}")

        if not result:
            raise HTTPException(status_code=500, detail="Failed to extract data from file")

        # 计算处理时间
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        logger.info(f"Extraction completed in {processing_time:.2f} seconds")

        # 构建结果
        formatted_result = {
            "extraction_id": extraction_id,
            "schema_id": schema_id,
            "schema_name": schema.name,
            "file_name": file.filename,
            "status": "success",
            "data": result,
            "metadata": {
                "extraction_time": extraction_time,
                "source": file.filename,
                "file_size": len(content),
                "confidence": 0.9,  # Placeholder
                "processing_time": processing_time
            }
        }

        logger.info(f"Formatted extraction result with {len(result) if isinstance(result, dict) else 'unknown'} fields")

        # Save result
        logger.info(f"Adding task to save extraction result: {extraction_id}")
        background_tasks.add_task(_save_extraction_result, extraction_id, formatted_result)

        # 构建响应
        response = _result_to_response(extraction_id, formatted_result)
        logger.info(f"Returning extraction response for ID: {extraction_id}")
        return response
    except Exception as e:
        logger.error(f"Error extracting data from file: {e}")
        raise HTTPException(status_code=500, detail=f"Error extracting data: {str(e)}")
    finally:
        # Clean up temporary file
        try:
            os.unlink(temp_file.name)
        except:
            pass


@router.post("/text", response_model=ExtractionResponse, status_code=201)
async def extract_from_text(
    background_tasks: BackgroundTasks,
    request: TextExtractionRequest
):
    """Extract data from text."""
    # 记录请求信息
    logger.info(f"Received text extraction request: schema_id={request.schema_id}, text_length={len(request.text)}")

    # 记录开始时间
    start_time = datetime.now()

    # Check if schema exists
    schema = Schema.get_by_name(request.schema_id)
    if not schema:
        logger.error(f"Schema not found: {request.schema_id}")
        raise HTTPException(status_code=404, detail=f"Schema not found: {request.schema_id}")

    logger.info(f"Using schema: {request.schema_id} ({schema.name})")

    # Generate extraction ID
    extraction_id = str(uuid.uuid4())
    logger.info(f"Generated extraction ID: {extraction_id}")

    try:
        # Extract data
        extraction_time = datetime.now().isoformat()

        # 准备LLM参数
        llm_params = {}
        if request.llm:
            llm_params = {
                "llm_model": request.llm.model,
                "llm_options": {
                    "temperature": request.llm.temperature,
                    "max_tokens": request.llm.max_tokens
                }
            }

        # 使用 ExtractEngine 进行提取
        try:
            # 获取 schema
            schema = Schema.get_by_name(request.schema_id)
            if not schema:
                raise HTTPException(status_code=404, detail=f"Schema not found: {request.schema_id}")

            # 使用已导入的ExtractEngine

            # 准备 LLM 参数 (从配置文件读取，如果请求中有参数则覆盖)
            llm_config = config.llm
            llm_params = {
                "llm_provider": llm_config["provider"],
                "llm_model": llm_config["id"],
                "llm_api_key": llm_config["api_key"],
                "llm_api_base_url": llm_config["api_base_url"],
                "llm_temperature": llm_config["temperature"],
                "llm_max_tokens": llm_config["max_tokens"]
            }

            # 如果请求中有LLM参数，则覆盖配置文件中的参数
            if request.llm:
                if request.llm.model:
                    llm_params["llm_model"] = request.llm.model
                if request.llm.temperature is not None:
                    llm_params["llm_temperature"] = request.llm.temperature
                if request.llm.max_tokens is not None:
                    llm_params["llm_max_tokens"] = request.llm.max_tokens

            # 创建提取引擎实例
            extraction_engine = ExtractEngine(**llm_params)

            # 使用提取引擎进行提取
            try:
                # 导入超时处理模块
                import concurrent.futures

                # 创建一个线程池
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    # 提交任务到线程池
                    future = executor.submit(
                        extraction_engine.extract_from_text_sync,
                        text=request.text,
                        schema=schema,  # 传递schema对象，而不是schema_name
                        **llm_params
                    )

                    # 初始化 result 变量
                    result = None

                    try:
                        # 从配置文件中读取超时时间，默认为120秒
                        extraction_timeout = config.get("EXTRACTION_TIMEOUT", 120)
                        logger.info(f"等待提取结果，超时时间: {extraction_timeout}秒")

                        # 等待任务完成
                        result = future.result(timeout=extraction_timeout)
                        logger.info(f"Extracted result using ExtractEngine: {result}")

                        # 检查是否有取消标记
                        cancel_path = RESULTS_DIR / f"{extraction_id}.cancel"
                        if cancel_path.exists():
                            logger.info(f"Extraction task {extraction_id} was cancelled")
                            raise HTTPException(status_code=499, detail="Task cancelled by user")
                    except concurrent.futures.TimeoutError:
                        # 如果超时，记录警告并继续使用备用方法
                        logger.warning(f"提取超时，超过了 {extraction_timeout} 秒，使用备用方法")
                        # 不抛出异常，让代码继续执行备用方法
                        result = None
            except Exception as extract_error:
                # 如果提取失败，记录错误并使用备用方法
                logger.error(f"Error using ExtractEngine: {str(extract_error)}", exc_info=True)
                result = None

            # 如果 result 为 None，使用备用方法
            if result is None:
                # 备用方法：使用 schema 中定义的正则表达式进行提取
                result = {}

                # 导入正则表达式模块
                import re

                # 处理所有字段
                for field_name, field in schema.fields.items():
                    # 初始化字段值为 None
                    field_value = None

                    # 如果字段有正则表达式模式，尝试使用正则表达式提取
                    if field.regex_pattern:
                        try:
                            matches = re.finditer(field.regex_pattern, request.text)
                            for match in matches:
                                try:
                                    # 提取匹配组
                                    value = match.group(field.regex_group)

                                    # 清理提取的值，去除字段名称等前缀
                                    cleaned_value = value
                                    # 如果值包含冒号，可能是包含了字段名称，尝试提取冒号后的部分
                                    if "：" in value or ":" in value:
                                        parts = re.split(r'[：:]', value, 1)
                                        if len(parts) > 1:
                                            cleaned_value = parts[1].strip()

                                    # 根据字段类型进行转换
                                    if field.type == FieldType.INTEGER:
                                        try:
                                            field_value = int(cleaned_value)
                                        except ValueError:
                                            field_value = None
                                    elif field.type == FieldType.FLOAT:
                                        try:
                                            field_value = float(cleaned_value)
                                        except ValueError:
                                            field_value = None
                                    elif field.type == FieldType.BOOLEAN:
                                        field_value = cleaned_value.lower() in ["true", "yes", "1", "y"]
                                    else:
                                        field_value = cleaned_value

                                    break  # 找到第一个匹配就退出
                                except Exception as e:
                                    logger.debug(f"Error extracting field {field_name} with regex: {e}")
                        except Exception as regex_error:
                            logger.warning(f"Regex extraction failed for field {field_name}: {str(regex_error)}")

                    # 处理对象类型字段
                    if field.type == FieldType.OBJECT and field.fields:
                        # 初始化对象
                        obj_value = {}

                        # 处理嵌套字段
                        for nested_field in field.fields:
                            # 初始化嵌套字段值为 None
                            nested_value = None

                            # 如果嵌套字段有正则表达式模式，尝试使用正则表达式提取
                            if nested_field.regex_pattern:
                                try:
                                    matches = re.finditer(nested_field.regex_pattern, request.text)
                                    for match in matches:
                                        try:
                                            # 提取匹配组
                                            value = match.group(nested_field.regex_group)

                                            # 清理提取的值，去除字段名称等前缀
                                            cleaned_value = value
                                            # 如果值包含冒号，可能是包含了字段名称，尝试提取冒号后的部分
                                            if "：" in value or ":" in value:
                                                parts = re.split(r'[：:]', value, 1)
                                                if len(parts) > 1:
                                                    cleaned_value = parts[1].strip()

                                            # 根据字段类型进行转换
                                            if nested_field.type == FieldType.INTEGER:
                                                try:
                                                    nested_value = int(cleaned_value)
                                                except ValueError:
                                                    nested_value = None
                                            elif nested_field.type == FieldType.FLOAT:
                                                try:
                                                    nested_value = float(cleaned_value)
                                                except ValueError:
                                                    nested_value = None
                                            elif nested_field.type == FieldType.BOOLEAN:
                                                nested_value = cleaned_value.lower() in ["true", "yes", "1", "y"]
                                            else:
                                                nested_value = cleaned_value

                                            break  # 找到第一个匹配就退出
                                        except Exception as e:
                                            logger.debug(f"Error extracting nested field {nested_field.name} with regex: {e}")
                                except Exception as regex_error:
                                    logger.warning(f"Regex extraction failed for nested field {nested_field.name}: {str(regex_error)}")

                            # 如果嵌套字段有默认值且提取失败，使用默认值
                            if nested_value is None and nested_field.default is not None:
                                nested_value = nested_field.default

                            # 将嵌套字段值添加到对象中
                            obj_value[nested_field.name] = nested_value

                        # 设置对象字段值
                        field_value = obj_value

                    # 处理数组类型字段
                    elif field.array and field.fields:
                        # 数组类型字段的处理比较复杂，这里简化处理为空数组
                        field_value = []

                    # 如果字段有默认值且提取失败，使用默认值
                    if field_value is None and field.default is not None:
                        field_value = field.default

                    # 将字段值添加到结果中
                    result[field_name] = field_value

                logger.info(f"Extracted result using backup method: {result}")
        except Exception as e:
            logger.error(f"Error in extraction: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"Error in extraction: {str(e)}")

        # 计算处理时间
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        logger.info(f"Text extraction completed in {processing_time:.2f} seconds")

        # Format result
        formatted_result = {
            "extraction_id": extraction_id,
            "schema_id": request.schema_id,
            "schema_name": schema.name,
            "status": "success",
            "data": result,
            "metadata": {
                "extraction_time": extraction_time,
                "source": "text_input",
                "text_length": len(request.text),
                "confidence": 0.9,  # Placeholder
                "processing_time": processing_time
            }
        }

        logger.info(f"Formatted text extraction result with {len(result) if isinstance(result, dict) else 'unknown'} fields")

        # Save result
        logger.info(f"Adding task to save text extraction result: {extraction_id}")
        background_tasks.add_task(_save_extraction_result, extraction_id, formatted_result)

        # 构建响应
        response = _result_to_response(extraction_id, formatted_result)
        logger.info(f"Returning text extraction response for ID: {extraction_id}")
        return response
    except Exception as e:
        logger.error(f"Error extracting data from text: {e}")
        raise HTTPException(status_code=500, detail=f"Error extracting data: {str(e)}")


@router.delete("/{extraction_id}")
async def delete_extraction(extraction_id: str = PathParam(...)):
    """Delete an extraction result."""
    # Check if extraction result exists
    result_path = RESULTS_DIR / f"{extraction_id}.json"
    if not result_path.exists():
        raise HTTPException(status_code=404, detail=f"Extraction result not found: {extraction_id}")

    # Delete the result file
    result_path.unlink()

    return {"message": f"Extraction result deleted: {extraction_id}"}


@router.get("/status/{task_id}")
async def get_extraction_status(task_id: str = PathParam(...)):
    """Get the status of an extraction task."""
    # Check if extraction result exists
    results_data = _load_extraction_results()

    # If the extraction result exists, return it
    if task_id in results_data:
        result_data = results_data[task_id]
        response = _result_to_response(task_id, result_data)
        return {
            "status": "success",
            "task_id": task_id,
            "result": response
        }

    # Check if the task was cancelled
    cancel_path = RESULTS_DIR / f"{task_id}.cancel"
    if cancel_path.exists():
        return {
            "status": "cancelled",
            "task_id": task_id,
            "message": "Task cancelled by user"
        }

    # If the task ID is valid but not completed or cancelled, it's still processing
    # In a real implementation, we would check if the task exists in a task queue
    # For now, we'll just assume it's processing if the ID format is valid
    try:
        uuid.UUID(task_id)
        return {
            "status": "processing",
            "task_id": task_id,
            "progress": 0.5  # Placeholder
        }
    except ValueError:
        # If the task ID is not a valid UUID, it's not a valid task
        raise HTTPException(status_code=404, detail=f"Extraction task not found: {task_id}")


@router.post("/cancel/{task_id}")
async def cancel_extraction_task(task_id: str = PathParam(...)):
    """Cancel an ongoing extraction task."""
    logger.info(f"Attempting to cancel extraction task: {task_id}")

    # 创建取消标记文件
    cancel_path = RESULTS_DIR / f"{task_id}.cancel"

    try:
        # 创建一个空文件作为取消标记
        with open(cancel_path, "w") as f:
            f.write(f"Cancelled at {datetime.now().isoformat()}")

        # 创建一个结果文件，表示任务已取消
        result_path = RESULTS_DIR / f"{task_id}.json"
        if not result_path.exists():
            cancelled_result = {
                "extraction_id": task_id,
                "status": "cancelled",
                "message": "Task cancelled by user",
                "metadata": {
                    "cancelled_at": datetime.now().isoformat()
                }
            }
            with open(result_path, "w") as f:
                json.dump(cancelled_result, f, indent=2)

        logger.info(f"Successfully cancelled extraction task: {task_id}")
        return {"message": f"Extraction task cancelled: {task_id}", "status": "cancelled"}
    except Exception as e:
        logger.error(f"Error cancelling extraction task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Error cancelling task: {str(e)}")
