"""Prompt API routes for XExtract."""
import os
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional

from fastapi import APIRouter, HTTPException, Query, Path as PathParam
from fastapi.responses import JSONResponse

from src.xextract.schema import Schema
from src.server.api.v1.models import (
    PromptResponse, PromptListResponse, PromptCreateRequest, PromptUpdateRequest
)

# Configure logging
logger = logging.getLogger("xextract.api.prompts")

# Create router
router = APIRouter()

# Define prompts directory
PROMPTS_DIR = Path("models/prompts")
if not PROMPTS_DIR.exists():
    PROMPTS_DIR.mkdir(parents=True)


def _load_prompts() -> Dict[str, Dict[str, Any]]:
    """Load all prompts from the prompts directory."""
    prompts = {}

    logger.info(f"加载提示词目录: {PROMPTS_DIR}")
    if not PROMPTS_DIR.exists():
        logger.warning(f"提示词目录不存在: {PROMPTS_DIR}")
        return prompts

    # 递归查找所有子目录中的txt文件
    logger.info("查找TXT格式提示词文件")
    txt_files = list(PROMPTS_DIR.glob("**/*.txt"))
    logger.info(f"找到 {len(txt_files)} 个TXT提示词文件")

    for prompt_file in txt_files:
        try:
            logger.debug(f"加载TXT提示词文件: {prompt_file}")
            with open(prompt_file, "r", encoding="utf-8") as f:
                content = f.read()

            # 构建相对路径作为ID
            rel_path = prompt_file.relative_to(PROMPTS_DIR)
            prompt_id = str(rel_path.with_suffix(''))
            logger.debug(f"提示词ID: {prompt_id}")

            # 构建提示词数据
            prompt_data = {
                "name": prompt_id,
                "description": f"提示词模板: {prompt_id}",
                "content": content,
                "schema_id": ""  # 默认为空，可以在前端选择
            }

            prompts[prompt_id] = prompt_data
            logger.info(f"成功加载TXT提示词: {prompt_id}")
        except Exception as e:
            logger.warning(f"加载提示词失败 {prompt_file}: {e}")

    # 同时支持json格式的提示词
    logger.info("查找JSON格式提示词文件")
    json_files = list(PROMPTS_DIR.glob("**/*.json"))
    logger.info(f"找到 {len(json_files)} 个JSON提示词文件")

    for prompt_file in json_files:
        try:
            logger.debug(f"加载JSON提示词文件: {prompt_file}")
            with open(prompt_file, "r", encoding="utf-8") as f:
                prompt_data = json.load(f)

            # 构建相对路径作为ID
            rel_path = prompt_file.relative_to(PROMPTS_DIR)
            prompt_id = str(rel_path.with_suffix(''))
            logger.debug(f"提示词ID: {prompt_id}")

            prompts[prompt_id] = prompt_data
            logger.info(f"成功加载JSON提示词: {prompt_id}")
        except Exception as e:
            logger.warning(f"加载提示词失败 {prompt_file}: {e}")

    logger.info(f"总共加载了 {len(prompts)} 个提示词: {list(prompts.keys())}")
    return prompts


def _prompt_to_response(prompt_id: str, prompt_data: Dict[str, Any]) -> PromptResponse:
    """Convert prompt data to PromptResponse."""
    # Get file stats for created/updated times
    created_at = None
    updated_at = None

    # 尝试查找对应的文件
    # 首先尝试.txt文件
    prompt_path = PROMPTS_DIR / f"{prompt_id}.txt"
    if not prompt_path.exists():
        # 如果不存在，尝试在子目录中查找
        for txt_file in PROMPTS_DIR.glob(f"**/{prompt_id}.txt"):
            prompt_path = txt_file
            break

    # 如果没找到.txt文件，尝试.json文件
    if not prompt_path.exists():
        prompt_path = PROMPTS_DIR / f"{prompt_id}.json"
        if not prompt_path.exists():
            # 如果不存在，尝试在子目录中查找
            for json_file in PROMPTS_DIR.glob(f"**/{prompt_id}.json"):
                prompt_path = json_file
                break

    if prompt_path.exists():
        stats = prompt_path.stat()
        created_at = datetime.fromtimestamp(stats.st_ctime)
        updated_at = datetime.fromtimestamp(stats.st_mtime)

    return PromptResponse(
        id=prompt_id,
        name=prompt_data.get("name", ""),
        description=prompt_data.get("description"),
        content=prompt_data.get("content", ""),
        schema_id=prompt_data.get("schema_id", ""),
        created_at=created_at,
        updated_at=updated_at
    )


@router.get("", response_model=PromptListResponse)
async def list_prompts(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    search: Optional[str] = Query(None),
    schema_id: Optional[str] = Query(None)
):
    """List all prompts."""
    prompts_data = _load_prompts()
    prompts = []

    for prompt_id, prompt_data in prompts_data.items():
        # Apply schema filter if provided
        if schema_id and prompt_data.get("schema_id") != schema_id:
            continue

        # Apply search filter if provided
        if search:
            search_lower = search.lower()
            name = prompt_data.get("name", "")
            description = prompt_data.get("description", "")
            if (search_lower not in name.lower() and
                (not description or search_lower not in description.lower())):
                continue

        prompts.append(_prompt_to_response(prompt_id, prompt_data))

    # Apply pagination
    total = len(prompts)
    prompts = prompts[skip:skip + limit]

    return PromptListResponse(items=prompts, total=total)


@router.get("/{prompt_id:path}", response_model=PromptResponse)
async def get_prompt(prompt_id: str = PathParam(...)):
    """Get a prompt by ID."""
    logger.info(f"获取提示词详情: {prompt_id}")

    # 首先尝试从已加载的提示词中查找
    prompts_data = _load_prompts()
    logger.debug(f"已加载的提示词: {list(prompts_data.keys())}")

    if prompt_id in prompts_data:
        logger.info(f"从已加载的提示词中找到: {prompt_id}")
        return _prompt_to_response(prompt_id, prompts_data[prompt_id])

    # 如果在已加载的提示词中没有找到，尝试查找文件
    prompt_path = None

    # 首先尝试.json文件
    json_path = PROMPTS_DIR / f"{prompt_id}.json"
    logger.debug(f"检查JSON路径: {json_path}")
    if json_path.exists():
        logger.info(f"找到JSON文件: {json_path}")
        prompt_path = json_path

    # 如果没找到，尝试在子目录中查找.json文件
    if not prompt_path:
        logger.debug(f"在子目录中查找JSON文件: **/{prompt_id}.json")
        for file_path in PROMPTS_DIR.glob(f"**/{prompt_id}.json"):
            logger.info(f"在子目录中找到JSON文件: {file_path}")
            prompt_path = file_path
            break

    # 如果没找到.json文件，尝试.txt文件
    if not prompt_path:
        txt_path = PROMPTS_DIR / f"{prompt_id}.txt"
        logger.debug(f"检查TXT路径: {txt_path}")
        if txt_path.exists():
            logger.info(f"找到TXT文件: {txt_path}")
            prompt_path = txt_path

    # 如果没找到，尝试在子目录中查找.txt文件
    if not prompt_path:
        logger.debug(f"在子目录中查找TXT文件: **/{prompt_id}.txt")
        for file_path in PROMPTS_DIR.glob(f"**/{prompt_id}.txt"):
            logger.info(f"在子目录中找到TXT文件: {file_path}")
            prompt_path = file_path
            break

    if not prompt_path:
        logger.error(f"提示词未找到: {prompt_id}")
        raise HTTPException(status_code=404, detail=f"Prompt not found: {prompt_id}")

    # 如果找到了文件，加载提示词
    if prompt_path.suffix == ".txt":
        with open(prompt_path, "r", encoding="utf-8") as f:
            content = f.read()

        # 创建提示词数据
        prompt_data = {
            "name": prompt_id,
            "description": f"提示词模板: {prompt_id}",
            "content": content,
            "schema_id": ""
        }
    else:
        # 加载JSON数据
        with open(prompt_path, "r", encoding="utf-8") as f:
            prompt_data = json.load(f)

    return _prompt_to_response(prompt_id, prompt_data)


@router.post("", response_model=PromptResponse, status_code=201)
async def create_prompt(prompt_request: PromptCreateRequest):
    """Create a new prompt."""
    prompt_id = prompt_request.id

    # Check if prompt already exists
    json_path = PROMPTS_DIR / f"{prompt_id}.json"
    txt_path = PROMPTS_DIR / f"{prompt_id}.txt"

    # 检查是否已存在同名的JSON或TXT文件
    if json_path.exists() or txt_path.exists():
        raise HTTPException(status_code=409, detail=f"Prompt already exists: {prompt_id}")

    # 检查子目录中是否存在同名文件
    existing_files = list(PROMPTS_DIR.glob(f"**/{prompt_id}.*"))
    if existing_files:
        raise HTTPException(status_code=409, detail=f"Prompt already exists: {prompt_id}")

    # Check if schema exists
    schema = Schema.get_by_name(prompt_request.schema_id)
    if not schema:
        raise HTTPException(status_code=404, detail=f"Schema not found: {prompt_request.schema_id}")

    # Create prompt data
    prompt_data = {
        "name": prompt_request.name,
        "description": prompt_request.description,
        "content": prompt_request.content,
        "schema_id": prompt_request.schema_id
    }

    # Save prompt
    if not PROMPTS_DIR.exists():
        PROMPTS_DIR.mkdir(parents=True)

    # 使用JSON路径保存
    with open(json_path, "w", encoding="utf-8") as f:
        json.dump(prompt_data, f, indent=2, ensure_ascii=False)

    return _prompt_to_response(prompt_id, prompt_data)


@router.put("/{prompt_id:path}", response_model=PromptResponse)
async def update_prompt(
    prompt_request: PromptUpdateRequest,
    prompt_id: str = PathParam(...)
):
    """Update a prompt."""
    # 查找提示词文件
    prompt_path = None

    # 首先尝试.json文件
    json_path = PROMPTS_DIR / f"{prompt_id}.json"
    if json_path.exists():
        prompt_path = json_path

    # 如果没找到，尝试在子目录中查找.json文件
    if not prompt_path:
        for file_path in PROMPTS_DIR.glob(f"**/{prompt_id}.json"):
            prompt_path = file_path
            break

    # 如果没找到.json文件，尝试.txt文件
    if not prompt_path:
        txt_path = PROMPTS_DIR / f"{prompt_id}.txt"
        if txt_path.exists():
            prompt_path = txt_path

    # 如果没找到，尝试在子目录中查找.txt文件
    if not prompt_path:
        for file_path in PROMPTS_DIR.glob(f"**/{prompt_id}.txt"):
            prompt_path = file_path
            break

    if not prompt_path:
        raise HTTPException(status_code=404, detail=f"Prompt not found: {prompt_id}")

    # 如果是.txt文件，将其转换为.json文件
    if prompt_path.suffix == ".txt":
        with open(prompt_path, "r", encoding="utf-8") as f:
            content = f.read()

        # 创建JSON数据
        prompt_data = {
            "name": prompt_id,
            "description": f"提示词模板: {prompt_id}",
            "content": content,
            "schema_id": ""
        }

        # 创建新的JSON路径
        json_path = PROMPTS_DIR / f"{prompt_id}.json"
    else:
        # 加载现有的JSON数据
        with open(prompt_path, "r", encoding="utf-8") as f:
            prompt_data = json.load(f)
        json_path = prompt_path

    # Update prompt properties
    if prompt_request.name is not None:
        prompt_data["name"] = prompt_request.name

    if prompt_request.description is not None:
        prompt_data["description"] = prompt_request.description

    if prompt_request.content is not None:
        prompt_data["content"] = prompt_request.content

    if prompt_request.schema_id is not None:
        # Check if schema exists
        schema = Schema.get_by_name(prompt_request.schema_id)
        if not schema:
            raise HTTPException(status_code=404, detail=f"Schema not found: {prompt_request.schema_id}")

        prompt_data["schema_id"] = prompt_request.schema_id

    # Save prompt
    with open(json_path, "w", encoding="utf-8") as f:
        json.dump(prompt_data, f, indent=2, ensure_ascii=False)

    return _prompt_to_response(prompt_id, prompt_data)


@router.delete("/{prompt_id:path}")
async def delete_prompt(prompt_id: str = PathParam(...)):
    """Delete a prompt."""
    # 查找提示词文件
    prompt_path = None

    # 首先尝试.json文件
    json_path = PROMPTS_DIR / f"{prompt_id}.json"
    if json_path.exists():
        prompt_path = json_path

    # 如果没找到，尝试在子目录中查找.json文件
    if not prompt_path:
        for file_path in PROMPTS_DIR.glob(f"**/{prompt_id}.json"):
            prompt_path = file_path
            break

    # 如果没找到.json文件，尝试.txt文件
    if not prompt_path:
        txt_path = PROMPTS_DIR / f"{prompt_id}.txt"
        if txt_path.exists():
            prompt_path = txt_path

    # 如果没找到，尝试在子目录中查找.txt文件
    if not prompt_path:
        for file_path in PROMPTS_DIR.glob(f"**/{prompt_id}.txt"):
            prompt_path = file_path
            break

    if not prompt_path:
        raise HTTPException(status_code=404, detail=f"Prompt not found: {prompt_id}")

    # Delete the prompt file
    prompt_path.unlink()

    return {"message": f"Prompt deleted: {prompt_id}"}
