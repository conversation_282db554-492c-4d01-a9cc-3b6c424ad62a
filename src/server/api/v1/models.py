"""API models for XExtract."""
from datetime import datetime
from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field


class SchemaFieldResponse(BaseModel):
    """Schema field response model."""
    name: str
    type: str
    description: Optional[str] = None
    required: bool = False
    array: bool = False
    extraction_method: str
    regex_pattern: Optional[str] = None
    regex_group: int = 0
    prompt_hint: Optional[str] = None
    format: Optional[str] = None
    pre_processors: Optional[List[str]] = None
    post_processors: Optional[List[str]] = None


class SchemaResponse(BaseModel):
    """Schema response model."""
    id: str
    name: str
    description: Optional[str] = None
    version: str
    fields: List[SchemaFieldResponse]
    metadata: Dict[str, Any] = Field(default_factory=dict)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class SchemaListResponse(BaseModel):
    """Schema list response model."""
    items: List[SchemaResponse]
    total: int


class SchemaFieldRequest(BaseModel):
    """Schema field request model."""
    name: str
    type: str
    description: Optional[str] = None
    required: bool = False
    array: bool = False
    extraction_method: str
    regex_pattern: Optional[str] = None
    regex_group: int = 0
    prompt_hint: Optional[str] = None
    format: Optional[str] = None
    pre_processors: Optional[List[str]] = None
    post_processors: Optional[List[str]] = None


class SchemaCreateRequest(BaseModel):
    """Schema create request model."""
    id: str
    name: str
    description: Optional[str] = None
    version: str = "1.0.0"
    fields: List[SchemaFieldRequest]
    metadata: Dict[str, Any] = Field(default_factory=dict)


class SchemaUpdateRequest(BaseModel):
    """Schema update request model."""
    name: Optional[str] = None
    description: Optional[str] = None
    version: Optional[str] = None
    fields: Optional[List[SchemaFieldRequest]] = None
    metadata: Optional[Dict[str, Any]] = None


class PromptResponse(BaseModel):
    """Prompt response model."""
    id: str
    name: str
    description: Optional[str] = None
    content: str
    schema_id: str
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class PromptListResponse(BaseModel):
    """Prompt list response model."""
    items: List[PromptResponse]
    total: int


class PromptCreateRequest(BaseModel):
    """Prompt create request model."""
    id: str
    name: str
    description: Optional[str] = None
    content: str
    schema_id: str


class PromptUpdateRequest(BaseModel):
    """Prompt update request model."""
    name: Optional[str] = None
    description: Optional[str] = None
    content: Optional[str] = None
    schema_id: Optional[str] = None


class ExtractionMetadata(BaseModel):
    """Extraction metadata model."""
    confidence: float = 0.0
    processing_time: float = 0.0
    source: str
    file_type: Optional[str] = None
    page_count: Optional[int] = None
    file_size: Optional[int] = None
    extraction_time: datetime


class ExtractionResponse(BaseModel):
    """Extraction response model."""
    id: str
    schema_id: str
    schema_name: str
    file_name: Optional[str] = None
    extraction_time: datetime
    status: str
    data: Dict[str, Any]
    metadata: ExtractionMetadata


class ExtractionListResponse(BaseModel):
    """Extraction list response model."""
    items: List[ExtractionResponse]
    total: int


class LLMSettings(BaseModel):
    """LLM settings model."""
    model: str
    temperature: float = 0.3
    max_tokens: int = 1000


class TextExtractionRequest(BaseModel):
    """Text extraction request model."""
    text: str
    schema_id: str
    prompt_id: Optional[str] = None
    llm: Optional[LLMSettings] = None
