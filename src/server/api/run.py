#!/usr/bin/env python
"""Run the XExtract API server.

This module is kept for backward compatibility.
For new code, use the unified entry point at src/main.py.
"""
# Standard library imports
import sys
import argparse
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# Import the server function from the main entry point
from src.main import run_server

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("xextract.api.run")


def main():
    """Run the XExtract API server."""
    parser = argparse.ArgumentParser(description="Run the XExtract API server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")

    args = parser.parse_args()

    logger.info(f"Starting XExtract API server on {args.host}:{args.port}")
    logger.info("Note: This script is deprecated. Please use 'python src/main.py server' instead.")

    # Call the server function from the main entry point
    return run_server(args)


if __name__ == "__main__":
    sys.exit(main())
