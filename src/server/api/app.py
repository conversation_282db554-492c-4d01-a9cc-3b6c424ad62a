"""FastAPI application for XExtract."""
import logging
import datetime
from pathlib import Path

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles

# Add the project root to the Python path
import sys
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 使用配置对象
from config.config import config
from src.xextract.utils.version import get_git_info
from src.xextract.utils.time_utils import get_uptime_info
# 导入路由器
from src.server.api.v1 import api_v1_router

# Configure logging
from src.xextract.utils.logging_config import configure_logging, get_logger
configure_logging(level=logging.INFO)
logger = get_logger("xextract.api")
logger.info("Starting XExtract API application")

# Create FastAPI app
app = FastAPI(
    title="XExtract API",
    description="API for extracting structured data from documents",
    version="0.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(api_v1_router)

# API endpoints
@app.get("/api/health")
async def health_check():
    """Health check endpoint."""
    # Get service start time
    start_time = getattr(app, "start_time", None)
    if start_time is None:
        # Set start time if not already set
        start_time = datetime.datetime.now().isoformat()
        setattr(app, "start_time", start_time)

    # Get git version info using utility function
    git_info = get_git_info()
    git_version = {
        "commit": git_info.get("commit_hash", "unknown"),
        "branch": git_info.get("branch", "unknown"),
        "dirty": git_info.get("dirty", False)
    }

    # Calculate and format uptime using utility function
    uptime_seconds, uptime_display = get_uptime_info(start_time)
    return {
        "status": "ok",
        "version": config.VERSION,
        "start_time": start_time,
        "git_version": git_version,
        "uptime_seconds": uptime_seconds,
        "uptime": uptime_display
    }

@app.get("/api/config")
async def get_config():
    """Get API configuration."""
    # 使用全局配置对象
    return {
        "app_name": config.APP_NAME,
        "version": config.VERSION,
        "llm": {
            "provider": config.llm["provider"],
            "model": config.llm["model"],
        },
        "document_parser": {
            "supported_formats": ["pdf", "docx", "doc", "txt", "md"],
        }
    }

# Serve static files (web frontend)
web_dir = Path(__file__).parent.parent.parent.parent / "web"
if web_dir.exists():
    app.mount("/", StaticFiles(directory=str(web_dir), html=True), name="web")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
